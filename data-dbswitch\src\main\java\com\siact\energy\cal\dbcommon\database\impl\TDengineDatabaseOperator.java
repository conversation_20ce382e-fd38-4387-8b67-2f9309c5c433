// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbcommon.database.impl;

import com.siact.energy.cal.dbcommon.constant.Constants;
import com.siact.energy.cal.dbcommon.database.AbstractDatabaseOperator;
import com.siact.energy.cal.dbcommon.database.IDatabaseOperator;
import com.siact.energy.cal.dbcommon.domain.StatementResultSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.util.List;

@Slf4j
public class TDengineDatabaseOperator extends AbstractDatabaseOperator implements IDatabaseOperator {

  public TDengineDatabaseOperator(DataSource dataSource) {
    super(dataSource);
  }

  @Override
  public DataSource getDataSource() {
    return this.dataSource;
  }

  @Override
  public int getFetchSize() {
    return this.fetchSize;
  }

  @Override
  public void setFetchSize(int size) {
    if (size < Constants.MINIMUM_FETCH_SIZE) {
      throw new IllegalArgumentException(
              "设置的批量处理行数的大小fetchSize不得小于" + Constants.MINIMUM_FETCH_SIZE);
    }

    this.fetchSize = size;
  }


  @Override
  public String getSelectTableSql(String schemaName, String tableName, List<String> fields) {
    return null;
  }

  @Override
  public StatementResultSet queryTableData(String schemaName, String tableName, List<String> fields, List<String> orders) {
    return null;
  }

  @Override
  public StatementResultSet queryTableData(String schemaName, String tableName, List<String> fields) {
    String sql = String.format("select tbname,%s from %s.%s order by ts desc limit 4305",
            StringUtils.join(fields, ","),
            schemaName, tableName);
    return selectTableTDData(sql);
  }

  public StatementResultSet selectTableTDData(String sql) {
    if (log.isDebugEnabled()) {
      log.debug("Query table data sql :{}", sql);
    }
    log.info("Query table data sql :{}", sql);
    try {
      StatementResultSet srs = new StatementResultSet();
      srs.setConnection(dataSource.getConnection());
      srs.setAutoCommit(srs.getConnection().getAutoCommit());
      srs.getConnection().setAutoCommit(false);
      srs.setStatement(srs.getConnection()
              .createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY));
      srs.getStatement().setQueryTimeout(Constants.DEFAULT_QUERY_TIMEOUT_SECONDS);
      srs.getStatement().setFetchSize(this.fetchSize*5);
      srs.setResultset(srs.getStatement().executeQuery(sql));
      return srs;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
  @Override
  public void truncateTableData(String schemaName, String tableName) {

  }

  @Override
  public void dropTable(String schemaName, String tableName) {
    String sql = String.format("DROP TABLE IF EXISTS \"%s\".\"%s\" ", schemaName, tableName);
    this.executeSql(sql);
  }
}
