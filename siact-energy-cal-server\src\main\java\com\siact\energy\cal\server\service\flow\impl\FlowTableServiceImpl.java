package com.siact.energy.cal.server.service.flow.impl;

import com.siact.energy.cal.server.dao.flow.FlowTableMapper;
import com.siact.energy.cal.server.service.flow.IFlowTableService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-11
 * @Description: 数据表实现类
 * @Version: 1.0
 */
@Service
public class FlowTableServiceImpl implements IFlowTableService {
    @Resource
    private FlowTableMapper flowTableMapper;
    @Override
    public int getTableCount(String tableName) {
        return flowTableMapper.getTableCount(tableName);
    }
}
