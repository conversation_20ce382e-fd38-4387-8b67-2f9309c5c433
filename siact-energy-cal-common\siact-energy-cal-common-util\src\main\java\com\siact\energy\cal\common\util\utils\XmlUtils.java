package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.util.XmlUtil;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.net.URL;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-31
 * @Description: xml操作工具类
 * @Version: 1.0
 */
public class XmlUtils {

    public static void addXmlNode(String xmlPath, String node, String value){
        Document document = XmlUtil.readXML(xmlPath);
        Element rootElement = XmlUtil.getRootElement(document);
        //创建一个标签
        Element elementItem = document.createElement("chain");
        elementItem.setAttribute("name", node);
        elementItem.setTextContent(value);
        rootElement.appendChild(elementItem);
        XmlUtil.toFile(document, xmlPath);
    }

    public static void updateXmlNode(String xmlPath, String node, String value){
        boolean isNodeExist = true;
        Document document = XmlUtil.readXML(xmlPath);
        Element rootElement = XmlUtil.getRootElement(document);
        NodeList nodeList = rootElement.getElementsByTagName("chain");
        for (int i = 0; i <nodeList.getLength() ; i++) {
            if (nodeList.item(i).getAttributes().getNamedItem("name") != null){
                if (node.equals(nodeList.item(i).getAttributes().getNamedItem("name").getNodeValue())){
                    nodeList.item(i).setTextContent(value);
                    isNodeExist = false;
                }
            }
        }
        if (isNodeExist){
            //创建一个标签
            Element elementItem = document.createElement("chain");
            elementItem.setAttribute("name", node);
            elementItem.setTextContent(value);
            rootElement.appendChild(elementItem);
        }
        XmlUtil.toFile(document, xmlPath);

    }

    public static String getXmlPath(){
        return System.getProperty("user.dir") + "/config/flow.el.xml";
    }

    public static void main(String[] args) {
        //updateXmlNode(getXmlPath(), 97+"", null);
    }


    public static boolean isNodeExist(NodeList nodeList, String nodeName) {
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getNodeName().equals(nodeName)) {
                return true;
            }
        }
        return false;
    }
}
