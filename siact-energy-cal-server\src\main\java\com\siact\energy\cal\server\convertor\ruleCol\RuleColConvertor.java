package com.siact.energy.cal.server.convertor.ruleCol;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.siact.energy.cal.server.entity.ruleCol.RuleCol;
import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColUpdateDTO;

/**
 * 指标集表(RuleCol) Convertor
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:13
 */
@Mapper
public interface RuleColConvertor extends AbstractConvertor<RuleColVO, RuleCol> {

    RuleColConvertor INSTANCE = Mappers.getMapper(RuleColConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<RuleColVO> entityPage2VoPageBean(Page<RuleCol> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<RuleCol> voPageBean2EntityPage(PageBean<RuleColVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleCol queryDTO2Entity(RuleColQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleCol insertDTO2Entity(RuleColInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleCol updateDTO2Entity(RuleColUpdateDTO dto);

}
