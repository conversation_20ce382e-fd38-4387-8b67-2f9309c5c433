package com.siact.energy.cal.common.util.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ClassUtil
 *
 * <AUTHOR>
 * @since 2024-05-13 16:08:47
 */
public class ClassUtil {

    private ClassUtil() {}

    /**
     * 获取类自己的属性名称列表
     *
     * @param clz 类信息
     * @return 属性名称集合
     */
    public static List<String> getClassOwnFields(Class clz) {

        List<String> fieldNameList = new ArrayList<>();

        Field[] fields = clz.getDeclaredFields();
        for(Field fd : fields) {

            if(!fd.isAccessible()) {
                fd.setAccessible(true);
            }
            fieldNameList.add(fd.getName());
        }

        return fieldNameList;
    }

    /**
     * 获取类的属性名称列表（包含父类）
     *
     * @param clz 类信息
     * @return 属性名称集合
     */
    public static List<String> getClassAllFields(Class clz) {

        List<String> fieldNameList = new ArrayList<>();

        recursionGetClassAllFields(clz, fieldNameList);

        return fieldNameList;

    }

    /**
     * 递归获取类以及父类的属性
     *
     * @param clz 类定义
     * @param fieldNameList 属性存放集合
     */
    private static void recursionGetClassAllFields(Class clz, List<String> fieldNameList) {

        if (Objects.nonNull(clz) && clz != Objects.class) {
            Field[] fields = clz.getDeclaredFields();
            for(Field fd : fields) {

                if(!fd.isAccessible()) {
                    fd.setAccessible(true);
                }
                fieldNameList.add(fd.getName());
            }
            clz = clz.getSuperclass();
            recursionGetClassAllFields(clz, fieldNameList);
        }

    }

    public static <T> T getObjByClass(Class<T> clazz) {
        T t = null;
        try {
            t = clazz.getConstructor().newInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;
    }
}
