<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.xxlJob.XxlJobInfoDao">

	<resultMap id="XxlJobInfo" type="com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo" >
		<result column="id" property="id" />

		<result column="job_group" property="jobGroup" />
	    <result column="job_desc" property="jobDesc" />

	    <result column="add_time" property="addTime" />
	    <result column="update_time" property="updateTime" />

	    <result column="author" property="author" />
	    <result column="alarm_email" property="alarmEmail" />

		<result column="schedule_type" property="scheduleType" />
		<result column="schedule_conf" property="scheduleConf" />
		<result column="misfire_strategy" property="misfireStrategy" />

		<result column="executor_route_strategy" property="executorRouteStrategy" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
		<result column="executor_block_strategy" property="executorBlockStrategy" />
		<result column="executor_timeout" property="executorTimeout" />
		<result column="executor_fail_retry_count" property="executorFailRetryCount" />

	    <result column="glue_type" property="glueType" />
	    <result column="glue_source" property="glueSource" />
	    <result column="glue_remark" property="glueRemark" />
		<result column="glue_updatetime" property="glueUpdatetime" />

		<result column="child_jobid" property="childJobId" />

		<result column="trigger_status" property="triggerStatus" />
		<result column="trigger_last_time" property="triggerLastTime" />
		<result column="trigger_next_time" property="triggerNextTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.author,
		t.alarm_email,
		t.schedule_type,
		t.schedule_conf,
		t.misfire_strategy,
		t.executor_route_strategy,
		t.executor_handler,
		t.executor_param,
		t.executor_block_strategy,
		t.executor_timeout,
		t.executor_fail_retry_count,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.glue_updatetime,
		t.child_jobid,
		t.trigger_status,
		t.trigger_last_time,
		t.trigger_next_time
	</sql>

	<select id="pageList" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
            <if test="triggerStatus gte 0">
                AND t.trigger_status = #{triggerStatus}
            </if>
			<if test="jobDesc != null and jobDesc != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.job_desc like ('%' || #{jobDesc} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
					</otherwise>
				</choose>
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.executor_handler like ('%' || #{executorHandler} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
					</otherwise>
				</choose>
			</if>
			<if test="author != null and author != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.author like ('%' || #{author} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.author like CONCAT(CONCAT('%', #{author}), '%')
					</otherwise>
				</choose>
			</if>
		</trim>
		ORDER BY id DESC
		<choose>
			<!-- PostgreSQL数据库（包括南大通用） -->
			<when test="_databaseId == 'postgresql'">
				LIMIT #{pagesize} OFFSET #{offset}
			</when>
			<!-- 默认使用MySQL语法 -->
			<otherwise>
				LIMIT #{offset}, #{pagesize}
			</otherwise>
		</choose>
	</select>

	<select id="findAll" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		ORDER BY id DESC
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM xxl_job_info AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
            <if test="triggerStatus gte 0">
                AND t.trigger_status = #{triggerStatus}
            </if>
			<if test="jobDesc != null and jobDesc != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.job_desc like ('%' || #{jobDesc} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.job_desc like CONCAT(CONCAT('%', #{jobDesc}), '%')
					</otherwise>
				</choose>
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.executor_handler like ('%' || #{executorHandler} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
					</otherwise>
				</choose>
			</if>
			<if test="author != null and author != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.author like ('%' || #{author} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.author like CONCAT(CONCAT('%', #{author}), '%')
					</otherwise>
				</choose>
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_info (
			job_group,
			job_desc,
			add_time,
			update_time,
			author,
			alarm_email,
			schedule_type,
			schedule_conf,
			misfire_strategy,
            executor_route_strategy,
			executor_handler,
			executor_param,
			executor_block_strategy,
			executor_timeout,
			executor_fail_retry_count,
			glue_type,
			glue_source,
			glue_remark,
			glue_updatetime,
			child_jobid,
			trigger_status,
			trigger_last_time,
			trigger_next_time
		) VALUES (
			#{jobGroup},
			#{jobDesc},
			#{addTime},
			#{updateTime},
			#{author},
			#{alarmEmail},
			#{scheduleType},
			#{scheduleConf},
			#{misfireStrategy},
			#{executorRouteStrategy},
			#{executorHandler},
			#{executorParam},
			#{executorBlockStrategy},
			#{executorTimeout},
			#{executorFailRetryCount},
			#{glueType},
			#{glueSource},
			#{glueRemark},
			#{glueUpdatetime},
			#{childJobId},
			#{triggerStatus},
			#{triggerLastTime},
			#{triggerNextTime}
		);
		<!--<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID()
			/*SELECT @@IDENTITY AS id*/
		</selectKey>-->
	</insert>

	<select id="loadById" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		WHERE t.id = #{id}
	</select>

	<update id="update" parameterType="com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo" >
		UPDATE xxl_job_info
		SET
			job_group = #{jobGroup},
			job_desc = #{jobDesc},
			update_time = #{updateTime},
			author = #{author},
			alarm_email = #{alarmEmail},
			schedule_type = #{scheduleType},
			schedule_conf = #{scheduleConf},
			misfire_strategy = #{misfireStrategy},
			executor_route_strategy = #{executorRouteStrategy},
			executor_handler = #{executorHandler},
			executor_param = #{executorParam},
			executor_block_strategy = #{executorBlockStrategy},
			executor_timeout = ${executorTimeout},
			executor_fail_retry_count = ${executorFailRetryCount},
			glue_type = #{glueType},
			glue_source = #{glueSource},
			glue_remark = #{glueRemark},
			glue_updatetime = #{glueUpdatetime},
			child_jobid = #{childJobId},
			trigger_status = #{triggerStatus},
			trigger_last_time = #{triggerLastTime},
			trigger_next_time = #{triggerNextTime}
		WHERE id = #{id}
	</update>

	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM xxl_job_info
		WHERE id = #{id}
	</delete>

	<delete id="deleteBatch" parameterType="java.util.HashMap">
		DELETE
		FROM xxl_job_info
		WHERE id in
		<foreach item="id" collection="ids" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<select id="getJobsByGroup" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		WHERE t.job_group = #{jobGroup}
	</select>

	<select id="findAllCount" resultType="int">
		SELECT count(1)
		FROM xxl_job_info
	</select>


	<select id="scheduleJobQuery" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_info AS t
		WHERE t.trigger_status = 1
			and t.trigger_next_time <![CDATA[ <= ]]> #{maxNextTime}
		ORDER BY id ASC
		LIMIT #{pagesize}
	</select>

	<update id="scheduleUpdate" parameterType="com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo"  >
		UPDATE xxl_job_info
		SET
			trigger_last_time = #{triggerLastTime},
			trigger_next_time = #{triggerNextTime},
			trigger_status = #{triggerStatus}
		WHERE id = #{id}
	</update>

</mapper>