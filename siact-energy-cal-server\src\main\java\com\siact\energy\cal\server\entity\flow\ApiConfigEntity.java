package com.siact.energy.cal.server.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 接口表实体类
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@TableName("si_api_config_t")
public class ApiConfigEntity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * api名称
     */
    private String apiName;

    /**
     * api地址
     */
    private String apiUrl;

    /**
     * 请求类型
     */
    private String requestType;



    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 接口描述
     */
    private String remark;

    /**
     * 绑定组件流id	
     */
    private Long flowId;

    /**
     * 输出表id	
     */
    private String tableId;


}
