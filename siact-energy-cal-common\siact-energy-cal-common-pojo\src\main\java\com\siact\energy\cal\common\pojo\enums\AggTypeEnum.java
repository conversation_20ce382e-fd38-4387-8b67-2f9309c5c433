package com.siact.energy.cal.common.pojo.enums;


/**
 * @Package com.siact.energy.cal.common.pojo.enums
 * @description:
 * <AUTHOR>
 * @create 2024/10/8 10:39
 */


public enum AggTypeEnum {


    /**
     * 平均值
     */
    AVG("AVG", "平均值"),

    /**
     * 最大值
     */
    MAX("MAX", "最大值"),
    /**
     * 最新值
     */
    LAST("LAST", "最新值"),
    /**
     * 最小值
     */
    FIRST("FIRST", "最早值"),
    /**
     * 最小值
     */
    DIFF("DIFF", "累加值");


    public static final String TIPS = "计算类型，AVG:均值;MAX:最大值;MIN:最小值;LAST:最新值;FIRST:最早值;SUM:累加;DIFF:差值";

    private final String value;

    private final String description;

    AggTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
