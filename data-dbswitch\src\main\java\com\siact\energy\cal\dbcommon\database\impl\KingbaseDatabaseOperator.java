// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbcommon.database.impl;

/**
 * Kingbase8数据库实现类
 *
 * <AUTHOR>
 */

import javax.sql.DataSource;

public class KingbaseDatabaseOperator extends PostgreSqlDatabaseOperator {

  public KingbaseDatabaseOperator(DataSource dataSource) {
    super(dataSource);
  }

}
