package com.siact.energy.cal.common.pojo.dto.dataSource;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * 数据源表(DataSource) 新增DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:31
 */
@ApiModel("数据源表新增DTO")
@Data
public class DataSourceInsertDTO {

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称", required = true, position = 1)
    @NotBlank(message = "数据源名称不能为空")
    private String databaseName;

    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", required = true, position = 2)
    @NotBlank(message = "数据源类型不能为空")
    private Integer dbType;
    /**
     * 数据源ip
     */
    @ApiModelProperty(value = "数据源ip", required = true, position = 3)
    @NotBlank(message = "数据源ip不能为空")
    private String databaseIp;

    /**
     * 数据源端口
     */
    @ApiModelProperty(value = "数据源端口", required = true, position = 4)
    @Pattern(message = "数据源端口格式不正确", regexp = "^(0|[1-9]\\d{0,4}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-3]\\d|6553[0-5])$")
    private String databasePort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称", required = true, position = 5)
    @NotBlank(message = "数据库名称不能为空")
    private String db;

    /**
     * 超级表名
     */
    @ApiModelProperty(value = "超级表名", required = true, position = 6)
    @NotBlank(message = "超级表名不能为空")
    private String tableName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true, position = 7)
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true, position = 8)
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 数据库连接串
     */
    @ApiModelProperty(value = "数据库连接串", position = 9)
    private String jdbcUrl;

    /**
     * 项目id,如果是数仓数据库，id为空
     */
    @ApiModelProperty(value = "项目id，编辑的时候不能更换项目ID", required = true, position = 10)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 数据源描述
     */
    @ApiModelProperty(value = "数据源描述", position = 11)
    @Length(max = 1000, message = "数据源描述长度不超过{max}")
    private String description;

}

