package com.siact.energy.cal.server.common.datasource.db.impl.tdengine;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractSqlBuilder;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("tdengineSqlBuilder")
public class TDengineSqlBuilder extends AbstractSqlBuilder {

    @Override
    public String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo) {

        List<String> devpropertyList = queryDTO.getDataCodes();
        String devpropListStr = devpropertyList.stream()
                .map(value -> "'" + value + "'") // 将每个元素用单引号括起来
                .collect(Collectors.joining(","));
        String sql = "";
        if (!queryDTO.isEquallySpacedQuery()) {
            sql = String.format("SELECT ts,first(itemvalue) as itemvalue FROM %s WHERE devproperty in (%s) and ts >= '%s' AND ts <= '%s' group by devproperty union all " +
                            "SELECT ts,last(itemvalue) as itemvalue FROM %s WHERE devproperty in (%s) and ts >= '%s' AND ts <= '%s' group by devproperty;",
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime(),
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime());
        } else {
            // 拼凑步长
            String intervalStr = queryDTO.getInterval() + queryDTO.getTsUnit();
            sql = String.format("SELECT first(itemvalue) as itemvalue FROM %s WHERE devproperty in (%s) and ts >= '%s' AND ts <= '%s' interval(%s) FILL(NULL) group by devproperty;",
                    dataSourceVo.getTableName(),
                    devpropListStr,
                    queryDTO.getStartTime(),
                    queryDTO.getEndTime(),
                    intervalStr);
        }
        return sql;
    }

    @Override
    public String buildTimeRangeAggSql(String function,
                                       Map<String, String> propMapping,
                                       TimeQueryDTO queryDTO,
                                       String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // SQL模板
        String sqlTemplate = "SELECT first(ts) as ts,%s(itemvalue) as itemvalue " +
                "FROM %s " +
                "WHERE devproperty in (%s) " +
                "and ts >= '%s' " +
                "AND ts <= '%s' " +
                "group by devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime());
    }

    @Override
    public String buildIntervalAggSql(String function,
                                    Map<String, String> propMapping,
                                    TimeQueryDTO queryDTO,
                                    String tableName) {
        // 将设备属性列表转换为SQL IN子句格式
        String devpropListStr = propMapping.keySet().stream()
                .map(value -> "'" + value + "'")
                .collect(Collectors.joining(","));

        // 构建时间间隔
        String intervalUnit = queryDTO.getInterval() + queryDTO.getTsUnit();

        // SQL模板
        String sqlTemplate = "SELECT first(ts) as ts,%s(itemvalue) as itemvalue " +
                "FROM %s " +
                "WHERE devproperty in (%s) " +
                "and ts >= '%s' " +
                "AND ts <= '%s' " +
                "interval(%s) FILL(NULL) " +
                "group by devproperty";

        Set<String> supportedFunctions = new HashSet<>(Arrays.asList("sum", "avg", "max", "min", "last", "first"));
        if (!supportedFunctions.contains(function)) {
            throw new BizException("不支持的聚合函数: " + function);
        }

        return String.format(sqlTemplate,
                function,
                tableName,
                devpropListStr,
                queryDTO.getStartTime(),
                queryDTO.getEndTime(),
                intervalUnit);
    }

    @Override
    public String buildTimeSliceDataSql(String startTime, String endTime, List<String> dataCOdes, String tableName) {
        String devpropListStr = dataCOdes.stream()
                .map(value -> "'" + value + "'") // 将每个元素用单引号括起来
                .collect(Collectors.joining(","));

        String sql = String.format("SELECT ts, last(itemvalue) as itemvalue FROM %s WHERE devproperty in (%s) and ts >= '%s' AND ts <= '%s' group by devproperty;",
                tableName,
                devpropListStr,
                startTime,
                endTime);
        return sql;
    }

    @Override
    public String buildDiffSql(Map<String, String> propMapping, String tableName, String startTime, String endTime, String interval, String unit) {
        return null;
    }
}