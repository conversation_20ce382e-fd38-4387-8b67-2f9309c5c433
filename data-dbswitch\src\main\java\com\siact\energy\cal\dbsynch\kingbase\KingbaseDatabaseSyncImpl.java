// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbsynch.kingbase;

import com.siact.energy.cal.dbsynch.pgsql.PostgresqlDatabaseSyncImpl;

import javax.sql.DataSource;

/**
 * kingbase8数据库DML同步实现类
 *
 * <AUTHOR>
 */
public class KingbaseDatabaseSyncImpl extends PostgresqlDatabaseSyncImpl {

  public KingbaseDatabaseSyncImpl(DataSource ds) {
    super(ds);
  }

}
