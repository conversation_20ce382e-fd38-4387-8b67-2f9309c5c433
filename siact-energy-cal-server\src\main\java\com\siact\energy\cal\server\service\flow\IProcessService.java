package com.siact.energy.cal.server.service.flow;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ComponentFlowDto;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.vo.flow.ComFlowNameVo;
import com.siact.energy.cal.common.pojo.vo.flow.ComponentFlowVO;
import com.siact.energy.cal.common.pojo.vo.flow.FlowRunResultVO;
import com.siact.energy.cal.server.common.flow.logicflow.LogicFlow;
import com.siact.energy.cal.server.entity.flow.ComponentFlowEntity;

import java.util.List;

public interface IProcessService extends IService<ComponentFlowEntity> {

    /**
     * 保存组件流
     * @param logicFlow 组件流对象
     * @return 1-成功
     */
    int saveComponent(LogicFlow logicFlow);

    /**
     * 运行组件流
     * @param id 组件流id
     * @param paramList 参数列表
     * @return 组件流运行结果
     */
    FlowRunResultVO runFlow(String id, List<ParamDto> paramList);




    /**
     * 获取组件流内容-画布内容
     * @param flowId 组件流id
     * @return 组件流内容
     */
    String getFlowContent(String flowId);

    PageBean<ComponentFlowVO> list(Page<ComponentFlowEntity> page, ComponentFlowDto componentFlowDto);

    /**
     * 批量删除组件流
     * @param ids 组件流id的集合
     * @return true-成功，false-失败
     */
    Boolean deleteFlow(List<Long> ids);

    /**
     * 复制组件流
     * @param componentFlowDto 组件流对象
     * @return 新的组件流id
     */
    Integer copy(ComponentFlowDto componentFlowDto);

    /**
     * 获取未删除所有的组件流
     * @return 所有组件流
     */
    List<ComFlowNameVo> getComFlowList();

    /**
     * 校验组件流名称是否存在
     * @param flowName 组件流名称
     * @return true-存在，false-不存在
     */
    Boolean check(String flowName);

    /**
     * 更新组件流状态
     * @param flowId 组件流id
     * @param status 状态
     */
    void updateComponentStatus(String flowId, Integer status);
}
