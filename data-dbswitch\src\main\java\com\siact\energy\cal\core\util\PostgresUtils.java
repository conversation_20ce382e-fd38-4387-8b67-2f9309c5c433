package com.siact.energy.cal.core.util;

import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.core.database.AbstractDatabase;
import com.siact.energy.cal.core.database.DatabaseFactory;
import com.siact.energy.cal.core.model.ColumnDescription;

import java.sql.Connection;
import java.util.List;

public final class PostgresUtils {

  public static String getTableDDL(Connection connection, String schema, String table) {
    AbstractDatabase db = DatabaseFactory.getDatabaseInstance(ProductTypeEnum.POSTGRESQL);
    List<ColumnDescription> columnDescriptions = db.queryTableColumnMeta(connection, schema, table);
    List<String> pks = db.queryTablePrimaryKeys(connection, schema, table);
    return GenerateSqlUtils.getDDLCreateTableSQL(
        db.getDatabaseType(), columnDescriptions, pks, schema, table, false);
  }

  private PostgresUtils() {
	  throw new IllegalStateException();
  }
}
