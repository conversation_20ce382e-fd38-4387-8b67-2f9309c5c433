package com.siact.energy.cal.tool.dao.dataTrans;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.tool.entity.dataTrans.DataTrans;

/**
 * 数据传输表（mqtt）(DataTrans)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:36
 */
@Repository
public interface DataTransDao extends BaseMapper<DataTrans> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DataTrans> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<DataTrans> entities);

}

