package com.siact.energy.cal.pgwriter.pgsql.constants;

public enum DataType {

  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>a,
  <PERSON>r,
  Int8,
  Int2,
  Int4,
  Text,
  Jsonb,
  SinglePrecision,
  DoublePrecision,
  Cash,
  Money,
  MacAddress,
  Inet4,
  Inet6,
  Cidr,
  Unknown,
  Date,
  Timestamp,
  Uuid,
  Point,
  Box,
  Line,
  LineSegment,
  Circle,
  Path,
  Polygon,
  Hstore,
  VarChar,
  Xml,
  Name,
  Oid,
  Tid,
  Xid,
  Cid,
  AbsTime,
  RelTime,
  TInterval,
  MacAddress8,
  CharLength,
  Time,
  TimestampTz,
  Interval,
  TimeTz,
  Bit,
  VarBit,
  Record,
  Numeric,
  TsRange,
  TsTzRange,
  Int4Range,
  Int8Range,
  NumRange,
  DateRange
}
