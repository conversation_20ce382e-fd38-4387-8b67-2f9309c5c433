package com.siact.energy.cal.dbsynch.tdengine;

import com.siact.energy.cal.dbsynch.AbstractDatabaseSynchronize;
import com.siact.energy.cal.dbsynch.IDatabaseSynchronize;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.dwenergy.datadbswitch.dbsynch.tdengine
 * @description:
 * @create 2023/9/4 14:22
 */
public class TDengineDatabaseSyncImpl extends AbstractDatabaseSynchronize implements
        IDatabaseSynchronize {
    public TDengineDatabaseSyncImpl(DataSource ds) {
        super(ds);
    }

    @Override
    public String getColumnMetaDataSql(String schemaName, String tableName) {
        return String.format("DESCRIBE \"%s\".\"%s\"", schemaName, tableName);
    }

    @Override
    public String getInsertPrepareStatementSql(String schemaName, String tableName, List<String> fieldNames) {
     return null;
    }

    @Override
    public String getInsertPrepareStatementSql2(String schemaName, String tableName, List<String> tagNames, List<String> fieldNames) {
        List<String> fieldplaceHolders = Collections.nCopies(fieldNames.size(), "?");
        List<String> tagsplaceHolders = Collections.nCopies(tagNames.size(), "?");
        return String.format("INSERT INTO \"%s\" USING \"%s\".\"%s\" TAGS ( \"%s\" ) VALUES ( %s )",
                "?",
                schemaName, tableName,
                StringUtils.join(tagsplaceHolders, "\",\""),
                StringUtils.join(fieldplaceHolders, ","));
    }

    @Override
    public String getUpdatePrepareStatementSql(String schemaName, String tableName, List<String> fieldNames, List<String> pks) {
        return null;
    }

    @Override
    public String getUpdatePrepareStatementSql2(String schemaName, String tableName, List<String> tagNames, List<String> fieldNames) {
        List<String> fieldplaceHolders = Collections.nCopies(fieldNames.size(), "?");
        List<String> tagsplaceHolders = Collections.nCopies(tagNames.size(), "?");
        return String.format("INSERT INTO \"%s\" USING \"%s\".\"%s\" TAGS ( \"%s\" ) VALUES ( %s )",
                "?",
                schemaName, tableName,
                StringUtils.join(tagsplaceHolders, "\",\""),
                StringUtils.join(fieldplaceHolders, ","));
    }
    @Override
    public String getDeletePrepareStatementSql(String schemaName, String tableName, List<String> pks) {
        return null;
    }
}
