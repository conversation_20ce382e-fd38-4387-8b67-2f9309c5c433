package com.siact.energy.cal.tool.service.feign;

import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.tool.service.feign.fallback.DatasourceServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;

@FeignClient(name = "siact-energy-cal-server",
             contextId = "datasource-client",
             url = "${service.backend.url}",
             path = "/data/source",
             fallbackFactory = DatasourceServiceFallbackFactory.class)
public interface DataSourceFeignClient {
    
    @PostMapping("/dbTest")
    R<Object> testDataSource(@RequestBody DataSourceTestDTO dto);
    @GetMapping("/dbTestById")
    R<Object> testDataSourceById(@RequestParam("id") Serializable id);
}