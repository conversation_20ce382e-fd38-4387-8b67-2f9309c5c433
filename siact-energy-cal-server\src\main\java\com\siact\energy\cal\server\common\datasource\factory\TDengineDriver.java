package com.siact.energy.cal.server.common.datasource.factory;

import com.github.alenfive.rocketapi.datasource.DataSourceDialect;
import com.github.alenfive.rocketapi.datasource.factory.JdbcDriver;
import com.github.alenfive.rocketapi.entity.DBConfig;
import com.siact.energy.cal.server.common.datasource.DataSourceTypeEnum;
import com.siact.energy.cal.server.common.datasource.TDengineDataSource;

/**
 * TDengineDriver
 *
 * <AUTHOR>
 * @since 2024-05-23 14:48:11
 */
public class TDengineDriver extends JdbcDriver {

    private final DataSourceTypeEnum dataSourceType = DataSourceTypeEnum.TDENGINE;

    @Override
    public String getName() {
        return dataSourceType.getName();
    }

    @Override
    public String getIcon() {
        return dataSourceType.getIcon();
    }

    @Override
    public String getFormat() {
        return dataSourceType.getFormat();
    }

    @Override
    public DataSourceDialect factory(DBConfig config) throws Exception {
        return new TDengineDataSource(super.getDataSource(config));
    }
}
