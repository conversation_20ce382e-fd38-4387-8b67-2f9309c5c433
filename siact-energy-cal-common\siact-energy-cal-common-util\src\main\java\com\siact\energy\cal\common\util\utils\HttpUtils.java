package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static cn.hutool.json.JSONObject.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-19
 * @Description: 远程调用工具类
 * @Version: 1.0
 */
public class HttpUtils {

    public static String doGet(String url, Map<String, Object> paramMap) {
        return HttpUtil.get(url, paramMap);
    }

    public static String doPost(String url, String body) {
        return HttpUtil.post(url, body);
    }

    public static String doPostParam(String url, String body) {
        return HttpRequest.post(url).form(body).execute().body();
    }


    public static void main(String[] args) {
//        HashMap<String, Object> param = new HashMap<>();
//        param.put("insDataCode", "PGY02001_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000");
//        List<String> list = Arrays.asList("PGY02_SPD01_STPDZ01_UBYQDY_EQ000000000_MPEPF2");
//        param.put("propModelDataCodes", list);
//        String post = HttpUtil.post("http://*************:8842/bk/siact-sec-api/common/prop/ins", JSONObject.toJSONString(param));
//        JSONObject jsonObject1 = JSONObject.parseObject(post);
//        Object o = jsonObject1.get("data");
////
//        System.out.println(jsonObject1);

//        HashMap<String, Object> param1 = new HashMap<>();
//        param1.put("dataCode", "PGY02001_SPD01001_STPDZ01001_UBYQDY007_EQPD01BYQ00001_MP0000000");
//        param1.put("hasEq", true);
//        param1.put("hasSub", true);
//        param1.put("param1", true);
//        String url = "http://*************:8842/bk/siact-sec-api/common/ins/tree";
//        String post1 = HttpUtil.get("http://*************:8842/bk/siact-sec-api/common/ins/tree", param1);
//
//        Map<String, List<String>> map = new HashMap<>();
//        JSONObject jsonObject = JSONUtil.parseObj(post1);
//        Object o = jsonObject.get("data");
//        JSONArray jsonArray = JSONUtil.parseArray(o);
//        if (!jsonArray.isEmpty()){
//            System.out.println(jsonArray);
//            for (int i = 0; i < jsonArray.size(); i++) {
//                System.out.println(jsonArray.getJSONObject(i));
//            }
//        }

       // System.out.println(post1);
//        Set<String> stringSet = new TreeSet<>();
//        getChildren("PGY02001_SPD01001_STPDZ01001_UBYQDY007_EQ000000000000_MP0000000", stringSet);
//        System.out.println(stringSet);
       // getPropIns(null, new ArrayList<>());

        String s = new String("a");
        s.intern();
        String s1 = "a";
        System.out.println(s == s1);

        String st= new String("st1")+new String("st2");
        st.intern();
        String st1= "st1st2";
        System.out.println(st==st1);

    }

    public static void getChildren(String code,  Set<String>  resultSet){
        resultSet.add(code);
        HashMap<String, Object> param = new HashMap<>();
        param.put("dataCode", code);
        param.put("hasEq", true);
        param.put("hasSub", true);
        param.put("param1", true);
        String result = HttpUtil.get("http://*************:8842/bk/siact-sec-api/common/ins/tree", param);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if ("200".equals(jsonObject.get("code"))){
            JSONArray jsonArray = JSONUtil.parseArray(jsonObject.get("data"));
            if (!jsonArray.isEmpty()){
                for (int i = 0; i < jsonArray.size(); i++) {
                    String dataCode = JSONUtil.parseObj(jsonArray.get(i)).get("dataCode").toString();
                    resultSet.add(dataCode);
                    getChildren(dataCode, resultSet);
                }
            }
        }
    }

    private static   void getPropIns(String insCode, List<String>  modelCodeList){
        insCode = "PGY02001_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000";
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> param = new HashMap<>();
        param.put("insDataCode", insCode);
        List<String> list = Arrays.asList("PGY02_SPD01_STPDZ01_UBYQDY_EQ000000000_MPEPF2");
        param.put("propModelDataCodes", list);
        String post = HttpUtil.post("http://*************:8842/bk/siact-sec-api/common/prop/ins", JSONUtil.toJsonStr(param));;

        Map<String, Set<String>> map = new HashMap<>();
        JSONObject jsonObject = JSONUtil.parseObj(post);
        Object o = jsonObject.get("data");
        if ("200".equals(jsonObject.get("code"))){
            JSONArray jsonArray = JSONUtil.parseArray(o);
            for (Object object : jsonArray) {
                String insDataCode = JSONUtil.parseObj(object).get("insDataCode").toString();
                Set<String> resultSet = new HashSet<>();
                getChildren(insDataCode, resultSet);
                map.put(insDataCode, resultSet);
            }
        }
        result.put(insCode, map);

        JSONObject jsonObject1 = JSONUtil.parseObj(result);
        System.out.println("jsonObject1 = " + jsonObject1);
    }
}
