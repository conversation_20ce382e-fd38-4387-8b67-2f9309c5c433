package com.siact.energy.cal.tool.xxljob.handler.annotation;

import java.lang.annotation.*;

/**
 * annotation for method jobhandler
 *
 * <AUTHOR> 2019-12-11 20:50:13
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface XxlJob {

    /**
     * jobhandler name
     */
    String value();

    /**
     * init handler, invoked when JobThread init
     */
    String init() default "";

    /**
     * destroy handler, invoked when JobThread destroy
     */
    String destroy() default "";

}
