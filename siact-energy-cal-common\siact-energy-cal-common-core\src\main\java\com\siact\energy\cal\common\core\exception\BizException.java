package com.siact.energy.cal.common.core.exception;


import com.siact.energy.cal.common.core.domain.RStatus;

/**
 * BizException
 *
 * <AUTHOR>
 * @since 2024-05-13 16:02:53
 */

public class BizException extends RuntimeException {

    private RStatus rStatus;

    private BizException() {
        super();
    }

    public BizException(RStatus rStatus, String... msgArgs) {
        super(String.format(rStatus.getMessage(), msgArgs));
        this.rStatus = rStatus;
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
        this.rStatus = RStatus.ERROR;
    }

    public BizException(RStatus rStatus, Throwable cause, String... msgArgs) {
        super(String.format(rStatus.getMessage(), msgArgs), cause);
        this.rStatus = rStatus;
    }

    public RStatus getRStatus() {
        return rStatus;
    }
}
