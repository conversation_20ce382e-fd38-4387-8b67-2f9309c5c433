package com.siact.energy.cal.tool.entity.ruleDetail;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.siact.energy.cal.common.datasource.common.BaseEntity;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail;
import lombok.Data;

import java.util.List;

/**
 * 指标详情表(RuleDetail)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Data
@TableName(value = "rule_detail_instance", autoResultMap = true)
public class RuleDetailInstance extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * rule_detail ID
     */
    private Long ruleDetailId;
    /**
     * 指标名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String ruleName;

    /**
     * 指标描述
     */
    @TableField(condition = SqlCondition.LIKE)
    private String ruleDes;

    /**
     * 计算模式: 0-表达式模式, 1-流程模式
     */
    @TableField("calculation_mode")
    private Integer calculationMode;

    /**
     * 关联的流程链ID (逻辑外键, 指向 si_liteflow_chain.id)
     */
    @TableField("flow_chain_id")
    private Long flowChainId;
    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    private Integer ruleType;

    /**
     * 计算类型（0-数值计算，1-逻辑计算，2-正则表达式）
     */
    private Integer calType;

    /**
     * 节点（模型）编码
     */
    @TableField(condition = SqlCondition.LIKE)
    private String devCode;

    /**
     * 节点（模型）名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String devName;

    /**
     * 属性编码，模型编码
     */
    @TableField(condition = SqlCondition.LIKE)
    private String devProperty;

    /**
     * 属性（模型）名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String propName;

    /**
     * 公式表达式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String ruleFormula;

    /**
     * 公式表达式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String ruleFormulaShow;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 所属规则集id
     */
    private Long ruleColId;

    /**
     * 是否激活（0-激活,1-未激活）
     */
    private Integer activeState;

    /**
     * 所属规则集
     */
    @TableField(exist = false)
    private String ruleColName;
    /**
     * 逻辑删除标识：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
}

