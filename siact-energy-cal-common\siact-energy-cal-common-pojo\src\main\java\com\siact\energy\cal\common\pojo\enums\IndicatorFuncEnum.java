package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.common.pojo.enums
 * @description:
 * @create 2024/9/3 14:20
 */
@Getter
public enum IndicatorFuncEnum {
    /**
     * 记录数
     */
    RECORDVALUE("", "记录数"),

    /**
     * 和值
     */
    SUM("sum", "和值"),
    /**
     * 最大值
     */
    MAX("max", "最大值"),
    /**
     * 最小值
     */
    MIN("min", "最小值");
    /**
     * 首行
     */
//    FIRST("first", "首行"),
    /**
     * 尾行
     */
//    LAST("last", "尾行");


    private final String value;

    private final String description;
    IndicatorFuncEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }
    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(IndicatorFuncEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDescription()).value(enumObj.getValue()).build());
        }
        return list;
    }
}
