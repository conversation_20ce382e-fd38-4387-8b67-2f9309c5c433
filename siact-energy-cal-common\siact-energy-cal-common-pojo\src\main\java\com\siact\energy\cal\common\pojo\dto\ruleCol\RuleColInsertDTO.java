package com.siact.energy.cal.common.pojo.dto.ruleCol;

import com.alibaba.fastjson.JSONArray;
import com.siact.energy.cal.common.pojo.enums.ActiveStateEnum;
import com.siact.energy.cal.common.pojo.validator.EnumValidator;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 指标集表(RuleCol) 新增DTO
 *
 * <AUTHOR>
 * @since 2024-05-20 11:30:22
 */
@ApiModel("指标集表新增DTO")
@Data
public class RuleColInsertDTO {

    /**
     * 指标集名称
     */
    @ApiModelProperty(value = "指标集名称", required = true, position = 1)
    @NotBlank(message = "指标集名称不能为空")
    private String ruleColName;

    /**
     * 指标集描述
     */
    @ApiModelProperty(value = "指标集描述", position = 2)
    private String ruleColDes;

    /**
     * 是否启用（0-启用,1-未启用）
     */
    @ApiModelProperty(value = "是否启用（0-启用,1-未启用）", required = true, position = 3)
    @EnumValidator(enumClass = ActiveStateEnum.class, existGetMethod = "getValue", message = ActiveStateEnum.TIPS)
    @NotNull(message = "是否启用不能为空")
    private Integer activeState;

    /**
     * 指标集构建
     */
    @ApiModelProperty(value = "指标集构建-属性列表", required = false, position = 4)
    private List<AttrObject> attrList;

}

