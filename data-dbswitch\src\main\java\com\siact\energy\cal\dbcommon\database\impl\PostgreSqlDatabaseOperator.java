// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbcommon.database.impl;

import com.siact.energy.cal.dbcommon.database.AbstractDatabaseOperator;
import com.siact.energy.cal.dbcommon.database.IDatabaseOperator;
import org.apache.commons.lang3.StringUtils;
import com.siact.energy.cal.dbcommon.domain.StatementResultSet;

import javax.sql.DataSource;
import java.util.List;

/**
 * PostgreSQL数据库实现类
 *
 * <AUTHOR>
 */
public class PostgreSqlDatabaseOperator extends AbstractDatabaseOperator implements
        IDatabaseOperator {

  public PostgreSqlDatabaseOperator(DataSource dataSource) {
    super(dataSource);
  }

  @Override
  public String getSelectTableSql(String schemaName, String tableName, List<String> fields) {
    return String.format("select \"%s\" from \"%s\".\"%s\" ",
        StringUtils.join(fields, "\",\""), schemaName,
        tableName);
  }

  @Override
  public StatementResultSet queryTableData(String schemaName, String tableName, List<String> fields,
										   List<String> orders) {
    String sql = String.format("select \"%s\" from \"%s\".\"%s\" order by \"%s\" asc ",
        StringUtils.join(fields, "\",\""), schemaName, tableName,
        StringUtils.join(orders, "\",\""));
    return this.selectTableData(sql, this.fetchSize);
  }

  @Override
  public StatementResultSet queryTableData(String schemaName, String tableName,
										   List<String> fields) {
    String sql = String.format("select \"%s\" from \"%s\".\"%s\" ",
        StringUtils.join(fields, "\",\""), schemaName,
        tableName);
    return this.selectTableData(sql, this.fetchSize);
  }

  @Override
  public void truncateTableData(String schemaName, String tableName) {
    String sql = String.format("TRUNCATE TABLE \"%s\".\"%s\" RESTART IDENTITY ",
        schemaName, tableName);
    this.executeSql(sql);
  }

  @Override
  public void dropTable(String schemaName, String tableName) {
    String sql = String.format("DROP TABLE \"%s\".\"%s\" cascade ",
        schemaName, tableName);
    this.executeSql(sql);
  }
}
