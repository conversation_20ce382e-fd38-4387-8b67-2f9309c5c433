package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.IfComponentNodeDto;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-11
 * @Description: 条件组件
 * @Version: 1.0
 */
@Component("IfNode")
@Slf4j
public class IfNodeComponent extends NodeBooleanComponent {

    @Resource
    IFlowRelationService flowRelationService;

    @Override
    public boolean processBoolean(){
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            log.error("组件配置信息不存在,{}, {}", flowId, nodeId);
            throw new BizException("组件配置信息不存在");
        }
        //获取数据源id
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        IfComponentNodeDto ifComponentNodeDto = BeanUtil.toBean(collect, IfComponentNodeDto.class);
        String text = ifComponentNodeDto.getCondition();
        if (text.equalsIgnoreCase("true")){
            return true;
        }else if (text.equalsIgnoreCase("false")){
            return false;
        }else {
            log.error("条件判断不合法,{}", text);
            throw new BizException("条件判断不合法");
        }
    }
}
