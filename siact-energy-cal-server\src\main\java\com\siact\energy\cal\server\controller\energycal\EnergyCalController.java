package com.siact.energy.cal.server.controller.energycal;

/**
 * @Package com.siact.energycal.controller
 * @description:
 * <AUTHOR>
 * @create 2024/8/2 15:53
 */

import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.energycal.*;
import com.siact.energy.cal.common.pojo.vo.energycal.DataIntervalQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.DataPointQueryVo;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "计算框架数据调用接口", tags = {"计算框架-数据调用"})
public class EnergyCalController {

    @Autowired
    public EnergyCalService energyCalService;

    @PostMapping("/history/getEquallySpacedTimeData")
    @ApiOperation("获取历史数据等时间间隔采样数据")
    public R<List<DataIntervalQueryVo>> getEquallySpacedTimeData(
            @RequestBody QueryDataByIntervalTimeDTO oldDTO) {
        TimeQueryDTO queryDTO = convertToTimeQueryDTO(oldDTO, TimeQueryDTO.TimeQueryType.EQUALLY_SPACED);
        return R.OK(energyCalService.getEquallySpacedTimeData(queryDTO));
    }


    @PostMapping("/history/getTimeIntervalData")
    @ApiOperation("获取时间区间数据")
    public R<List<DataPointQueryVo>> getTimeIntervalData(@RequestBody DataIntervalTimeDTO dataIntervalTimeDTO) {
        TimeQueryDTO queryDTO = convertToTimeQueryDTO(dataIntervalTimeDTO, TimeQueryDTO.TimeQueryType.INTERVAL);
        return R.OK(energyCalService.getTimeIntervalData(queryDTO));
    }

    @PostMapping("/history/getTimeSliceData")
    @ApiOperation("获取指定时间断面数据,最近15分钟最后一包数据")
    public R<List<DataPointQueryVo>> getTimeSliceData(@RequestBody QueryDataByPointTimeDTO queryDataByPointTimeDTO) {
        return R.OK(energyCalService.getTimeSliceData(queryDataByPointTimeDTO));
    }


    @PostMapping("/history/calBaseIndicatorByPropCode")
    @ApiOperation("根据基础指标属性编码计算基础指标值")
    public R calBaseIndicatorByPropCode(@RequestBody QueryDataByIntervalTimeDTO dataIntervalTimeDTO) {
        TimeQueryDTO queryDTO = convertToTimeQueryDTO(dataIntervalTimeDTO, TimeQueryDTO.TimeQueryType.EQUALLY_SPACED);
        energyCalService.calBaseIndicatorByDevpropertyCode(queryDTO);
        return R.OK();
    }


    @PostMapping("/history/calBaseIndicatorByProjectCode")
    @ApiOperation("根据项目编码计算基础指标值")
    public R calBaseIndicatorByProjectCode(@RequestBody QueryDataByIntervalTimeDTO queryDataByIntervalTimeDTO) {
        TimeQueryDTO queryDTO = convertToTimeQueryDTO(queryDataByIntervalTimeDTO, TimeQueryDTO.TimeQueryType.EQUALLY_SPACED);
        energyCalService.calBaseIndicatorByProjectCode(queryDTO);
        return R.OK();
    }

    @PostMapping("/calculateFormula/EquallySpacedTime")
    @ApiOperation("公式等时间间隔计算")
    public R<List<DataIntervalQueryVo>> calculateFormulaEquallySpacedTime(@RequestBody CalculateFormulaEquallyTimeDTO calculateFormulaDTO) {
        TimeQueryFormulaDTO timeQueryFormulaDTO = convertToTimeQueryFormulaDTO(calculateFormulaDTO, TimeQueryFormulaDTO.TimeQueryType.EQUALLY_SPACED);
        return R.OK(energyCalService.calculateFormulaEquallySpacedTime(timeQueryFormulaDTO));
    }


    @PostMapping("/calculateFormula/IntervalTime")
    @ApiOperation("公式时间区间计算")
    public R<List<DataIntervalQueryVo>> calculateFormulaIntervalTime(@RequestBody CalculateFormulaIntervalTimeDTO calculateFormulaDTO) {
        TimeQueryFormulaDTO timeQueryFormulaDTO = convertToTimeQueryFormulaDTO(calculateFormulaDTO, TimeQueryFormulaDTO.TimeQueryType.INTERVAL);
        return R.OK(energyCalService.calculateFormulaEquallySpacedTime(timeQueryFormulaDTO));
    }
    @PostMapping("/calculateFormula/TimeSlice")
    @ApiOperation("公式时间断面计算")
    public R<List<DataPointQueryVo>> calculateFormulaTimeSlice(@RequestBody CalculateFormulaSliceTimeDTO calculateFormulaDTO) {
        return R.OK(energyCalService.calculateFormulaTimeSlice(calculateFormulaDTO));
    }

    private TimeQueryDTO convertToTimeQueryDTO(Object sourceDTO, TimeQueryDTO.TimeQueryType queryType) {
        TimeQueryDTO targetDTO = new TimeQueryDTO();
        BeanUtils.copyProperties(sourceDTO, targetDTO);
        targetDTO.setQueryType(queryType);
        return targetDTO;
    }
    private TimeQueryFormulaDTO convertToTimeQueryFormulaDTO(Object sourceDTO, TimeQueryFormulaDTO.TimeQueryType queryType) {
        TimeQueryFormulaDTO targetDTO = new TimeQueryFormulaDTO();
        BeanUtils.copyProperties(sourceDTO, targetDTO);
        targetDTO.setQueryType(queryType);
        return targetDTO;
    }
}
