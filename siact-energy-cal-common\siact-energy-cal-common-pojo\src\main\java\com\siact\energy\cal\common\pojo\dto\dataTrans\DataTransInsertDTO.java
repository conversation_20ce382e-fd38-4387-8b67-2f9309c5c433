package com.siact.energy.cal.common.pojo.dto.dataTrans;


import com.siact.energy.cal.common.pojo.validator.MqttTopicValidator;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 数据传输表（mqtt）(DataTrans) 新增DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:55
 */
@ApiModel("数据传输表（mqtt）新增DTO")
@Data
public class DataTransInsertDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 1, required = true, example = "10000")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 主机名
     */
    @ApiModelProperty(value = "主机名", position = 2, required = true, example = "**************")
    @NotBlank(message = "主机名不能为空")
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口", position = 3, required = true, example = "1883")
    @NotBlank(message = "端口不能为空")
    private String port;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 4, required = true, example = "mqttuser")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 5, required = true, example = "mqttuser")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题", position = 6, required = true, example = "/#")
    @MqttTopicValidator
    @NotBlank(message = "主题不能为空")
    private String topic;

}

