package com.siact.energy.cal.tool.controller.dataTrans;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.ResponseCodeConstant;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransSubscribeDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransUpdateDTO;
import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import com.siact.energy.cal.common.pojo.vo.dataTrans.DataTransVO;
import com.siact.energy.cal.tool.common.enums.SseEmitterEventEnum;
import com.siact.energy.cal.tool.service.dataTrans.DataTransService;
import io.swagger.annotations.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 数据传输表（mqtt）(DataTrans)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:35
 */
@Api(tags = {"数据传输表（mqtt）"})
@ApiSort(30)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/data/trans")
public class DataTransController {

    private final DataTransService dataTransService;

    /**
     * 分页列表
     *
     * @param page              分页对象
     * @param dataTransQueryDTO 查询实体
     * @return 查询结果
     */
    @ApiOperation(value = "分页列表", hidden = true)
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    @GetMapping("/listPage")
    public R<PageBean<DataTransVO>> listPage(PageBean<DataTransVO> page, DataTransQueryDTO dataTransQueryDTO) {
        return R.OK(dataTransService.listPage(page, dataTransQueryDTO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过主键查询单条数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 20)
    @GetMapping("{id}")
    public R<DataTransVO> selectOne(@PathVariable Serializable id) {
        return R.OK(dataTransService.getVoById(id));
    }

    /**
     * 新增数据
     *
     * @param dataTransInsertDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataTransInsertDTO", value = "实体对象", dataType = "数据传输表（mqtt）新增DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping
    public R<Boolean> insert(@RequestBody @Validated DataTransInsertDTO dataTransInsertDTO) {
        return R.OK(dataTransService.save(dataTransInsertDTO));
    }

    /**
     * 修改数据
     *
     * @param dataTransUpdateDTO 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataTransUpdateDTO", value = "实体对象", dataType = "数据传输表（mqtt）更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PutMapping
    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) DataTransUpdateDTO dataTransUpdateDTO) {
        return R.OK(dataTransService.updateVoById(dataTransUpdateDTO));
    }

    /**
     * 删除数据
     *
     * @param ids 主键集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping
    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        return R.OK(dataTransService.removeByIds(ids));
    }

    /**
     * 通过项目ID获取实时数据消费配置
     *
     * @param projectId 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过项目ID获取实时数据消费配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目ID", example = "10000", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @GetMapping("getByProjectId")
    public R<DataTransVO> getByProjectId(@RequestParam("projectId") Long projectId) {
        return R.OK(dataTransService.getByProjectId(projectId));
    }

    /**
     * 新增或编辑信息
     *
     * @param dataTransUpdateDTO 实体对象
     * @return 新增或编辑结果
     */
    @ApiOperation(value = "新增或编辑信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataTransUpdateDTO", value = "实体对象", dataType = "数据传输表（mqtt）更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @PostMapping("/saveOrUpdate")
    public R<Boolean> saveOrUpdate(@RequestBody @Validated DataTransUpdateDTO dataTransUpdateDTO) {
        return R.OK(dataTransService.saveOrUpdate(dataTransUpdateDTO));
    }

    /**
     * 订阅主题
     *
     * @param dto 实体对象
     * @return 订阅结果
     */
    @ApiOperation(value = "订阅主题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", value = "实体对象", dataType = "数据传输订阅DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功", response = SubscribeResponseVO.class)
    })
    @ApiOperationSupport(order = 70)
    @PostMapping(value = "/subscribe", produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public SseEmitter subscribe(@RequestBody @Validated DataTransSubscribeDTO dto, BindingResult bindingResult) {
        try {

            if(bindingResult.hasErrors()) {
                String error = bindingResult.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(";"));
                throw new BizException(error);
            }

            return dataTransService.subscribe(dto);
        } catch (Exception e) {
            // 处理抛出异常的场景
            SseEmitter sseEmitter = new SseEmitter();
            try {
                sseEmitter.send(SseEmitter.event()
                        .id(UUID.randomUUID().toString())
                        .name(SseEmitterEventEnum.MQTT_SUBSCRIBE_MESSAGE_FAIL.getValue())
                        .comment("subscribe mqtt message fail")
                        .data(e.getMessage()));
                sseEmitter.completeWithError(e);
            } catch (IOException ex) {
                sseEmitter.completeWithError(ex);
            }
            return sseEmitter;
        }
    }

    @ApiModel("订阅主题相应VO")
    @Data
    public static class SubscribeResponseVO {

        /**
         * sse ID
         */
        @ApiModelProperty(value = "sse ID", position = 1, example = "ca71f3f2-49f7-4d87-b63d-1dfa8cf28721")
        private String id;

        /**
         * sse event
         */
        @ApiModelProperty(value = "sse event，mqttConnectSuccessEvent：MQTT连接成功事件，mqttConsumerMessageEvent：MQTT消费消息事件，mqttSubscribeMessageFail：消费mqtt消息失败事件",
                position = 2, example = "mqttConsumerMessageEvent")
        private String event;

        /**
         * sse comment
         */
        @ApiModelProperty(value = "sse comment", position = 3, example = "Consumer mqtt message")
        private String comment;

        /**
         * sse data
         */
        @ApiModelProperty(value = "sse data, 消费消息格式见示例", position = 4, example = "{\"topic\":\"topic/demo\",\"qos\":1,\"message\":\"{}\",\"date\":\"2024-05-17 08:39:07\"}")
        private Object data;
    }

    /**
     * 取消订阅主题
     *
     * @param subscribeId 订阅ID
     * @return 取消订阅结果
     */
    @ApiOperation(value = "取消订阅主题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subscribeId", value = "订阅ID", dataType = "String", example = "ca71f3f2-49f7-4d87-b63d-1dfa8cf28721", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 80)
    @DeleteMapping(value = "/unsubscribe")
    public R<Boolean> unsubscribe(@NotBlank(message = "订阅ID不能为空") @RequestParam("subscribeId") String subscribeId) {
        return R.OK(dataTransService.unsubscribe(subscribeId));
    }
}

