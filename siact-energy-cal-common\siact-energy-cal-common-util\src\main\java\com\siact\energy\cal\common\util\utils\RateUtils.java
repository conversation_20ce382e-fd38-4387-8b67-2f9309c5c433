package com.siact.energy.cal.common.util.utils;


import com.siact.energy.cal.common.pojo.dto.energycal.TimeUnitDto;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.TimeUnit;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.common.util.utils
 * @description: 同比环比计算工具类
 * @create 2024/10/14 10:59
 */

public class RateUtils {
    /**
     * 获取传入时间单位的父级单位。
     *
     * @param unit 时间单位字符串
     * @return 父级时间单位
     */
    public static TimeUnit getParentUnit(String unit) {
        TimeUnit timeUnit = TimeUnit.fromString(unit);
        if (timeUnit == null) {
            throw new IllegalArgumentException("Invalid time unit: " + unit);
        }
        return timeUnit.getParent();
    }

    /**
     * @return com.siact.energy.cal.common.pojo.dto.energycal.TimeUnitDto
     * <AUTHOR>
     * @Description //环比的基期计算
     * @Date 11:40 2024/10/14
     * @Param [timeUnitDto]
     **/

    public static TimeUnitDto getBasePeriod(TimeUnitDto timeUnitDto, String flag) {
        TimeUnitDto timeUnitDtoResult = new TimeUnitDto();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startDateTime = LocalDateTime.parse(timeUnitDto.getStartTime(), formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(timeUnitDto.getEndTime(), formatter);

        TimeUnit timeUnit = timeUnitDto.getTimeUnit();
        Integer interval = timeUnitDto.getInterval();
        String tsUnitName = "";
        if (ConstantBase.YOY.equals(flag)) {
            //同比
            tsUnitName= timeUnit.getParent().getName();
        } else if (ConstantBase.MOM.equals(flag)) {
            //环比
            tsUnitName= timeUnit.getName();
        }
        if (timeUnit != null) {
            switch (tsUnitName) {
                case "y":
                    startDateTime = startDateTime.minusYears(interval);
                    endDateTime = endDateTime.minusYears(interval);
                    break;
                case "n":
                    startDateTime = startDateTime.minusMonths(interval);
                    endDateTime = endDateTime.minusMonths(interval);
                    break;
                case "d":
                    startDateTime = startDateTime.minusDays(interval);
                    endDateTime = endDateTime.minusDays(interval);
                    break;
                case "h":
                    startDateTime = startDateTime.minusHours(interval);
                    endDateTime = endDateTime.minusHours(interval);
                    break;
                case "m":
                    startDateTime = startDateTime.minusMinutes(interval);
                    endDateTime = endDateTime.minusMinutes(interval);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported time unit: " + timeUnit.getName());
            }

            timeUnitDtoResult.setStartTime(startDateTime.format(formatter));
            timeUnitDtoResult.setEndTime(endDateTime.format(formatter));
            timeUnitDtoResult.setInterval(interval);
            timeUnitDtoResult.setTimeUnit(timeUnit);
        }
        return timeUnitDtoResult;
    }

    public static void main(String[] args) {
        TimeUnitDto timeUnitDto = new TimeUnitDto();
        timeUnitDto.setStartTime("2024-10-10 00:00:00");
        timeUnitDto.setEndTime("2024-10-14 11:00:00");
        timeUnitDto.setInterval(1);
        timeUnitDto.setTimeUnit(TimeUnit.YEAR);
        TimeUnitDto momBasePeriod = getBasePeriod(timeUnitDto,ConstantBase.MOM);

        System.out.println(momBasePeriod);
    }
}
