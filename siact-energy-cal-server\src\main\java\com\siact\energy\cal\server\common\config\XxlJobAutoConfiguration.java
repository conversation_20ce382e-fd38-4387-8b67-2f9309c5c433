package com.siact.energy.cal.server.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@ConditionalOnProperty(prefix = "xxl.job", name = "enabled", havingValue = "true", matchIfMissing = false)
@Import({
    XxlJobConfig.class,
//    XxlJobAdminConfig.class,
//    JobAlarmer.class,
    // ... 其他 xxl-job 相关配置类
})
public class XxlJobAutoConfiguration {
    // 空配置类，用于统一导入和控制 xxl-job 相关配置
} 