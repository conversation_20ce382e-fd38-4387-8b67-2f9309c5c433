// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbwriter.dm;

import com.siact.energy.cal.dbwriter.AbstractDatabaseWriter;
import com.siact.energy.cal.dbwriter.IDatabaseWriter;
import com.siact.energy.cal.dbwriter.util.ObjectCastUtils;

import javax.sql.DataSource;
import java.util.List;

/**
 * DM数据库写入实现类
 *
 * <AUTHOR>
 */
public class DmWriterImpl extends AbstractDatabaseWriter implements IDatabaseWriter {

  public DmWriterImpl(DataSource dataSource) {
    super(dataSource);
  }

  @Override
  protected String getDatabaseProductName() {
    return "DM";
  }

  @Override
  public long write(List<String> fieldNames, List<Object[]> recordValues) {
    recordValues.parallelStream().forEach((Object[] row) -> {
      for (int i = 0; i < row.length; ++i) {
        try {
          row[i] = ObjectCastUtils.castByDetermine(row[i]);
        } catch (Exception e) {
          row[i] = null;
        }
      }
    });

    return super.write(fieldNames, recordValues);
  }
}
