/*
 *
 *  Licensed to the Apache Software Foundation (ASF) under one or more
 *  contributor license agreements.  See the NOTICE file distributed with
 *  this work for additional information regarding copyright ownership.
 *  The ASF licenses this file to You under the Apache License, Version 2.0
 *  (the "License"); you may not use this file except in compliance with
 *  the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 */

package com.siact.energy.cal.core.pool;

import java.util.Map;

/**
 * AbstractPool
 *
 * <AUTHOR>
 * @since 2022/5/28 19:40
 */
public abstract class AbstractPool<T> {

    public abstract Map<String, T> getMap();

    public boolean exist(String key) {
        return getMap().containsKey(key);
    }

    public int push(String key, T entity) {
        getMap().put(key, entity);
        return getMap().size();
    }

    public int remove(String key) {
        getMap().remove(key);
        return getMap().size();
    }

    public T get(String key) {
        return getMap().get(key);
    }

    public abstract void refresh(T entity);
}
