// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.core.database.impl;


import com.siact.energy.cal.common.type.ProductTypeEnum;

/**
 * 支持GBase8a数据库的元信息实现
 *
 * <AUTHOR>
 */
public class DatabaseGbase8aImpl extends DatabaseMysqlImpl {

  public DatabaseGbase8aImpl() {
    super("com.gbase.jdbc.Driver");
  }

  @Override
  public ProductTypeEnum getDatabaseType() {
    return ProductTypeEnum.GBASE8A;
  }

}
