package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-12
 * @Description: 条件组件
 * @Version: 1.0
 */
@Data
@ApiModel("条件组件")
public class IfComponentNodeDto {
    @ApiModelProperty(value = "组件名称")
    private String name;
    @ApiModelProperty(value = "判断公式")
    private String condition;
}
