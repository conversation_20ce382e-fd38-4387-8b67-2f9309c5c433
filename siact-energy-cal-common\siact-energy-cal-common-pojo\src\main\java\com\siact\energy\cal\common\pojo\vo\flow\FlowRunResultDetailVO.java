package com.siact.energy.cal.common.pojo.vo.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-20
 * @Description: 组件流执行返回结果VO
 * @Version: 1.0
 */
@ApiModel("组件流执行返回结果详情VO")
@Data
public class FlowRunResultDetailVO {
    @ApiModelProperty(value = "数据条数", position = 1, example = "100")
    private Integer count;
    @ApiModelProperty(value = "结果数据", position = 2)
    private List<Map<String, Object>> resultDetailList;

    @ApiModelProperty(value = "数据字段", position = 3)
    private List<String> fieldList;
}
