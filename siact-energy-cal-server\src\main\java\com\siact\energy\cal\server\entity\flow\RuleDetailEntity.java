package com.siact.energy.cal.server.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-31
 * @Description: 规则表
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("rule_detail")
public class RuleDetailEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField("rule_name")
    private String ruleName;
    @TableField("rule_des")
    private String ruleDes;
    @TableField("rule_type")
    private Integer ruleType;
    @TableField("cal_type")
    private Integer calType;
    @TableField("dev_code")
    private String devCode;
    @TableField("dev_name")
    private String devName;
    @TableField("dev_property")
    private String devProperty;
    @TableField("prop_name")
    private String propMame;
    @TableField("rule_formula")
    private String ruleFormula;
    @TableField("rule_formula_show")
    private String ruleFormulaShow;
    @TableField("project_id")
    private Long projectId;
    @TableField("rule_col_id")
    private Long ruleColId;
    @TableField("active_state")
    private Integer activeState;

}
