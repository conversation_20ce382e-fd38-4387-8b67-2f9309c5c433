package com.siact.energy.cal.server.common.flow.node;

import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-11
 * @Description: 分组组件
 * @Version: 1.0
 */
@Component("WhenGroupNode")
@Slf4j
public class WhenGroupNode extends NodeComponent {
    @Override
    public void process() throws Exception {
        log.info("分组组件");
    }
}
