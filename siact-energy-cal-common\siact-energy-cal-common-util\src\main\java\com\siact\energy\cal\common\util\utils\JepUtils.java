package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.util.StrUtil;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.Jep;
import com.singularsys.jep.JepException;
import com.singularsys.jep.ParseException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project siact-sec
 * @description JEP公式计算 @[]
 * @date 2023/10/19 17:16:20
 */
public class JepUtils {
    private static final Logger LOGGER = Logger.getLogger(JepUtils.class.getName());
    /**
     * 公式计算
     *
     * @param formula    公式
     * @param param2Vals 变量-》值
     * @param scale      精度
     * @param nullCalc   空值是否计算 如果计算 则将空值当0参与运算 如果不计算则当前计算结果为null
     * @return
     */
    public static BigDecimal calc(String formula, Map<String, BigDecimal> param2Vals, int scale, boolean nullCalc) throws ParseException, EvaluationException {

        if(StrUtil.isEmpty(formula)){
            return null;
        }
        //如果数据全为null则返回null
        if (param2Vals.values().stream().filter(Objects::nonNull).collect(Collectors.toList()).size() == 0) {
            return null;
        }

        //JEP初始化
        Jep jep = new Jep();
        String fm = formula.replaceAll("@|\\[|\\]", "");
        jep.parse(fm);

        //添加变量
        BigDecimal bpv = null;
        //是否计算
        boolean isCalc = true;
        for (String s : getVariables(formula)) {
            try {
                BigDecimal v = param2Vals.get(s);
                if (v == null) {
                    if (nullCalc) {
                        //空值置为0参与运算
                        jep.addVariable(s, 0);
                    } else {
                        isCalc = false;
                        break;
                    }
                } else {
                    jep.addVariable(s, param2Vals.get(s));
                }
            } catch (JepException e) {
                e.printStackTrace();
//                throw new RuntimeException("JEP添加变量异常");
            }
        }

        //执行计算
        if (isCalc) {
            Object evaluate = null;
            try {
                evaluate = jep.evaluate();
            } catch (EvaluationException e) {
                LOGGER.severe("JEP计算异常==="+e.getMessage());
//                throw new RuntimeException(e);
            }
            if (null != evaluate ) {
                if(evaluate instanceof BigDecimal){
                    bpv = (BigDecimal) evaluate;
                }else if(!Double.isNaN((Double) evaluate )&& !Double.isInfinite((Double) evaluate)){
                    bpv = new BigDecimal(String.valueOf(evaluate));
                }else {
                    return null;
                }
                bpv = bpv.setScale(scale, BigDecimal.ROUND_HALF_UP);
            }
        }
        return bpv;
    }
    public static List<String> getVariables(String formula) {
        String f = formula;
        List<String> res = new ArrayList<>();
        while (f.indexOf("@[") >= 0) {
            int startIndex = f.indexOf("@[") + 2;
            int endIndex = f.indexOf("]", startIndex);
            res.add(f.substring(startIndex, endIndex));
            f = f.substring(endIndex + 1);
        }
        return res;
    }

}
