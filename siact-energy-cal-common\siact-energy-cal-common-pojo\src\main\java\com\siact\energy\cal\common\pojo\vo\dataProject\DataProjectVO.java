package com.siact.energy.cal.common.pojo.vo.dataProject;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目表(DataProject) VO
 *
 * <AUTHOR>
 * @since 2024-05-14 15:36:45
 */
@ApiModel("项目表VO")
@Data
public class DataProjectVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    private Long id;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", position = 2)
    private String projectName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码", position = 3)
    private String projectCode;

}

