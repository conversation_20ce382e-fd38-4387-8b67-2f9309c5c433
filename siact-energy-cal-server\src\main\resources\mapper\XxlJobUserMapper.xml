<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.xxlJob.XxlJobUserMapper">

	<resultMap id="XxlJobUser" type="com.siact.energy.cal.server.xxljob.core.model.XxlJobUser" >
		<result column="id" property="id" />
		<result column="username" property="username" />
	    <result column="password" property="password" />
	    <result column="role" property="role" />
	    <result column="permission" property="permission" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.username,
		t.password,
		t.role,
		t.permission
	</sql>

	<select id="pageList" parameterType="java.util.HashMap" resultMap="XxlJobUser">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_user AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="username != null and username != ''">
				<choose>
					<!-- PostgreSQL数据库（包括南大通用） -->
					<when test="_databaseId == 'postgresql'">
						AND t.username like ('%' || #{username} || '%')
					</when>
					<!-- 默认使用MySQL语法 -->
					<otherwise>
						AND t.username like CONCAT(CONCAT('%', #{username}), '%')
					</otherwise>
				</choose>
			</if>
			<if test="role gt -1">
				AND t.role = #{role}
			</if>
		</trim>
		ORDER BY username ASC
		<choose>
			<!-- PostgreSQL数据库（包括南大通用） -->
			<when test="_databaseId == 'postgresql'">
				LIMIT #{pagesize} OFFSET #{offset}
			</when>
			<!-- 默认使用MySQL语法 -->
			<otherwise>
				LIMIT #{offset}, #{pagesize}
			</otherwise>
		</choose>
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM xxl_job_user AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="username != null and username != ''">
				AND t.username like CONCAT(CONCAT('%', #{username}), '%')
			</if>
			<if test="role gt -1">
				AND t.role = #{role}
			</if>
		</trim>
	</select>

	<select id="loadByUserName" parameterType="java.util.HashMap" resultMap="XxlJobUser">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_user AS t
		WHERE t.username = #{username}
	</select>

	<insert id="save" parameterType="com.siact.energy.cal.server.xxljob.core.model.XxlJobUser" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_user (
			username,
			password,
			role,
			permission
		) VALUES (
			#{username},
			#{password},
			#{role},
			#{permission}
		);
	</insert>

	<update id="update" parameterType="com.siact.energy.cal.server.xxljob.core.model.XxlJobUser" >
		UPDATE xxl_job_user
		SET
			<if test="password != null and password != ''">
				password = #{password},
			</if>
			role = #{role},
			permission = #{permission}
		WHERE id = #{id}
	</update>

	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM xxl_job_user
		WHERE id = #{id}
	</delete>

</mapper>