package com.siact.energy.cal.common.pojo.dto.funLib;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 常用函数库(FunLib) 查询DTO
 *
 * <AUTHOR>
 * @since 2024-06-11 11:04:46
 */
@ApiModel("常用函数库查询DTO")
@Data
public class FunLibQueryDTO {

    /**
     * 函数名称
     */
    @ApiModelProperty(value = "函数名称", position = 1)
    private String value;

    /**
     * 函数展示名称
     */
    @ApiModelProperty(value = "函数展示名称", position = 2)
    private String label;

    /**
     * 函数组件
     */
    @ApiModelProperty(value = "函数组件", position = 3)
    private List<String> pieces;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", position = 4)
    private String description;

    /**
     * 计算类型（1-基础指标，2-聚合指标）
     */
    @ApiModelProperty(value = "计算类型（1-基础指标，2-聚合指标）", position = 5)
    private Integer calType;

}

