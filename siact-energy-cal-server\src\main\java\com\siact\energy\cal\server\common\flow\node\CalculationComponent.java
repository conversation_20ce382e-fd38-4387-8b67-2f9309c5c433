package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CalculationDto;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.common.flow.init.TaoSnit;
import com.siact.energy.cal.server.entity.flow.RuleDetailEntity;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.RuleDetailFlowServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 指标计算组件
 * @Version: 1.0
 */
@Component("CommonNode")
@Slf4j
public class CalculationComponent extends NodeComponent  {

    @Resource
    DataSource dataSource;
    @Resource
    RuleDetailFlowServiceImpl ruleDetailFlowService;
    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    IFlowRelationService flowRelationService;


    @Resource
    TaoSnit taoSnit;
    @Override
    public void process() {
        ResultContext anyName = this.getContextBean("resultContext");
        //查询规则
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = anyName.getTaskId();
        log.info("指标计算组件开始执行，tag:{}",nodeId);
        //获取计算组件的页面配置信息
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置信息不存在");
        }
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        CalculationDto bean = BeanUtil.toBean(collect, CalculationDto.class);
        //创建试图
        StringBuilder sb = new StringBuilder();
        String viewName = UUIDUtils.uuidViewName();
        sb.append("create view ").append(viewName).append(" as select ts, ");
        if (bean.getCalFormula() != null && !bean.getCalFormula().isEmpty()) {
            //1、根据配置公式查询taoS数据库计算结果
            for (int i=0; i< bean.getCalFormula().size(); i++) {
                Optional<RuleDetailEntity> ruleDetailOptionConfig = ruleDetailFlowService.getRuleDetailOption("1802633784983654403");
                if(!ruleDetailOptionConfig.isPresent()){
                    continue;
                }
                String ruleFormula = ruleDetailOptionConfig.get().getRuleFormula();
                String ruleFormulaStr = StringUtils.getRuleFormulaStr(ruleFormula);
                sb.append(ruleFormulaStr).append(" as ").append(ruleDetailOptionConfig.get().getDevProperty()).append(i).append(",");
            }
            sb.deleteCharAt(sb.lastIndexOf(","));
            sb.append(" from ").append(anyName.getResultTable())
                    .append(";");
            DBTools.executeSql(sb.toString(), dataSource);
            flowViewService.insertFlowView(Integer.parseInt(flowId), viewName, nodeId, taskId);
            anyName.setSql(sb.toString());
            anyName.setResultTable(viewName);
        }else {
            throw new BizException("CommonNode:配置的公式不存在");
        }
        log.info("指标计算组件执行完毕");
    }

    private static List<Map<String, String>> getRuleDetailOption(String sql){
        List<Map<String, String>> list = new ArrayList<>();
        Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(), "121.36.3.28",
                "29001", "test", "root",
                "taosdata");
        try {
            ResultSet rs = DBTools.executeQuerySql(con, sql);
            HashMap<String, String> map = new HashMap<>();
            while (true){
                if (!rs.next()){
                    break;
                }
                map.put("ts", rs.getObject(1).toString());
                map.put("value", rs.getObject(2).toString());
                list.add(map);
            }
            return list;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }finally {
            try {
                assert con != null;
                con.close();
            } catch (SQLException e) {
                throw new BizException("关闭连接失败");
            }
        }
    }



}
