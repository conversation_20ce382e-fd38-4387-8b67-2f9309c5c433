package com.siact.energy.cal.common.pojo.vo.energycal;

/**
 * @Package com.siact.energycal.vo
 * @description:
 * <AUTHOR>
 * @create 2024/7/16 14:07
 */

import lombok.Data;

import java.util.Date;

/**
 * @ClassName DataSourceVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/16 14:07
 * @Version 1.0
 **/
@Data
public class DataSourceVo {
    /**
     * 数据源名称
     */
    private String databaseName;
    /**
     * 数据源类型
     */
    private Integer dbType;
    /**
     * 数据源ip
     */
    private String databaseIp;
    /**
     * 数据源端口
     */
    private String databasePort;
    /**
     * 数据库名称
     */
    private String db;
    /**
     * 超级表名
     */
    private String tableName;
    /**
     * 状态（0-正常/1-中断），描述数据库的连接状态
     */
    private Integer status;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密码
     */
    private String password;
    /**
     * 数据库连接串
     */
    private String jdbcUrl;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 数据源描述
     */
    private String description;
    /**
     * 创建者id
     */
    private Long creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新者
     */
    private Long updater;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除标志(0-正常，1-已删除)
     */
    private Integer deleted;

    // 标记该数据源配置是否有效
    private boolean valid = true;  // 默认为true，表示有效
}
