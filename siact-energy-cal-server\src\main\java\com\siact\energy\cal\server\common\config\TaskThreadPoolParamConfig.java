package com.siact.energy.cal.server.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "io.thread.pool")
public class TaskThreadPoolParamConfig {
  
  /**
   * 核心线程数
   */
  private int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
  
  /**
   * 最大线程数
   */
  private int maxPoolSize = Runtime.getRuntime().availableProcessors() * 4;
  
  /**
   * 线程空闲时间
   */
  private int keepAliveSeconds = 60;
  
  /**
   * 任务队列容量（阻塞队列）
   */
  private int queueCapacity = 100;

  /**
   * 线程名称前缀
   */
  private String threadNamePrefix = "ioThreadPool-";
}
