package com.siact.energy.cal.tool.common.utils;

import com.siact.energy.cal.tool.common.enums.SseEmitterEventEnum;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttMessageListener;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * CustomMqttMessageListener
 *
 * <AUTHOR>
 * @since 2024-05-15 17:56:02
 */
@Slf4j
@RequiredArgsConstructor
public class CustomMqttMessageListener implements IMqttMessageListener {

    private final SseEmitter sseEmitter;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {

        if(log.isDebugEnabled()) {
            log.debug("接受到消息，topic: {}, message: {}", topic, new String(message.getPayload()));
        }

        sseEmitter.send(SseEmitter.event()
                .id(UUID.randomUUID().toString())
                .name(SseEmitterEventEnum.MQTT_CONSUMER_MESSAGE_EVENT.getValue())
                .comment("Consumer mqtt message")
                .data(
                        SseEmitterData.builder()
                                .topic(topic)
                                .qos(message.getQos())
                                .message(new String(message.getPayload()))
                                .date(LocalDateTime.now().format(dateTimeFormatter)).build()
                ));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class SseEmitterData {

        /**
         * 主题
         */
        private String topic;

        /**
         *
         */
        private int qos;

        /**
         * 消息
         */
        private String message;

        /**
         * 时间
         */
        private String date;

    }
}
