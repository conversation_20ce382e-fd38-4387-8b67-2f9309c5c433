package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-09-07
 * @Description: 数据源连接输出Dto
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DataSourceOutPutDto {
    @ApiModelProperty(value = "组件名称", example = "Mysql数据源", position = 1)
    private String name;
    @ApiModelProperty(value = "数据库类型", position = 2, example = "MYSQL")
    private Integer databaseType;

    @ApiModelProperty(value = "主机ip", position = 3, example = "127.0.0.1")
    private String databaseIp;

    @ApiModelProperty(value = "端口", position = 4, example = "3306")
    private String databasePort;

    @ApiModelProperty(value = "库名(服务名)", position = 5, example = "test")
    private String databaseName;

    @ApiModelProperty(value = "表名称", position = 6, example = "test")
    private String tableName;

    @ApiModelProperty(value = "数据库连接用户名", position = 7, example = "root")
    private String userName;

    @ApiModelProperty(value = "数据库连接密码", position = 8, example = "root")
    private String password;

    @ApiModelProperty(value = "jdbc连接串", position = 9)
    private String jdbcUrl;

}
