package com.siact.energy.cal.common.pojo.dto.energycal;/**
 * @Package com.siact.energycal.dto
 * @description:
 * <AUTHOR>
 * @create 2024/8/2 16:15
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName DataIntervalTimeDTO
 * @Description 查询时间区间DTO
 * <AUTHOR>
 * @Date 2024/8/2 16:15
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "获取时间区间数据DTO")
public class DataIntervalTimeDTO {

    @ApiModelProperty(value = "属性编码列表",position = 1,required = true)
    public List<String> dataCodes;

    @ApiModelProperty(value = "开始时间,示例值:2024-08-08 10:00:00",position = 2,required = true)
    public String startTime;

    @ApiModelProperty(value = "结束时间,示例值:2024-08-08 11:00:00",position = 3,required = true)
    public String endTime;

}
