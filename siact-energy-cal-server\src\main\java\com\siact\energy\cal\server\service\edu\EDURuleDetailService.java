package com.siact.energy.cal.server.service.edu;

import com.siact.edu.common.pojo.dto.DriverProjectDTO;
import com.siact.edu.common.pojo.dto.calc.CalcDriverDetailDTO;

import com.siact.edu.common.pojo.dto.calc.CalcPropDataDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailUpdateDTO;
import com.siact.energy.cal.common.pojo.enums.CalTypeEnum;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.RuleTypeEnum;
import com.siact.energy.cal.dbcommon.constant.Constants;
import com.siact.energy.cal.server.entity.dataProject.DataProject;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetail;
import com.siact.energy.cal.server.service.dataProject.DataProjectService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * EDU指标详情服务
 *
 * <AUTHOR>
 * @since 2025-07-10 16:28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EDURuleDetailService {

    private final RuleDetailService ruleDetailService;
    private final DataProjectService dataProjectService;

    /**
     * EDU加载数据 - 将DriverProjectDTO转换为RuleDetailInsertDTO并保存
     *
     * @param driverProjectDTO EDU驱动项目数据
     * @return 保存结果
     */
    @Transactional
    public Boolean eduLoad(DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        if (driverProjectDTO == null) {
            log.warn("EDU加载数据失败：driverProjectDTO为空");
            return false;
        }

        try {
            // 1. 根据projectCode获取projectId
            Long projectId = getProjectIdByCode(driverProjectDTO.getProjectCode());
            if (projectId == null) {
                log.warn("EDU加载数据失败：未找到项目编码对应的项目ID，projectCode: {}", driverProjectDTO.getProjectCode());
                return false;
            }

            // 2. 转换并保存指标详情
            List<RuleDetailInsertDTO> ruleDetailList = convertToRuleDetailInsertDTOList(driverProjectDTO, projectId);
            if (CollectionUtils.isEmpty(ruleDetailList)) {
                log.warn("EDU加载数据失败：转换后的指标详情列表为空");
                return false;
            }

            // 3. 批量保存指标详情
            boolean allSaved = true;
            for (RuleDetailInsertDTO ruleDetailInsertDTO : ruleDetailList) {
                Boolean saved = ruleDetailService.save(ruleDetailInsertDTO);
                if (!saved) {
                    log.error("保存指标详情失败：{}", ruleDetailInsertDTO.getRuleName());
                    allSaved = false;
                }
            }

            log.info("EDU加载数据完成，项目编码：{}，成功保存指标数量：{}",
                    driverProjectDTO.getProjectCode(), ruleDetailList.size());
            return allSaved;

        } catch (Exception e) {
            log.error("EDU加载数据异常：", e);
            throw new RuntimeException("EDU加载数据失败", e);
        }
    }

    /**
     * 根据项目编码获取项目ID
     *
     * @param projectCode 项目编码
     * @return 项目ID
     */
    private Long getProjectIdByCode(String projectCode) {
        if (!StringUtils.hasText(projectCode)) {
            return null;
        }

        // 查询所有项目，找到匹配的项目编码
        List<DataProject> allProjects = dataProjectService.list();
        for (DataProject project : allProjects) {
            if (projectCode.equals(project.getProjectCode())) {
                return project.getId();
            }
        }
        return null;
    }

    /**
     * 将DriverProjectDTO转换为RuleDetailInsertDTO列表
     *
     * @param driverProjectDTO 驱动项目数据
     * @param projectId 项目ID
     * @return 转换后的指标详情列表
     */
    private List<RuleDetailInsertDTO> convertToRuleDetailInsertDTOList(
            DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO, Long projectId) {

        List<RuleDetailInsertDTO> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(driverProjectDTO.getDriverDetail())) {
            return resultList;
        }

        for (CalcDriverDetailDTO driverDetail : driverProjectDTO.getDriverDetail()) {
            if (CollectionUtils.isEmpty(driverDetail.getDriverData())) {
                continue;
            }

            for (CalcPropDataDTO driverData : driverDetail.getDriverData()) {
                RuleDetailInsertDTO ruleDetailInsertDTO = convertDriverDataToRuleDetail(
                        driverData, driverDetail, projectId);

                if (ruleDetailInsertDTO != null) {
                    resultList.add(ruleDetailInsertDTO);
                }
            }
        }

        return resultList;
    }

    /**
     * 将单个驱动数据转换为RuleDetailInsertDTO
     *
     * @param driverData 驱动数据对象
     * @param driverDetail 驱动详情
     * @param projectId 项目ID
     * @return 转换后的指标详情DTO
     */
    private RuleDetailInsertDTO convertDriverDataToRuleDetail(
            CalcPropDataDTO driverData, CalcDriverDetailDTO driverDetail, Long projectId) {

        if (driverData == null) {
            return null;
        }

        RuleDetailInsertDTO dto = new RuleDetailInsertDTO();

        // 设置项目ID
        dto.setProjectId(projectId);

        // 设置默认值
        dto.setRuleType(RuleTypeEnum.MODEL.getValue()); // 默认为模型级规则

        // 从driverData设置指标信息
        dto.setRuleName(driverData.getRuleName());
        dto.setRuleDes(driverData.getRuleDes());
        dto.setCalType(Integer.parseInt(driverData.getCalType())); // 使用传入的计算类型
        dto.setDevProperty(driverData.getDevProperty());
        dto.setPropName(driverData.getPropName());
        dto.setRuleFormula(driverData.getRuleFormula());
        dto.setRuleFormulaShow(driverData.getRuleFormulaShow());

        // 从driverDetail设置节点信息
        dto.setDevCode(driverDetail.getNodeCode());
        dto.setDevName(driverDetail.getNodeName());

        // 使用公共指标集
        dto.setRuleColId(ConstantBase.RULECOLID_COMMON);
        return dto;
    }

    /**
     * EDU更新数据 - 将DriverProjectDTO转换为RuleDetailUpdateDTO并更新
     *
     * @param driverProjectDTO EDU驱动项目数据
     * @return 更新结果
     */
    @Transactional
    public Boolean eduUpdate(DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        if (driverProjectDTO == null) {
            log.warn("EDU更新数据失败：driverProjectDTO为空");
            return false;
        }

        try {
            // 1. 根据projectCode获取projectId
            Long projectId = getProjectIdByCode(driverProjectDTO.getProjectCode());
            if (projectId == null) {
                log.warn("EDU更新数据失败：未找到项目编码对应的项目ID，projectCode: {}", driverProjectDTO.getProjectCode());
                return false;
            }

            // 2. 转换并更新指标详情
            List<RuleDetailUpdateDTO> ruleDetailUpdateList = convertToRuleDetailUpdateDTOList(driverProjectDTO, projectId);

            if (CollectionUtils.isEmpty(ruleDetailUpdateList)) {
                log.warn("EDU更新数据失败：转换后的指标详情列表为空");
                return false;
            }

            // 3. 批量更新指标详情
            boolean allUpdated = true;
            for (RuleDetailUpdateDTO ruleDetailUpdateDTO : ruleDetailUpdateList) {
                Boolean updated = ruleDetailService.updateVoById(ruleDetailUpdateDTO);
                if (!updated) {
                    log.error("更新指标详情失败：{}", ruleDetailUpdateDTO.getRuleName());
                    allUpdated = false;
                }
            }

            log.info("EDU更新数据完成，项目编码：{}，成功更新指标数量：{}",
                    driverProjectDTO.getProjectCode(), ruleDetailUpdateList.size());
            return allUpdated;

        } catch (Exception e) {
            log.error("EDU更新数据异常：", e);
            throw new RuntimeException("EDU更新数据失败", e);
        }
    }

    /**
     * 将DriverProjectDTO转换为RuleDetailUpdateDTO列表
     *
     * @param driverProjectDTO 驱动项目数据
     * @param projectId 项目ID
     * @return 转换后的指标更新DTO列表
     */
    private List<RuleDetailUpdateDTO> convertToRuleDetailUpdateDTOList(
            DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO, Long projectId) {

        List<RuleDetailUpdateDTO> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(driverProjectDTO.getDriverDetail())) {
            return resultList;
        }

        for (CalcDriverDetailDTO driverDetail : driverProjectDTO.getDriverDetail()) {
            if (CollectionUtils.isEmpty(driverDetail.getDriverData())) {
                continue;
            }

            for (CalcPropDataDTO driverData : driverDetail.getDriverData()) {
                RuleDetailUpdateDTO ruleDetailUpdateDTO = convertDriverDataToRuleDetailUpdate(
                        driverData, driverDetail, projectId);

                if (ruleDetailUpdateDTO != null) {
                    resultList.add(ruleDetailUpdateDTO);
                }
            }
        }

        return resultList;
    }

    /**
     * 将单个驱动数据转换为RuleDetailUpdateDTO
     *
     * @param driverData 驱动数据对象
     * @param driverDetail 驱动详情
     * @param projectId 项目ID
     * @return 转换后的指标更新DTO
     */
    private RuleDetailUpdateDTO convertDriverDataToRuleDetailUpdate(
            CalcPropDataDTO driverData, CalcDriverDetailDTO driverDetail, Long projectId) {

        if (driverData == null) {
            return null;
        }

        // 先查询获取现有记录的ID，使用公共指标集
        RuleDetail existingRule = ruleDetailService.getByDevProperty(driverData.getDevProperty(), projectId, ConstantBase.RULECOLID_COMMON);
        if (existingRule == null) {
            log.warn("未找到对应的指标记录，devProperty: {}, projectId: {}, ruleColId: {}",
                    driverData.getDevProperty(), projectId, ConstantBase.RULECOLID_COMMON);
            return null;
        }

        RuleDetailUpdateDTO dto = new RuleDetailUpdateDTO();

        // 设置ID（必须）
        dto.setId(existingRule.getId());

        // 设置项目ID
        dto.setProjectId(projectId);

        // 设置默认值
        dto.setRuleType(RuleTypeEnum.MODEL.getValue()); // 默认为模型级规则

        // 从driverData设置指标信息
        dto.setRuleName(driverData.getRuleName());
        dto.setRuleDes(driverData.getRuleDes());
        dto.setCalType(Integer.parseInt(driverData.getCalType())); // 使用传入的计算类型
        dto.setDevProperty(driverData.getDevProperty());
        dto.setPropName(driverData.getPropName());
        dto.setRuleFormula(driverData.getRuleFormula());
        dto.setRuleFormulaShow(driverData.getRuleFormulaShow());

        // 从driverDetail设置节点信息
        dto.setDevCode(driverDetail.getNodeCode());
        dto.setDevName(driverDetail.getNodeName());

        // 使用公共指标集
        dto.setRuleColId(ConstantBase.RULECOLID_COMMON);

        return dto;
    }

    /**
     * EDU删除数据 - 根据传入的数据获取库中的ID并删除
     *
     * @param driverProjectDTO EDU驱动项目数据
     * @return 删除结果
     */
    @Transactional
    public Boolean eduDelete(DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        if (driverProjectDTO == null) {
            log.warn("EDU删除数据失败：driverProjectDTO为空");
            return false;
        }

        try {
            // 1. 根据projectCode获取projectId
            Long projectId = getProjectIdByCode(driverProjectDTO.getProjectCode());
            if (projectId == null) {
                log.warn("EDU删除数据失败：未找到项目编码对应的项目ID，projectCode: {}", driverProjectDTO.getProjectCode());
                return false;
            }

            // 2. 收集需要删除的指标ID
            List<Long> idsToDelete = collectIdsToDelete(driverProjectDTO, projectId);

            if (CollectionUtils.isEmpty(idsToDelete)) {
                log.warn("EDU删除数据失败：未找到需要删除的指标");
                return false;
            }

            // 3. 调用ruleDetailService删除
            boolean deleted = ruleDetailService.deleteByIds(idsToDelete);

            if (deleted) {
                log.info("EDU删除数据完成，项目编码：{}，删除指标数量：{}",
                        driverProjectDTO.getProjectCode(), idsToDelete.size());
            } else {
                log.error("EDU删除数据失败，项目编码：{}", driverProjectDTO.getProjectCode());
            }

            return deleted;

        } catch (Exception e) {
            log.error("EDU删除数据异常：", e);
            throw new RuntimeException("EDU删除数据失败", e);
        }
    }

    /**
     * 收集需要删除的指标ID
     *
     * @param driverProjectDTO 驱动项目数据
     * @param projectId 项目ID
     * @return 需要删除的指标ID列表
     */
    private List<Long> collectIdsToDelete(DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO, Long projectId) {
        List<Long> idsToDelete = new ArrayList<>();

        if (CollectionUtils.isEmpty(driverProjectDTO.getDriverDetail())) {
            return idsToDelete;
        }

        for (CalcDriverDetailDTO driverDetail : driverProjectDTO.getDriverDetail()) {
            if (CollectionUtils.isEmpty(driverDetail.getDriverData())) {
                continue;
            }

            for (CalcPropDataDTO driverData : driverDetail.getDriverData()) {
                // 根据devProperty、projectId和公共指标集ID查找现有记录
                RuleDetail existingRule = ruleDetailService.getByDevProperty(
                        driverData.getDevProperty(),
                        projectId,
                        ConstantBase.RULECOLID_COMMON
                );

                if (existingRule != null) {
                    idsToDelete.add(existingRule.getId());
                } else {
                    log.warn("未找到要删除的指标记录，devProperty: {}, projectId: {}",
                            driverData.getDevProperty(), projectId);
                }
            }
        }

        return idsToDelete;
    }

}
