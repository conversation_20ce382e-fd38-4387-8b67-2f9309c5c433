package com.siact.energy.cal.common.pojo.dto.dataTrans;


import com.siact.energy.cal.common.pojo.validator.MqttTopicValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


/**
 * DataTransSubscribeDTO
 *
 * <AUTHOR>
 * @since 2024-05-15 16:16:18
 */
@ApiModel("数据传输订阅DTO")
@Data
public class DataTransSubscribeDTO {

    /**
     * 订阅ID
     */
    @ApiModelProperty(value = "订阅ID，建议生成UUID传递", position = 1, example = "ca71f3f2-49f7-4d87-b63d-1dfa8cf28721")
    @NotBlank(message = "订阅ID不能为空")
    private String subscribeId;

    /**
     * 主机名
     */
    @ApiModelProperty(value = "主机名", position = 2, example = "**************")
    @NotBlank(message = "主机名不能为空")
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口", position = 3, example = "1883")
    @Pattern(message = "端口格式不正确", regexp = "^(0|[1-9]\\d{0,4}|[1-5]\\d{4}|6[0-4]\\d{3}|65[0-4]\\d{2}|655[0-3]\\d|6553[0-5])$")
    private String port;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 4, example = "mqttuser")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 5, example = "mqttuser")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题", position = 6, example = "/#")
    @MqttTopicValidator
    @NotBlank(message = "主题不能为空")
    private String topic;

}

