/*
 Navicat Premium Data Transfer

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 50624 (5.6.24-log)
 Source Host           : **************:3306
 Source Schema         : energy_cal

 Target Server Type    : MySQL
 Target Server Version : 50624 (5.6.24-log)
 File Encoding         : 65001

 Date: 24/05/2024 18:09:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for data_project
-- ----------------------------
DROP TABLE IF EXISTS `data_project`;
CREATE TABLE `data_project`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `project_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编码',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_project_code`(`project_code`) USING BTREE COMMENT '项目编码唯一约束'
) ENGINE = InnoDB AUTO_INCREMENT = 1790310767699787786 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for data_source
-- ----------------------------
DROP TABLE IF EXISTS `data_source`;
CREATE TABLE `data_source`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `database_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源名称',
  `database_ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源ip',
  `database_port` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源端口',
  `db` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '超级表名',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态（0-正常/1-中断），描述数据库的连接状态',
  `user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `jdbc_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库连接串',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id,如果是数仓数据库，id为空',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源描述',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793937602480541699 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for data_trans
-- ----------------------------
DROP TABLE IF EXISTS `data_trans`;
CREATE TABLE `data_trans`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id',
  `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主机名',
  `port` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '端口',
  `user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `topic` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1791646070922645507 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据传输表（mqtt）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rule_col
-- ----------------------------
DROP TABLE IF EXISTS `rule_col`;
CREATE TABLE `rule_col`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `rule_col_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标集名称',
  `rule_col_des` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标集描述',
  `active_state` int(255) NULL DEFAULT NULL COMMENT '是否激活（0-激活,1-未激活）',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793940576980275202 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '指标集表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `rule_detail`;
CREATE TABLE `rule_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `rule_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标名称',
  `rule_des` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标描述',
  `rule_type` int(11) NULL DEFAULT NULL COMMENT '指标类型（0-实例级规则，1-模型级规则）',
  `cal_type` int(11) NULL DEFAULT NULL COMMENT '计算类型（0-数值计算，1-逻辑计算，2-正则表达式）',
  `dev_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点（模型）编码',
  `dev_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点（模型）名称',
  `dev_property` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性编码，模型编码',
  `prop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性（模型）名称',
  `rule_formula` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公式表达式',
  `rule_formula_show` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公式表达式',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id',
  `rule_col_id` bigint(20) NULL DEFAULT NULL COMMENT '所属规则集id',
  `active_state` int(11) NULL DEFAULT NULL COMMENT '是否激活（0-激活,1-未激活）',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(11) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793529158305128451 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '指标详情表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
