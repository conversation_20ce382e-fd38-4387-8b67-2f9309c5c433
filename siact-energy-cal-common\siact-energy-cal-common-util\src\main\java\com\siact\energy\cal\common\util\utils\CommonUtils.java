package com.siact.energy.cal.common.util.utils;/**
 * @Package com.siact.energycal.utils
 * @description: 常用工具类
 * <AUTHOR>
 * @create 2024/7/13 15:07
 */

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyKey;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CommonUtils
 * @Description 常用工具类
 * <AUTHOR>
 * @Date 2024/7/13 15:07
 * @Version 1.0
 **/

public class CommonUtils {
    public static void addMap(Map<String, Map<DevPropertyKey, DevPropertyValue>> outerMap, String outerKey, DevPropertyKey innerKey, DevPropertyValue value) {
        outerMap.computeIfAbsent(outerKey, k -> new HashMap<>()).put(innerKey, value);
    }

    public static JSONArray returnResultHandler(Object returnObj){
        JSONArray data = null;
        if (returnObj != null) {
            JSONObject result = JSONObject.parseObject(returnObj.toString());

            data = result.getJSONArray("data");
            if (result.get("code").equals("200")) {
                return data;
            }
        }
        return data;
    }

    public static void addDataToMap(Map<String, Map<String, List<String>>> nodeMap, String outKey, String innerKey, List<String> value) {
        nodeMap.computeIfAbsent(outKey, k -> new HashMap<>()).merge(innerKey, value, (existing, newValue) -> {
            existing.addAll(newValue);
            return existing;
        });
    }

    public static void putDataToMap(Map<String, Map<DevPropertyKey, String>> propValueMap,
                                    String projectcode,
                                    String devcode,
                                    String propcode,
                                    String value) {
        // 创建 DevPropertyKey
        DevPropertyKey key = new DevPropertyKey(devcode, propcode);

        // 获取或创建内层 Map
        Map<DevPropertyKey, String> innerMap = propValueMap.computeIfAbsent(projectcode, k -> new HashMap<>());

        // 将数据放入内层 Map
        innerMap.put(key, value);
    }

    public static void addFormulaData(Map<String, Map<Integer, List<String>>> dataStructure, String projectCode, Integer type, String indicator) {
        // 获取或创建项目编码对应的内部映射
        Map<Integer, List<String>> typeMap = dataStructure.computeIfAbsent(projectCode, k -> new HashMap<>());

        // 获取或创建计算类型的列表
        List<String> indicators = typeMap.computeIfAbsent(type, k -> new ArrayList<>());

        // 将计算指标属性添加到列表中
        indicators.add(indicator);
    }

}
