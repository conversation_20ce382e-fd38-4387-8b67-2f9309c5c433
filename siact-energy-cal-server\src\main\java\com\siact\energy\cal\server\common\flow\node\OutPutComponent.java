package com.siact.energy.cal.server.common.flow.node;

import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import com.siact.energy.cal.server.service.flow.impl.ApiRtsServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据输出算子
 * @Version: 1.0
 */
@Component("OutPutNode")
@Slf4j
public class OutPutComponent extends NodeComponent {

    @Resource
    HikariDataSource dataSource;

    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    ApiRtsServiceImpl apiRtsService;

    @Override
    public void process(){
        ResultContext resultContext = this.getContextBean("resultContext");
        String tableName = UUIDUtils.uuidOutTableName();
        flowViewService.insertFlowTable(Integer.parseInt(this.getChainId()), tableName, this.getTag(), resultContext.getTaskId());

        //创输出表
        DBTools.executeSql("create table " + tableName + " select * from " + resultContext.getResultTable() + ";", dataSource);

        ApiConfigEntity apiConfig = apiRtsService.getById(resultContext.getApiId());
        if (apiConfig != null){
            apiConfig.setTableId(tableName);
            //更新流程表
            apiRtsService.updateById(apiConfig);
        }
    }
}
