package com.siact.energy.cal.server.common.mqtt;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqttResHandler {

    public void deal(Message msg){
        //根据messageId判断是否是本机发送的消息
        String msgId = msg.getMessageId();
        log.info("获取消息的messageId:{}", msgId);
        if(DefaultFuture.contains(msgId)){
            DefaultFuture.received(msg);
        }
    }
}

