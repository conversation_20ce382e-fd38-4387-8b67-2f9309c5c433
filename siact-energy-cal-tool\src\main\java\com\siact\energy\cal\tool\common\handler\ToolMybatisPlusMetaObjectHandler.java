package com.siact.energy.cal.tool.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.siact.energy.cal.tool.service.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * Tool模块专用的MyBatis Plus自动填充处理器
 * 处理BaseEntity的自动填充：createTime, updateTime, deleted, creator, updater
 * 使用BaseService.getLoginUserId()自动填充creator和updater字段
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class ToolMybatisPlusMetaObjectHandler implements MetaObjectHandler {

    @Autowired
    private BaseService baseService;

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();
        strictInsertFill(metaObject, "createTime", Date.class, date);
        strictInsertFill(metaObject, "updateTime", Date.class, date);
        strictInsertFill(metaObject, "deleted", Integer.class, 0);
        
        // 自动填充creator和updater字段
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            strictInsertFill(metaObject, "creator", Long.class, currentUserId);
            strictInsertFill(metaObject, "updater", Long.class, currentUserId);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        
        // 自动填充updater字段
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            strictUpdateFill(metaObject, "updater", Long.class, currentUserId);
        }
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }

    /**
     * 获取当前登录用户ID
     * 使用BaseService.getLoginUserId()方法
     */
    private Long getCurrentUserId() {
        try {
            return baseService.getLoginUserId();
        } catch (Exception e) {
            log.debug("获取当前登录用户失败: {}", e.getMessage());
            return null;
        }
    }
}
