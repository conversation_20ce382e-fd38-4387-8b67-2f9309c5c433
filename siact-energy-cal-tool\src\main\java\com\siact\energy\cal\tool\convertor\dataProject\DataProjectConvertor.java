package com.siact.energy.cal.tool.convertor.dataProject;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siact.api.common.api.vo.project.TMProjectInsSimpleVo;

import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectUpdateDTO;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.tool.entity.dataProject.DataProject;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 项目表(DataProject) Convertor
 *
 * <AUTHOR>
 * @since 2024-05-14 15:27:00
 */
@Mapper
public interface DataProjectConvertor extends AbstractConvertor<DataProjectVO, DataProject> {

    DataProjectConvertor INSTANCE = Mappers.getMapper(DataProjectConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<DataProjectVO> entityPage2VoPageBean(Page<DataProject> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<DataProject> voPageBean2EntityPage(PageBean<DataProjectVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataProject queryDTO2Entity(DataProjectQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataProject insertDTO2Entity(DataProjectInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataProject updateDTO2Entity(DataProjectUpdateDTO dto);

    /**
     * 数字孪生项目集合转为项目VO集合
     *
     * @param insSimpleVos 数字孪生项目集合
     * @return 项目VO集合
     */
    List<DataProjectVO> digitalTwinProjects2VOs(List<TMProjectInsSimpleVo> insSimpleVos);

    /**
     * 数字孪生项目转为项目VO
     *
     * @param insSimpleVo 数字孪生项目
     * @return 项目VO
     */
    @Mapping(source = "proName", target = "projectName")
    @Mapping(source = "dataCode", target = "projectCode")
    DataProjectVO digitalTwinProject2VO(TMProjectInsSimpleVo insSimpleVo);

}
