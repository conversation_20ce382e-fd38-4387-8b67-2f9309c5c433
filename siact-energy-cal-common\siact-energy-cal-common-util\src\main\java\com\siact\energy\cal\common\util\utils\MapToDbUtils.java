package com.siact.energy.cal.common.util.utils;

import cn.hutool.extra.spring.SpringUtil;

import com.siact.energy.cal.common.core.exception.BizException;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-03
 * @Description: map数据存如数据库
 * @Version: 1.0
 */
@Slf4j
public class MapToDbUtils {

    static HikariDataSource dataSource = SpringUtil.getBean(HikariDataSource.class);

    public static void mapToDb(List<Map<String, String>> list, String tableId){
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            statement = connection.createStatement();
            Map<String, List<Map<String, String>>> ts = list.stream().collect(Collectors.groupingBy(iteam -> iteam.get("ts"), Collectors.toList()));
            for (Map.Entry<String, List<Map<String, String>>> stringListEntry : ts.entrySet()) {
                StringBuilder sql = new StringBuilder();
                sql.append("insert into ").append(tableId);
                List<Map<String, String>> value = stringListEntry.getValue();
                StringBuilder keySb = new StringBuilder();
                StringBuilder valSb = new StringBuilder() ;
                keySb.append("ts").append(",");
                valSb.append("'").append(stringListEntry.getKey()).append("',");
                value.forEach(i->{
                    for (Map.Entry<String, String> entry1 : i.entrySet()) {
                        if (Objects.equals(entry1.getKey(), "ts")){
                            continue;
                        }
                        keySb.append(entry1.getKey()).append(",");
                        valSb.append("'").append(entry1.getValue()).append("',");
                    }
                });
                keySb.deleteCharAt(keySb.lastIndexOf(","));
                valSb.deleteCharAt(valSb.lastIndexOf(","));
                sql.append(" (").append(keySb).append(") values (").append(valSb).append(")");
                statement.addBatch(sql.toString());
            }
            statement.executeBatch();
            statement.clearBatch();
            connection.setAutoCommit(true);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }finally {
            try {
                assert statement != null;
                statement.close();
                DBBaseHandler.closeDB(connection);
            } catch (SQLException e) {
                throw new BizException("关闭数据源失败");
            }
        }

    }

    /**
     * 创建临时表
     * @param tableId 临时表id
     * @param fields 临时表字段
     */
    public static void createTale(String tableId, Set<String> fields){
        StringBuilder sb = new StringBuilder();
        sb.append("create table ").append(tableId).append("( ts varchar(255) ,");
        for (String field : fields) {
            sb.append(field).append(" DECIMAL(30,10),");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        sb.append(")");
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            DBTools.executeSql(connection, sb.toString());
        }catch (Exception e){
            log.error("sql执行失败", e);
            throw new BizException("DataSourceNode：sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }


    /**
     * 创建临时表
     * @param tableId 临时表id
     * @param fields 临时表字段
     */
    public static void createTaleByFields(String tableId, Set<String> fields){
        StringBuilder sb = new StringBuilder();
        sb.append("create table ").append(tableId).append("(");
        for (String field : fields) {
            sb.append(field).append(" text,");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        sb.append(")");
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            DBTools.executeSql(connection, sb.toString());
        }catch (Exception e){
            log.error("sql执行失败", e);
            throw new BizException("DataSourceNode：sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }

    public static void insertDbList(List<Map<String, Object>> list, String tableId){
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            statement = connection.createStatement();

            for (Map<String, Object> iteam : list){
                StringBuilder sql1 = new StringBuilder();
                sql1.append("insert into ").append(tableId);
                sql1.append("( ts").append(",");
                sql1.append(" PGY02_SSXT1_ST00000_U00000_EQ000000000_MP0000 ) VALUES (");
                sql1.append("'").append(iteam.get("ts")).append("','").append(iteam.get("value")).append("')");
                statement.addBatch(sql1.toString());
                System.out.println(sql1);
            }
            statement.executeBatch();
            statement.clearBatch();
            connection.setAutoCommit(true);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new BizException("插入数据失败:"+tableId);
        }finally {
            try {
                assert statement != null;
                statement.close();
                DBBaseHandler.closeDB(connection);
            } catch (SQLException e) {
                throw new BizException("关闭数据源失败");
            }
        }

    }

    public static void insertDbMap(List<Map<String, String>> list, String tableId, Map<String, List<Map<String, String>>> map){
        Statement statement = null;
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            statement = connection.createStatement();
            for (Map<String, String> item : list){
                StringBuilder sql1 = new StringBuilder();
                sql1.append("insert into ").append(tableId);
                sql1.append("( ts").append(",");
                sql1.append("value ) VALUES (");
                sql1.append("'").append(item.get("ts")).append("','").append(item.get("value")).append("')");
                statement.addBatch(sql1.toString());
                System.out.println(sql1);
            }

            statement.executeBatch();
            statement.clearBatch();
            connection.setAutoCommit(true);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }finally {
            try {
                assert statement != null;
                statement.close();
                DBBaseHandler.closeDB(connection);
            } catch (SQLException e) {
                throw new BizException("关闭数据源失败");
            }
        }

    }

    /**
     * 根据map某个key来合并两个list
     * @param m1 第一个list
     * @param m2 第二个list
     * @param key
     * @return
     */
    public static List<Map<String,Object>> merge(List<Map<String,Object>> m1,List<Map<String,Object>> m2, String key){
        m1.addAll(m2);
        Set<String> set = new HashSet<>();
        return getMaps(key, m1, set);

    }

    /**
     * 根据map某个key来合并多个list
     * @param list 多个list
     * @param key 制定的key
     * @return
     */
    public static List<Map<String,Object>> mergeList(List<List<Map<String,Object>>> list, String key){
        List<Map<String,Object>> mergeResult = new ArrayList<>();
        for (List<Map<String, Object>> maps : list) {
            mergeResult.addAll(maps);
        }
        Set<String> set = new HashSet<>();
        return getMaps(key, mergeResult, set);

    }

    private static List<Map<String, Object>> getMaps(String key, List<Map<String, Object>> mergeResult, Set<String> set) {
        return mergeResult.stream().collect(Collectors.groupingBy(o -> {
            //暂存所有key
            set.addAll(o.keySet());
            return o.get(key);
        })).values().stream().map(maps -> {
            //合并
            Map<String, Object> map = maps.stream().flatMap(m -> {
                return m.entrySet().stream();
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> b));
            //为没有key的赋值0
            set.forEach(k -> {
                if (!map.containsKey(k))
                    map.put(k, 0);
            });
            return map;
        }).collect(Collectors.toList());
    }

    public static void insertByMap(String tableId, Map<String, Object> map) {
        StringBuilder sql = new StringBuilder();
        sql.append("insert into ").append(tableId).append(" values(");
        for ( String key : map.keySet()) {
            sql.append("'").append(map.get(key)).append("',");
        }
        sql.deleteCharAt(sql.lastIndexOf(","));
        sql.append(")");
        Connection connection = null;
        try {
            dataSource.getConnection();
            DBTools.executeSql(connection, sql.toString());
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
