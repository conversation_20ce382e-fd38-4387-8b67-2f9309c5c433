package com.siact.energy.cal.common.pojo.dto.dataSource;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 数据源表(DataSource) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:32
 */
@ApiModel("数据源表更新DTO")
@Data
public class DataSourceUpdateDTO extends DataSourceInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

