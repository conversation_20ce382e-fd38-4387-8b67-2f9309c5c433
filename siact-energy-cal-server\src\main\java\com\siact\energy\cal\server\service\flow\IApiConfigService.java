package com.siact.energy.cal.server.service.flow;


import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ApiConfigDto;
import com.siact.energy.cal.common.pojo.vo.flow.ApiConfigVO;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
public interface IApiConfigService extends IService<ApiConfigEntity> {

    /**
     * * 添加或修改api配置
     * @param apiConfigDto api接口配置
     * @return 接口id
     */
    String addModifyApiConfig(ApiConfigDto apiConfigDto);

    /**
     * * 删除api配置
     * @param id api接口id
     * @return success -成功， false-失败
     */
    String deleteApiConfig(String id);

    ApiConfigVO getApiConfig(String id);

    List<ApiConfigVO> getApiConfigList(PageBean<ApiConfigVO> page, ApiConfigDto apiConfigDto);
}
