package com.siact.energy.cal.common.pojo.vo.flow;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 组件流定义
 * @Version: 1.0
 */
@Data
@ApiModel("组件流")
public class ComponentFlowVO {

    @ApiModelProperty(value = "组件流id", position = 1, example = "1")
    private Integer id;

    @ApiModelProperty(value = "组件流名称", position = 2, example = "组件流名称")
    private String flowName;

    @ApiModelProperty(value = "组件流详情", position = 3, example = "组件流详情")
    private String context;

    @ApiModelProperty(value = "组件流状态", position = 4, example = "0")
    private Integer deleted;

    @ApiModelProperty(value = "创建时间", position = 5, example = "2024-05-29 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", position = 6, example = "2024-05-29 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "创建人", position = 7, example = "admin")
    private String createName;

    @ApiModelProperty(value = "创建人id", position = 8, example = "1")
    private String createId;
    @ApiModelProperty(value = "组件流描述", position = 9)
    private String dec;

    @ApiModelProperty(value = "组件流状态:0:未运行,1:运行中,2:成功,3:失败", position = 10)
    private String status;
}
