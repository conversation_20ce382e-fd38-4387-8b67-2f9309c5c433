<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.flow.FlowTableMapper">

    <select id="getTableCount" parameterType="string" resultType="int">
        SELECT count(*) FROM information_schema.tables
                        WHERE table_schema = 'test'
                          AND table_name = #{tableName}
                          AND table_type = 'BASE TABLE'

    </select>

    <insert id="saveItemInput" parameterType="java.util.Map">
        insert into ${tableName} (
        <foreach collection="map" item="value" index="key" separator=",">
            ${key}
        </foreach>
        )
        values (
        <foreach collection="map" item="value" index="key" separator=",">
            #{value}
        </foreach>
        )
    </insert>


</mapper>