// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbsynch;

import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.common.util.DatabaseAwareUtils;
import com.siact.energy.cal.dbsynch.dm.DmDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.kingbase.KingbaseDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.mssql.SqlServerDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.mysql.MySqlDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.oracle.OracleDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.oscar.OscarDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.pgsql.PostgresqlDatabaseSyncImpl;
import com.siact.energy.cal.dbsynch.sqlite.Sqlite3DatabaseSyncImpl;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 数据库同步器构造工厂类
 *
 * <AUTHOR>
 */
public final class DatabaseSynchronizeFactory {

	private static final Map<ProductTypeEnum, Function<DataSource, IDatabaseSynchronize>> DATABASE_SYNC_MAPPER
			= new HashMap<ProductTypeEnum, Function<DataSource, IDatabaseSynchronize>>() {

		private static final long serialVersionUID = -2359773637275934408L;

		{
			put(ProductTypeEnum.MYSQL, MySqlDatabaseSyncImpl::new);
			put(ProductTypeEnum.ORACLE, OracleDatabaseSyncImpl::new);
			put(ProductTypeEnum.SQLSERVER, SqlServerDatabaseSyncImpl::new);
			put(ProductTypeEnum.SQLSERVER2000, SqlServerDatabaseSyncImpl::new);
			put(ProductTypeEnum.POSTGRESQL, PostgresqlDatabaseSyncImpl::new);
			put(ProductTypeEnum.DM, DmDatabaseSyncImpl::new);
			put(ProductTypeEnum.KINGBASE, KingbaseDatabaseSyncImpl::new);
			put(ProductTypeEnum.TDENGINE, OscarDatabaseSyncImpl::new);
			put(ProductTypeEnum.GBASE8A, MySqlDatabaseSyncImpl::new);
			put(ProductTypeEnum.SQLITE3, Sqlite3DatabaseSyncImpl::new);
		}
	};

	/**
	 * 获取指定数据源的同步器
	 *
	 * @param dataSource 数据源
	 * @return 同步器对象
	 */
	public static IDatabaseSynchronize createDatabaseWriter(DataSource dataSource) {
		ProductTypeEnum type = DatabaseAwareUtils.getDatabaseTypeByDataSource(dataSource);
		if (!DATABASE_SYNC_MAPPER.containsKey(type)) {
			throw new RuntimeException(
					String.format("[dbsynch] Unsupported database type (%s)", type));
		}

		return DATABASE_SYNC_MAPPER.get(type).apply(dataSource);
	}
}
