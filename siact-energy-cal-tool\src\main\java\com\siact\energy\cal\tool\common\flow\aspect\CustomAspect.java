package com.siact.energy.cal.tool.common.flow.aspect;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.ComFlowStatusEnum;
import com.siact.energy.cal.tool.service.flow.impl.ProcessServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-04
 * @Description: 组件切面
 * @Version: 1.0
 */
@Aspect
@Component
@Slf4j
public class CustomAspect {

    @Resource
    ProcessServiceImpl processService;

    @Pointcut("execution(*  com.siact.energy.cal.server.common.flow.node.*.process())")
    public void cut() {
    }

    @Before("execution(*  com.yomahub.liteflow.core.FlowExecutor.execute2Resp(..))")
    public void before(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args.length == 0) {
            throw new BizException("args is null");
        }
        String flowId = (String) args[0];
        log.info("flowId: {}", flowId);
        //更新组件状态为运行中
        processService.updateComponentStatus(flowId, ComFlowStatusEnum.RUNNING.getValue());

    }


    @Around("cut()")
    public Object around(ProceedingJoinPoint jp) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object returnObj = jp.proceed();
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        log.info("{} executed in {} ms", jp.getSignature(), executionTime);
        return returnObj;
    }
}
