// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbwriter.kingbase;

import com.siact.energy.cal.dbwriter.IDatabaseWriter;
import com.siact.energy.cal.dbwriter.gpdb.GreenplumInsertWriterImpl;

import javax.sql.DataSource;

/**
 * Kingbase8数据库Insert写入实现类
 *
 * <AUTHOR>
 */
public class KingbaseInsertWriterImpl extends GreenplumInsertWriterImpl implements IDatabaseWriter {

  public KingbaseInsertWriterImpl(DataSource dataSource) {
    super(dataSource);
  }

  @Override
  protected String getDatabaseProductName() {
    return "Kingbase";
  }

}
