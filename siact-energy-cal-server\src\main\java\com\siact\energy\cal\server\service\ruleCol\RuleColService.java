package com.siact.energy.cal.server.service.ruleCol;


import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColUpdateDTO;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.server.entity.ruleCol.RuleCol;

import java.io.Serializable;
import java.util.List;

/**
 * 指标集表(RuleCol)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:13
 */
public interface RuleColService extends IService<RuleCol> {

    /**
     * 注册列表
     *
     * @param page            分页对象
     * @param ruleColQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<RuleColVO> listPage(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    RuleColVO getVoById(Serializable id);

    /**
     * 新增数据
     *
     * @param ruleColInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(RuleColInsertDTO ruleColInsertDTO);

    /**
     * 修改数据
     *
     * @param ruleColUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(RuleColUpdateDTO ruleColUpdateDTO);

    /**
     * 激活/未激活
     *
     * @param activeState 激活/未激活
     * @param ids  主键集合
     * @return 默认成功返回
     */
    boolean toggle(Integer activeState, List<Long> ids);

    /**
     * 列表(全部)
     *
     * @return 查询结果
     */
    List<RuleColVO> listAll();

    void updateAttrList(Long ruleColId, List<AttrObject> list);
}

