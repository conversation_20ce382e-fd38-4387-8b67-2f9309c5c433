package com.siact.energy.cal.common.pojo.vo.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-12
 * @Description:
 * @Version: 1.0
 */
@ApiModel("接口配置表VO")
@Data
public class ApiConfigVO {

    /**
     * id
     */
    @ApiModelProperty(value = "id", position = 1, example = "1")
    private Long id;

    /**
     * api名称
     */
    @ApiModelProperty(value = "api名称", position = 2, example = "api名称")
    private String apiName;

    /**
     * api地址
     */
    @ApiModelProperty(value = "api地址", position = 3, example = "/url")
    private String apiUrl;

    /**
     * 请求类型
     */
    @ApiModelProperty(value = "请求类型", position = 4, example = "Post")
    private String requestType;

    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述", position = 5, example = "请求接口")
    private String remark;

//    /**
//     * 绑定组件流id
//     */
//    @ApiModelProperty(value = "绑定组件流id", position = 6, example = "2")
//    private Long flowId;

    /**
     * 输出表id
     */
    @ApiModelProperty(value = "输出表id", position = 7, example = "1")
    private String tableId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "输出表id", position = 8, example = "2024-06-12 10:34:45")
    private String createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 9, example = "admin")
    private String createName;
}
