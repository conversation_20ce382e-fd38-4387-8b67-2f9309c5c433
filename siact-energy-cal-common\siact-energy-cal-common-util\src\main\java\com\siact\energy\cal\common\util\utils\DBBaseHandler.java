package com.siact.energy.cal.common.util.utils;


import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import java.sql.Connection;
import java.sql.DriverManager;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据库初始化工具类
 * @Version: 1.0
 */
@Slf4j
public class DBBaseHandler {


    public static Connection initDMConnection(String jdbcurl, String username, String password, boolean autoCommit) {
        Connection conn = null;
        try {
            Class.forName(DbTypeEnum.DM.getDriverName());
            conn = DriverManager.getConnection(jdbcurl, username, password);
            conn.setAutoCommit(autoCommit);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return conn;
    }

    public static Connection initMySQLConnection(String jdbcurl, String username, String password, boolean autoCommit) {
        Connection conn = null;
        try {
            Class.forName(DbTypeEnum.Mysql.getDriverName());
            conn = DriverManager.getConnection(jdbcurl, username, password);
            conn.setAutoCommit(autoCommit);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return conn;
    }

    public static Connection initOracleConnection(String jdbcurl, String username, String password, boolean autoCommit) {
        Connection conn = null;
        try {
            Class.forName(DbTypeEnum.oracle.getDriverName());
            conn = DriverManager.getConnection(jdbcurl, username, password);
            conn.setAutoCommit(autoCommit);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return conn;
    }


    public static Connection initPOSTGREPConnection(String jdbcurl, String username, String password, boolean autoCommit) {
        Connection conn = null;
        try {
            Class.forName(DbTypeEnum.postgresql.getDriverName());
            conn = DriverManager.getConnection(jdbcurl, username, password);
            conn.setAutoCommit(autoCommit);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return conn;
    }



    public static Connection initTaoSConnection(String jdbcUrl) {
        Connection conn = null;
        try {
            Class.forName(DbTypeEnum.TaoS.getDriverName());
            conn = DriverManager.getConnection(jdbcUrl);
            conn.setAutoCommit(true);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(e.getMessage());
        }
        return conn;
    }

    public static void closeDB(Connection conn) {
        try {
            if (conn != null){
                conn.close();
            }
        } catch (Exception e) {
            log.error("关闭连接失败,{}",e.getMessage(), e);
        }
    }
}
