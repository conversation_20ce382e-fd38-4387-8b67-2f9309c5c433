package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.text.StrFormatter;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;

import java.sql.Connection;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据库连接类
 * @Version: 1.0
 */
public class DBConnection {

    public static Connection connection(String dbtype, String ip, String port, String dbname, String username, String password) {
        Connection conn = null;
        try {
            String jdbcurl = "";
            if (DbTypeEnum.DM.getDbType().equalsIgnoreCase(dbtype)) {
                jdbcurl =  StrFormatter.format("jdbc:dm://{}:{}/{}", ip, port, dbname);
                conn = DBBaseHandler.initDMConnection(jdbcurl, username, password, false);
            } else if (DbTypeEnum.Mysql.getDbType().equalsIgnoreCase(dbtype)) {
                jdbcurl =  StrFormatter.format("jdbc:mysql://{}:{}/{}?useSSL=false&serverTimezone=UTC", ip, port, dbname);
                conn = DBBaseHandler.initMySQLConnection(jdbcurl, username, password, false);
            } else if (DbTypeEnum.oracle.getDbType().equalsIgnoreCase(dbtype)) {
                jdbcurl =  StrFormatter.format("jdbc:oracle:thin:@{}:{}:{}", ip, port, dbname);
                conn = DBBaseHandler.initOracleConnection(jdbcurl, username, password, false);
            } else if (DbTypeEnum.postgresql.getDbType().equalsIgnoreCase(dbtype)) {
                jdbcurl =  StrFormatter.format("jdbc:postgresql://{}:{}/{}", ip, port, dbname);
                conn = DBBaseHandler.initPOSTGREPConnection(jdbcurl, username, password, false);
            } else if (DbTypeEnum.TaoS.getDbType().equalsIgnoreCase(dbtype)) {
                jdbcurl =  StrFormatter.format("jdbc:TAOS-RS://{}:{}/{}?user={}&password={}&useUnicode=true&characterEncoding=utf-8&serverTimezone=UTC",
                        ip, port, dbname, username, password);
                conn = DBBaseHandler.initTaoSConnection(jdbcurl);
            }
            else {
                return null;
            }
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
        return conn;
    }

    public static void close(Connection conn) {
        if (conn != null)
            DBBaseHandler.closeDB(conn);
    }
}
