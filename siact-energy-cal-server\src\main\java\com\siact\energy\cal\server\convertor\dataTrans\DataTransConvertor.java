package com.siact.energy.cal.server.convertor.dataTrans;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.siact.energy.cal.server.entity.dataTrans.DataTrans;
import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.dataTrans.DataTransVO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransUpdateDTO;

/**
 * 数据传输表（mqtt）(DataTrans) Convertor
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:36
 */
@Mapper
public interface DataTransConvertor extends AbstractConvertor<DataTransVO, DataTrans> {

    DataTransConvertor INSTANCE = Mappers.getMapper(DataTransConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<DataTransVO> entityPage2VoPageBean(Page<DataTrans> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<DataTrans> voPageBean2EntityPage(PageBean<DataTransVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataTrans queryDTO2Entity(DataTransQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataTrans insertDTO2Entity(DataTransInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    DataTrans updateDTO2Entity(DataTransUpdateDTO dto);

}
