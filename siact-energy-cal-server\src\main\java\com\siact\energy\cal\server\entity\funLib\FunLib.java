package com.siact.energy.cal.server.entity.funLib;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import com.siact.energy.cal.common.datasource.common.BaseEntity;

import java.util.List;

/**
 * 常用函数库(FunLib)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-11 11:04:26
 */
@Data
@TableName(value = "fun_lib", autoResultMap = true)
public class FunLib extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 函数名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String value;

    /**
     * 函数展示名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String label;

    /**
     * 函数组件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> pieces;

    /**
     * 描述
     */
    @TableField(condition = SqlCondition.LIKE)
    private String description;

    /**
     * 计算类型（1-基础指标，2-聚合指标）
     */
    private Integer calType;

}

