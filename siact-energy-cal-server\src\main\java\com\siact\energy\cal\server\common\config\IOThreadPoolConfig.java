package com.siact.energy.cal.server.common.config;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * IOThreadPoolConfig
 *
 * <AUTHOR>
 * @since 2024-05-22 08:42:30
 */
@Slf4j
@Configuration
public class IOThreadPoolConfig {

    /**
     * IO线程池名称
     */
    public static final String IO_THREAD_POOL_NAME = "ioThreadPool";

    @Bean(IO_THREAD_POOL_NAME)
    public Executor getThreadPoolExecutor(TaskThreadPoolParamConfig config) {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(config.getCorePoolSize());
        // 最大线程数
        executor.setMaxPoolSize(config.getMaxPoolSize());
        // 阻塞队列大小
        executor.setQueueCapacity(config.getQueueCapacity());
        // 保持时间
        executor.setKeepAliveSeconds(config.getKeepAliveSeconds());
        // 线程默认前缀
        executor.setThreadNamePrefix(config.getThreadNamePrefix());
        // 拒绝策略
        ThreadPoolExecutor.CallerRunsPolicy runsPolicy = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(runsPolicy);
        // 传递MDC上下文
        executor.setTaskDecorator(runnable -> {
            Optional<Map<String, String>> copyOfContextMapOptional = Optional.ofNullable(MDC.getCopyOfContextMap());
            return () -> {
                try {
                    copyOfContextMapOptional.ifPresent(MDC::setContextMap);
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        });
        // 初始化线程池
        executor.initialize();

        log.info("IO线程池初始化完毕，核心线程数:{},最大线程数:{},任务队列最大长度:{},任务等待超时时间:{}s,拒绝策略:{}",
                config.getCorePoolSize(), config.getMaxPoolSize(), config.getQueueCapacity(), config.getKeepAliveSeconds(), runsPolicy.getClass());

        return executor;
    }
}
