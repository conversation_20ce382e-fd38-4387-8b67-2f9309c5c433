package com.siact.energy.cal.server.service.dataSource;


import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.List;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.common.pojo.enums.DataSourceStatusEnum;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.entity.dataSource.DataSource;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceUpdateDTO;

import javax.validation.constraints.NotNull;

/**
 * 数据源表(DataSource)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:11
 */
public interface DataSourceService extends IService<DataSource> {

    /**
     * 注册列表
     *
     * @param page               分页对象
     * @param dataSourceQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<DataSourceVO> listPage(PageBean<DataSourceVO> page, DataSourceQueryDTO dataSourceQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    DataSourceVO getVoById(Serializable id);

    /**
     * 新增数据
     *
     * @param dataSourceInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(DataSourceInsertDTO dataSourceInsertDTO);

    /**
     * 修改数据
     *
     * @param dataSourceUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(DataSourceUpdateDTO dataSourceUpdateDTO);

    /**
     * 数据源测试
     *
     * @param dataSourceTestDTO 实体对象
     */
    void dbTest(DataSourceTestDTO dataSourceTestDTO);

    /**
     * 数据源测试(使用ID)
     *
     * @param id 主键
     */
    void dbTestById(Serializable id);

    /**
     * 更新数据源的状态
     *
     * @param id                   数据源ID
     * @param dataSourceStatusEnum 数据源状态
     * @return 更新结果
     */
    boolean updateDataSourceStatusById(@NotNull Serializable id,
                                       @NotNull DataSourceStatusEnum dataSourceStatusEnum);

    /**
     * 数据源检查
     */
    void dataSourceCheck();

    List<DataSourceVO> listAll();

    List<DataSourceVo> getAllDataSource();

    DataSourceVo getDataSourceConfig(String projectCode);
}

