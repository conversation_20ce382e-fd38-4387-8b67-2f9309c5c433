package com.siact.energy.cal.common.pojo.validator;

import lombok.extern.slf4j.Slf4j;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.io.UnsupportedEncodingException;
import java.lang.annotation.*;
import java.util.Objects;

/**
 * MqttTopicValidator
 *
 * <AUTHOR>
 * @since 2024-05-24 08:59:50
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Constraint(validatedBy = MqttTopicValidator.MqttTopicValidatorHandler.class)
public @interface MqttTopicValidator {

    /**
     * 是否是主题筛选器
     *
     * mqtt订阅的时候是主题筛选器(可以有通配符)，发送消息的时候是主题(不能有通配符)
     * @return true：是, false：否，默认true
     */
    boolean topicFilter() default true;

    /**
     * 消息
     *
     * @return 消息内容
     */
    String message() default "mqtt主题格式不正确";

    /**
     * 分组
     *
     * @return 分组
     */
    Class<?>[] groups() default {};

    /**
     * 有效载荷
     *
     * @return
     */
    Class<? extends Payload>[] payload() default {};

    @Slf4j
    class MqttTopicValidatorHandler implements ConstraintValidator<MqttTopicValidator, String> {

        /**
         * 是否主题筛选器
         */
        private boolean topicFilter;

        @Override
        public void initialize(MqttTopicValidator mqttTopicValidator) {
            ConstraintValidator.super.initialize(mqttTopicValidator);
            topicFilter = mqttTopicValidator.topicFilter();
        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {

            if(Objects.nonNull(value) && !value.isEmpty()) {
                try {
                    validate(value, topicFilter);
                    return true;
                } catch (IllegalArgumentException e) {
                    // 禁用默认的错误消息
                    context.disableDefaultConstraintViolation();
                    // 设置错误提示语
                    context.buildConstraintViolationWithTemplate(e.getMessage()).addConstraintViolation();
                    return false;
                }
            }
            // 如果是空直接放过
            return true;
        }

        /**
         * MQTT主题分隔符
         */
        public static final String TOPIC_LEVEL_SEPARATOR = "/";

        /**
         * MQTT主题多级通配符
         */
        public static final String MULTI_LEVEL_WILDCARD = "#";

        /**
         * MQTT主题单级通配符
         */
        public static final String SINGLE_LEVEL_WILDCARD = "+";

        /**
         * MQTT 主题通配 (/#)
         */
        public static final String MULTI_LEVEL_WILDCARD_PATTERN = TOPIC_LEVEL_SEPARATOR + MULTI_LEVEL_WILDCARD;

        /**
         * MQTT 主题通配 (#+)
         */
        public static final String TOPIC_WILDCARDS = MULTI_LEVEL_WILDCARD + SINGLE_LEVEL_WILDCARD;

        /**
         * MQTT 主题最小长度
         */
        private static final int MIN_TOPIC_LEN = 1;

        /**
         * MQTT 主题最大长度
         */
        private static final int MAX_TOPIC_LEN = 65535;

        /**
         * 空白字符
         */
        private static final char NUL = '\u0000';

        /**
         * org.eclipse.paho.client.mqttv3.MqttTopic#validate(java.lang.String, boolean)
         *
         * @param topicFilter 待校验主题
         * @param wildcardAllowed 允许通配符
         * @throws IllegalArgumentException 异常
         */
        public static void validate(String topicFilter, boolean wildcardAllowed) throws IllegalArgumentException {
            int topicLen = 0;
            try {
                topicLen = topicFilter.getBytes("UTF-8").length;
            } catch (UnsupportedEncodingException e) {
                throw new IllegalStateException(e.getMessage());
            }

            // 主题过滤器长度1~65535
            if (topicLen < MIN_TOPIC_LEN || topicLen > MAX_TOPIC_LEN) {
                throw new IllegalArgumentException(String.format("无效的主题长度, 长度应该在范围[%d, %d]!",
                        new Object[] { Integer.valueOf(MIN_TOPIC_LEN), Integer.valueOf(MAX_TOPIC_LEN) }));
            }

            // 主题筛选器字符串，可以包含通配符
            if (wildcardAllowed) {
                // 只有 # 或 +
                if (Strings.equalsAny(topicFilter, new String[] { MULTI_LEVEL_WILDCARD, SINGLE_LEVEL_WILDCARD })) {
                    return;
                }

                // 1) 检查多级通配符
                // 规则:
                // 多级通配符只能单独指定，也可以在主题级分隔符旁边指定。

                // - 只能包含一个多级通配符
                // - 多级通配符必须是主题树中使用的最后一个字符
                if (Strings.countMatches(topicFilter, MULTI_LEVEL_WILDCARD) > 1
                        || (topicFilter.contains(MULTI_LEVEL_WILDCARD)
                        && !topicFilter.endsWith(MULTI_LEVEL_WILDCARD_PATTERN))) {
                    throw new IllegalArgumentException(
                            "无效的多层级通配topic: " + topicFilter);
                }

                // 2) 检查单级通配符
                // 规则:
                // 单级通配符可以在主题树中的任何级别使用，也可以与多级通配符结合使用。它必须在主题级别分隔符旁边使用，除非单独指定。
                validateSingleLevelWildcard(topicFilter);

                return;
            }

            // 2) 这是一个主题名称字符串，不得包含任何通配符
            if (Strings.containsAny(topicFilter, TOPIC_WILDCARDS)) {
                throw new IllegalArgumentException("主题不能包含任何通配字符 (#+)");
            }
        }

        /**
         * org.eclipse.paho.client.mqttv3.MqttTopic#validateSingleLevelWildcard(java.lang.String)
         *
         * @param topicFilter 主题筛选器
         */
        private static void validateSingleLevelWildcard(String topicFilter) {
            char singleLevelWildcardChar = SINGLE_LEVEL_WILDCARD.charAt(0);
            char topicLevelSeparatorChar = TOPIC_LEVEL_SEPARATOR.charAt(0);

            char[] chars = topicFilter.toCharArray();
            int length = chars.length;
            char prev = NUL, next = NUL;
            for (int i = 0; i < length; i++) {
                prev = (i - 1 >= 0) ? chars[i - 1] : NUL;
                next = (i + 1 < length) ? chars[i + 1] : NUL;

                if (chars[i] == singleLevelWildcardChar) {
                    // prev 和 next 只能是 '/' 或 none
                    if (prev != topicLevelSeparatorChar && prev != NUL || next != topicLevelSeparatorChar && next != NUL) {
                        throw new IllegalArgumentException(
                                String.format("主题字符串中单级通配符的使用无效 '%s'!",
                                        new Object[] { topicFilter }));

                    }
                }
            }
        }

    }

}
