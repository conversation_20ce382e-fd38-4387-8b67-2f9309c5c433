// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.core.util;

import org.apache.commons.lang3.StringUtils;
import com.siact.energy.cal.common.constant.Const;
import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.core.database.AbstractDatabase;
import com.siact.energy.cal.core.database.DatabaseFactory;
import com.siact.energy.cal.core.model.ColumnDescription;
import com.siact.energy.cal.core.model.ColumnMetaData;
import com.siact.energy.cal.core.model.TableDescription;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拼接SQL工具类
 *
 * <AUTHOR>
 */
public final class GenerateSqlUtils {

	public static String getDDLCreateTableSQL(
			ProductTypeEnum type,
			List<ColumnDescription> fieldNames,
			List<String> primaryKeys,
			String schemaName,
			String tableName,
			boolean autoIncr) {
		AbstractDatabase db = DatabaseFactory.getDatabaseInstance(type);
		return getDDLCreateTableSQL(
				db,
				fieldNames,
				primaryKeys,
				schemaName,
				tableName,
				false,
				null,
				autoIncr);
	}

	public static String getDDLCreateTableSQL(
			AbstractDatabase db,
			List<ColumnDescription> fieldNames,
			List<String> primaryKeys,
			String schemaName,
			String tableName,
			boolean withRemarks,
			String tableRemarks,
			boolean autoIncr) {
		ProductTypeEnum type = db.getDatabaseType();
		StringBuilder sb = new StringBuilder();
		List<String> pks = fieldNames.stream()
				.filter((cd) -> primaryKeys.contains(cd.getFieldName()))
				.map(ColumnDescription::getFieldName)
				.collect(Collectors.toList());
		if(ProductTypeEnum.TDENGINE == type){
			sb.append(Const.CREATE_STABLE);
//			sb.append(db.getQuotedSchemaTableCombination(schemaName, tableName));
			sb.append(schemaName+"."+tableName);
			sb.append("(");
			sb.append("ts TIMESTAMP,").append("propValue DOUBLE");
			sb.append(") TAGS(devproperty NCHAR(100))");

		}else{
			sb.append(Const.CREATE_TABLE);
			sb.append(db.getQuotedSchemaTableCombination(schemaName, tableName));
			sb.append("(");

			for (int i = 0; i < fieldNames.size(); i++) {
				if (i > 0) {
					sb.append(", ");
				} else {
					sb.append("  ");
				}

				ColumnMetaData v = fieldNames.get(i).getMetaData();
				sb.append(db.getFieldDefinition(v, pks, autoIncr, false, withRemarks));
			}

			if (!pks.isEmpty()) {
				String pk = db.getPrimaryKeyAsString(pks);
				sb.append(", PRIMARY KEY (").append(pk).append(")");
			}

			sb.append(")");
			if (ProductTypeEnum.MYSQL == type) {
				sb.append("ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin");
				if (withRemarks && StringUtils.isNotBlank(tableRemarks)) {
					sb.append(String.format(" COMMENT='%s' ", tableRemarks.replace("'", "\\'")));
				}
			}

		}

		// if(ifNotExist && type!=DatabaseType.ORACLE) {
		// sb.append( Const.IF_NOT_EXISTS );
		// }

		return DDLFormatterUtils.format(sb.toString());
	}

	public static List<String> getDDLCreateTableSQL(
			ProductTypeEnum type,
			List<ColumnDescription> fieldNames,
			List<String> primaryKeys,
			String schemaName,
			String tableName,
			String tableRemarks,
			boolean autoIncr) {
		AbstractDatabase db = DatabaseFactory.getDatabaseInstance(type);
		List<String> results = new ArrayList<>(2);
		String createTableSql = getDDLCreateTableSQL(db, fieldNames, primaryKeys, schemaName,
				tableName, true, tableRemarks, autoIncr);
		results.add(createTableSql);
		if (type.noCommentStatement()) {
			return results;
		}

		TableDescription td = new TableDescription();
		td.setSchemaName(schemaName);
		td.setTableName(tableName);
		td.setRemarks(tableRemarks);
		td.setTableType("TABLE");
		if(!"TDENGINE".equals(type.name())){
			results = db.getTableColumnCommentDefinition(td, fieldNames);
		}
		results.add(0,createTableSql);
		return results;
	}

}
