package com.siact.energy.cal.server.common.utils;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.server.common.enums.SseEmitterEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SseEmitterUtil
 *
 * <AUTHOR>
 * @since 2024-05-16 09:01:00
 */
@Slf4j
public class SseEmitterUtil {

    /**
     * 订阅ID与SseEmitter对象映射集
     *
     */
    private static Map<String, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();

    /**
     * 创建 SseEmitter
     *
     * @param id 唯一ID
     * @return SseEmitter
     */
    public static SseEmitter createSseEmitter(String id) {
        // 设置timeout为0，不超时
        SseEmitter sseEmitter = new SseEmitter(0L);
        //连接成功需要返回数据，否则会出现待处理状态
        try{
            sseEmitter.send(SseEmitter.event()
                    .id(UUID.randomUUID().toString())
                    .name(SseEmitterEventEnum.MQTT_CONNECT_SUCCESS_EVENT.getValue())
                    .data("mqtt连接成功")
                    .comment("mqtt连接成功"));
        }catch (IOException e){
            log.error("sseEmitter发送mqtt连接成功消息失败，", e);
            throw new BizException("发送mqtt连接成功消息失败");
        }
        //连接断开
        sseEmitter.onCompletion(()->{
            sseEmitterMap.remove(id);
        });
        //连接超时
        sseEmitter.onTimeout(()->{
            sseEmitterMap.remove(id);
        });
        //连接报错
        sseEmitter.onError((throwable)-> {
            sseEmitterMap.remove(id);
        });

        sseEmitterMap.put(id, sseEmitter);

        return sseEmitter;
    }

    /**
     * 销毁SseEmitter
     *
     * @param id 唯一ID
     */
    public static void destroySseEmitter(String id) {
        if(sseEmitterMap.containsKey(id)) {
            SseEmitter sseEmitter = sseEmitterMap.get(id);
            sseEmitter.complete();
            sseEmitterMap.remove(id);
        }
    }

    /**
     * 判断是否存在Emitter
     *
     * @param id 唯一ID
     * @return 是否存在
     */
    public static boolean hasSseEmitter(String id) {
        return sseEmitterMap.containsKey(id);
    }

}
