<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.siact.energy.cal</groupId>
    <artifactId>siact-energy-cal-feign</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>siact-energy-cal-feign</name>
    <description>siact-energy-cal-feign</description>


    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
        <spring-cloud-starter-openfeign.version>3.0.1</spring-cloud-starter-openfeign.version>
        <siact-energy-cal-common-core.version>0.0.1-SNAPSHOT</siact-energy-cal-common-core.version>
        <siact-energy-cal-common-pojo.version>0.0.1-SNAPSHOT</siact-energy-cal-common-pojo.version>
        <sonar.maven.plugin.version>3.7.0.1746</sonar.maven.plugin.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <jacoco-maven-plugin.version>0.8.7</jacoco-maven-plugin.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${spring-cloud-starter-openfeign.version}</version>
        </dependency>
        <dependency>
            <groupId>com.siact</groupId>
            <artifactId>siact-energy-cal-common-core</artifactId>
            <version>${siact-energy-cal-common-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.siact</groupId>
            <artifactId>siact-energy-cal-common-pojo</artifactId>
            <version>${siact-energy-cal-common-pojo.version}</version>
        </dependency>

    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!-- 打源码包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 发布插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.0.2</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar.maven.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <!-- skipTests设置为false才可以走单元测试。如果设置为true的话，要么不会生成覆盖率，要么覆盖率为0  -->
                    <skipTests>false</skipTests>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>siact-public</id>
            <name>siact-public</name>
            <url>http://192.100.30.160:8081/repository/siact-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>siact-snapshots</id>
            <name>siact-snapshots</name>
            <url>http://192.100.30.160:8081/repository/siact-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>siact-release</id>
            <name>siact-release</name>
            <url>http://192.100.30.160:8081/repository/siact-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>siact-release</id>
            <name>siact-release</name>
            <url>http://192.100.30.160:8081/repository/siact-release/</url>
        </repository>
        <snapshotRepository>
            <id>siact-snapshots</id>
            <name>siact-snapshots</name>
            <url>http://192.100.30.160:8081/repository/siact-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
