package com.siact.energy.cal.common.pojo.dto.dataSource;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-19
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiOperation("属性历史数据批量查询入参")
public class HistoryDTO {

    @ApiModelProperty(value = "节点编码", position = 1)
    private List<String> dataCodes;
    @ApiModelProperty(value = "开始时间", position = 2, example = "2024-06-01 00:00:00")
    private String startTime;
    @ApiModelProperty(value = "结束时间", position = 3, example = "2024-06-10 00:00:00")
    private String endTime;
    @ApiModelProperty(value = "步长", position = 4, example = "1")
    private Long ts;
    @ApiModelProperty(value = "单位:Y:年;M:月;D:日;H:小时;MIN:分", position = 5, example = "D")
    private String tsUnit;
}
