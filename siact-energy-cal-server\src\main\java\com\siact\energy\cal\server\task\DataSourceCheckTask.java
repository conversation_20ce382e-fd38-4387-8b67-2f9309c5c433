package com.siact.energy.cal.server.task;

import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * DataSourceCheckTask
 *
 * <AUTHOR>
 * @since 2024-05-23 17:44:07
 */
@Slf4j
@Component
public class DataSourceCheckTask implements ApplicationListener<RefreshScopeRefreshedEvent> {

    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 任务开关
     */
    @Value("${data-source.task.enable:true}")
    private boolean taskEnable;

    @Scheduled(cron = "${data-source.task.cron:0 0 0/1 * * ?}")
    public void exec() {

        log.debug("定时检查数据源连接开始==>");

        if(!taskEnable) {
            return;
        }

        dataSourceService.dataSourceCheck();

        log.debug("<==定时检查数据源连接结束");
    }

    @Override
    public void onApplicationEvent(RefreshScopeRefreshedEvent event) {

    }

}
