package com.siact.energy.cal.tool.common.config;

import com.github.alenfive.rocketapi.datasource.JdbcDataSource;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * TDEngineDataSource
 *
 * <AUTHOR>
 * @since 2024-07-23 13:59:06
 */
public class TDengineDataSource extends JdbcDataSource {

    public TDengineDataSource(PlatformTransactionManager transactionManager) {
        super(transactionManager);
    }

    public TDengineDataSource(PlatformTransactionManager transactionManager, boolean storeApi) {
        super(transactionManager, storeApi);
    }

}
