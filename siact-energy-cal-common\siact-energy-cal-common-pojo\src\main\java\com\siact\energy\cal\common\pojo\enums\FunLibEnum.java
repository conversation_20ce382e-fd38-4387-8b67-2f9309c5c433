package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * FunLibEnum
 *
 * <AUTHOR>
 * @since 2024-05-22 11:09:59
 */
public enum FunLibEnum {

    // TODO 高卫东 放到数据库

    ABS("abs", "abs", Arrays.asList("abs", "(", "d", ")"), "求 d 的绝对值"),
    ROUND("round", "round", Arrays.asList("round", "(", "d", ")"), "四舍五入"),
    FLOOR("floor", "floor", Arrays.asList("floor", "(", "d", ")"), "向下取整"),
    CEIL("ceil", "ceil", Arrays.asList("ceil", "(", "d", ")"), "向上取整"),
    SQRT("sqrt", "sqrt", Arrays.asList("sqrt", "(", "d", ")"), "求 d 的平方根"),
    POW("pow", "pow", Arrays.asList("pow", "(", "d1", ",", "d2", ")"), "求 d1 的 d2 次方"),
    LOG("log", "log", Arrays.asList("log", "(", "d", ")"), "求 d 的自然对数"),
    LOG10("log10", "log10", Arrays.asList("log10", "(", "d", ")"), "求 d 以 10 为底的对数"),
    SIN("sin", "sin", Arrays.asList("sin", "(", "d", ")"), "正弦函数"),
    COS("cos", "cos", Arrays.asList("cos", "(", "d", ")"), "余弦函数"),
    TAN("tan", "tan", Arrays.asList("tan", "(", "d", ")"), "正切函数"),
    ATAN("atan", "atan", Arrays.asList("atan", "(", "d", ")"), "反正切函数"),
    ACOS("acos", "acos", Arrays.asList("acos", "(", "d", ")"), "反余弦函数"),
    ASIN("asin", "asin", Arrays.asList("asin", "(", "d", ")"), "反正弦函数"),
    MAX("max", "max", Arrays.asList("max", "(", "x1", "x2", "...", ")"), "取所有参数中的最小值，比较规则遵循逻辑运算符规则。"),
    MIN("min", "min", Arrays.asList("min", "(", "x1", "x2", "...", ")"), "用于生成一个函数，它对任意（个数）参数的调用结果 x。");

    private final String value;

    private final String show;

    private final List<String> pieces;

    private final String description;

    FunLibEnum(String value, String show, List<String> pieces, String description) {
        this.value = value;
        this.show = show;
        this.pieces = pieces;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getShow() {
        return show;
    }

    public List<String> getPieces() {
        return pieces;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<FunLibSelectOptionVO> list() {

        List<FunLibSelectOptionVO> list = new ArrayList();
        for(FunLibEnum enumObj : values()) {

            FunLibSelectOptionVO optionVO = new FunLibSelectOptionVO();
            optionVO.setPieces(enumObj.getPieces());
            optionVO.setDescription(enumObj.getDescription());
            optionVO.setValue(enumObj.getValue());
            optionVO.setLabel(enumObj.getShow());

            list.add(optionVO);
        }

        return list;
    }
}
