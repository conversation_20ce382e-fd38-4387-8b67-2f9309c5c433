package com.siact.energy.cal.server.common.datasource.db.core;

import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库操作抽象类
 */
public abstract class AbstractDbOperator {


    /**
     * 获取数据库连接
     */
    public abstract Connection getConnection(DataSourceVo dataSourceVo);
    
    /**
     * 执行基础查询
     */
    public abstract void executeBasicSql(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo);
            
    /**
     * 执行聚合查询
     */
    public abstract void executeAggQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping);
            
    /**
     * 执行差值查询
     */
    public abstract void executeDiffQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping);
            

    /**
     * 数据写入
     */
    public abstract void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                    List<String> dataCodes,
                                    DataSourceVo dataSourceVo);
                                   
    /**
     * 测试连接
     */
    public abstract boolean testConnection(DataSourceVo testDTO);
}