package com.siact.energy.cal.tool.common.mqtt;

import com.alibaba.fastjson.JSONObject;
import com.siact.energy.cal.common.util.utils.ZLibUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Package com.siact.control.mqtt
 * @description:
 * @create 2023/12/15 18:31
 */
@Component
@Slf4j
public class MqttMessageHandle implements MessageHandler {
    @Autowired
    MqttProperties mqttProperties;
    @Autowired
    MqttResHandler mqttResHandler;
    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        String topic = message.getHeaders().get("mqtt_receivedTopic").toString();

        byte[] decompress = ZLibUtils.decompress((byte[]) message.getPayload());
        // 解压缩后转换为字符串
        String msg = new String(decompress);

        JSONObject msgObj = JSONObject.parseObject(msg);
        //获取指令id
        String topicId = msgObj.getString("id");
        com.siact.energy.cal.tool.common.mqtt.Message mes = new com.siact.energy.cal.tool.common.mqtt.Message();
        mes.setMessageId(topicId);
        mes.setPlayLoad(msg);
        mqttResHandler.deal(mes);
        log.info("\n--------------------START-------------------\n" +
                "接收到订阅消息:\ntopic:" + topic + "\nmessage:" + msg +
                "\n---------------------END--------------------");
    }
}
