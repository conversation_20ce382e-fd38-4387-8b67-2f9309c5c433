// 2. 实现FallbackFactory处理调用失败情况
package com.siact.energy.cal.tool.service.feign.fallback;

import com.siact.energy.cal.common.core.domain.R;

import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.tool.service.feign.DataSourceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Slf4j
@Component
public class DatasourceServiceFallbackFactory implements FallbackFactory<DataSourceFeignClient> {
    
    @Override
    public DataSourceFeignClient create(Throwable cause) {
        log.error("调用服务后端数据源测试接口失败", cause);
        
        return new DataSourceFeignClient() {
            @Override
            public R<Object> testDataSource(DataSourceTestDTO dto) {
                String errorMsg = "无法连接到服务后端进行数据源测试, ";
                
                // 根据异常类型返回不同错误信息
                if (cause instanceof feign.RetryableException) {
                    errorMsg += "服务连接超时或服务不可达，请检查网络连接及服务状态";
                } else if (cause instanceof feign.FeignException.NotFound) {
                    errorMsg += "服务端接口不存在，请确认服务版本是否匹配";
                } else {
                    errorMsg += "发生错误: " + cause.getMessage();
                }
                
                return R.ERROR(errorMsg);
            }

            @Override
            public R<Object> testDataSourceById(Serializable id) {
                String errorMsg = "无法连接到服务后端进行数据源测试, ";
                // 根据异常类型返回不同错误信息
                if (cause instanceof feign.RetryableException) {
                    errorMsg += "服务连接超时或服务不可达，请检查网络连接及服务状态";
                } else if (cause instanceof feign.FeignException.NotFound) {
                    errorMsg += "服务端接口不存在，请确认服务版本是否匹配";
                } else {
                    errorMsg += "发生错误: " + cause.getMessage();
                }
                return R.ERROR(errorMsg);
            }
        };
    }
}