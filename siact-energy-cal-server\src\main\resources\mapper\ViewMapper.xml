<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.flow.FlowViewMapper">

    <delete id="deleteViews" parameterType="string">
        drop view IF EXISTS ${views};
    </delete>

    <select id="executeSql" parameterType="string" resultType="map">
        ${sql}
    </select>

    <select id="executeSqlCount" parameterType="string" resultType="integer">
        ${sql}
    </select>

    <delete id="deleteTables" parameterType="string">
        drop table IF EXISTS ${tables};
    </delete>

    <select id="getAllFields" parameterType="string" resultType="String">
        SELECT column_name FROM information_schema.columns WHERE table_name = #{tableName}
    </select>

</mapper>