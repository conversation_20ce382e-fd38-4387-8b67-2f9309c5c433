package com.siact.energy.cal.common.pojo.dto.ruleDetail;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * RuleDetailVerifyExpressionDTO
 *
 * <AUTHOR>
 * @since 2024-05-22 16:46:21
 */
@ApiModel("指标表达式校验DTO")
@Data
public class RuleDetailVerifyExpressionDTO {

    /**
     * 指标表达式
     */
    @ApiModelProperty(value = "指标表达式", position = 1, required = true, example = "[\"#[math.abs]\", \"(\", \"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPSHL2]\", \")\", \"*\", \"1\", \"0\", \"+\", \"#[sqrt]\", \"(\", \"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPFZL2]\", \")\", \"/\", \"1\", \"0\", \"0\"]")
    @NotEmpty(message = "指标表达式不能为空")
    private List<String> ruleFormula;

}

