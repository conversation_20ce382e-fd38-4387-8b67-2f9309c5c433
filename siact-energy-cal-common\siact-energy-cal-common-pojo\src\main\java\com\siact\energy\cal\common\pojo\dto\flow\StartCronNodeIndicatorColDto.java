package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: ZhangZanWu
 * @CreateTime: 2024-09-03
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("开始定时组件指标集参数Dto")
public class StartCronNodeIndicatorColDto {

  /*  @ApiModelProperty(value = "组件名称", example = "开始组件(指标列表)", position = 1)
    private String name;*/

    @ApiModelProperty(value = "指标集id",position = 1,required = true)
    public Long ruleColId;

    public String endTime;

    @ApiModelProperty(value = "步长",position = 4,required = true)
    public Integer interval;

}
