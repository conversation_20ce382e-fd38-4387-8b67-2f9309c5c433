package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Package com.siact.dwenergy.dataintegrate.entity
 * @description: 数据库实体类
 * @create 2023/7/28 15:18
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName(value = "data_database", autoResultMap = true)
public class DataDatabaseEntity extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 数据库类型
     */
    private Integer databaseType;

    /**
     * 主机ip
     */
    private String databaseIp;

    /**
     * 端口
     */
    private String databasePort;

    /**
     * 库名
     */
    private String databaseName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;


    /**
     * jdbcUrl
     */
    private String jdbcUrl;



}
