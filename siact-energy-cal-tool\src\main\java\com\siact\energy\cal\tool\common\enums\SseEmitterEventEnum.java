package com.siact.energy.cal.tool.common.enums;

/**
 * SseEmitterEventEnum
 *
 * <AUTHOR>
 * @since 2024-05-16 09:35:31
 */
public enum SseEmitterEventEnum {

    /**
     * MQTT连接成功事件
     */
    MQTT_CONNECT_SUCCESS_EVENT("mqttConnectSuccessEvent"),

    /**
     * MQTT消费消息事件
     */
    MQTT_CONSUMER_MESSAGE_EVENT("mqttConsumerMessageEvent"),

    /**
     * 消费mqtt消息失败事件
     */
    MQTT_SUBSCRIBE_MESSAGE_FAIL("mqttSubscribeMessageFail");


    private final String value;

    SseEmitterEventEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
