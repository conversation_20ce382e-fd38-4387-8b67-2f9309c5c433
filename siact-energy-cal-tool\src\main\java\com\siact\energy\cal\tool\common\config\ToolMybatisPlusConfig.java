package com.siact.energy.cal.tool.common.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.siact.energy.cal.tool.common.handler.ToolMybatisPlusMetaObjectHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Tool模块专用的MyBatis Plus配置
 * 确保使用ToolMybatisPlusMetaObjectHandler进行自动填充
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Configuration
public class ToolMybatisPlusConfig {

    @Autowired
    private ToolMybatisPlusMetaObjectHandler toolMybatisPlusMetaObjectHandler;

    /**
     * 自定义GlobalConfig，确保使用ToolMybatisPlusMetaObjectHandler
     */
    @Bean
    @Primary
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(toolMybatisPlusMetaObjectHandler);
        return globalConfig;
    }
}
