package com.siact.energy.cal.tool.service.send.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.TopicConstant;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.tool.common.mqtt.DefaultFuture;
import com.siact.energy.cal.tool.common.mqtt.Message;
import com.siact.energy.cal.tool.common.mqtt.SynMqttSender;
import com.siact.energy.cal.tool.service.send.SendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-13
 * @Description:
 * @Version: 1.0
 */
@Service
@Slf4j
public class SendServiceImpl  implements SendService {
    private  static  final  SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Resource
    SynMqttSender synMqttSender;

    @Override
    public R<JSONArray> sendMsg(JSONObject json, String topic) {
        cn.hutool.json.JSONArray dataCodes = JSONUtil.parseArray(json.get(TopicConstant.DATA_CODES));
        //查询指标不能为空
        if (dataCodes.isEmpty()){
            return R.ERROR("查询属性不能为空");
        }
        if (TopicConstant.POINT_TOPIC.equals(topic)){
            Object o = json.get("ts");
            if (DateUtils.isTimeFormatValid(o.toString(), format)){
                return R.ERROR("时间格式不正确");
            }
        }
        if (TopicConstant.HISTORY_TOPIC.equals(topic)){
            //校验参数-历史点位校验数据
            String checkResult = checkParameter(json);
            if (StrUtil.isNotBlank(checkResult)){
                return R.ERROR(checkResult);
            }
        }
        TimeInterval timer = DateUtil.timer();
        int random = RandomUtil.randomInt(10, 10000);
        //消息唯一键
        String id = random+"_"+topic;
        json.put("id", id);
        DefaultFuture defaultFuture = synMqttSender.sendMessage(topic,
                json, id);
        Message message = defaultFuture.get();
        log.info("耗时：{}, 重置时间:{}",timer.interval(), timer.restart());
        //耗时时间统计,单位:毫秒\
        try {
            JSONArray jsonArray = handlerResult(JSONObject.parseObject(message.getPlayLoad()), topic, dataCodes);
            log.info("解析数据耗时：{}",timer.interval());
            return R.OK(jsonArray);
        }catch (Exception e){
            log.error("解析数据异常{}", e.getMessage(),e);
            return R.ERROR(e.getMessage());
        }
    }

    /**
     * 处理数据
     * @param msgResult 获取到的mqqt消息
     * @param topic 消息主题
     * @return 返回参数
     */
    private static JSONArray handlerResult(JSONObject msgResult, String topic,  cn.hutool.json.JSONArray dataCodes) {
        JSONArray returnArray = new JSONArray();
        if (!ObjectUtils.isEmpty(msgResult)) {
            JSONObject data = msgResult.getJSONObject("data");
            //set:一共有多少个点位
            //map: string：为点位； List<String> 为每个点位的时间与值,
            // 比如:"PGY02001_SPD01001_STPDZ01001_UMXLLG003_EQ000000000000_MPZYD2001":["2024-05-01 00:00:00,30", "2024-05-02 00:00:00,40"]
            Map<String, List<String>> map = new HashMap<>();
            //解析返回数据
            analysisResults(dataCodes, data, map);
            //根据点位信息组合接口返回值
            generateResults(topic, dataCodes, map, returnArray);
        }
        return returnArray;
    }

    /**
     * 组装接口返回数据
     * @param topic 消息主题
     * @param dataCodes 点位信息
     * @param map 点位信息、时间、值
     * @param returnArray  接口返回数据
     */
    private static void generateResults(String topic, cn.hutool.json.JSONArray dataCodes, Map<String, List<String>> map, JSONArray returnArray) {
        for (int i = 0; i< dataCodes.size(); i++) {
            JSONObject jsonObject = new JSONObject();
            Object code = dataCodes.get(i);
            jsonObject.put("dataCode", code);
            if (TopicConstant.HISTORY_TOPIC.equals(topic)){
                //组装历史消息返回值
                //返回消息样例如下：
                //[{"valTimes": [{"propVal": "1828116.11","timestamp": "2024-05-31 00:00:00"}, {"propVal": "1828336.11","timestamp": "2024-05-31 00:30:00"}],"dataCode": "PGY02001_SKYXT001_STKYZ01001_UKYJDY001_EQKYXTKYJ01001_MPCLJ2001"}
                JSONArray valTimesArr = new JSONArray();
                for (int j = 0; j < map.get(code).size(); j++) {
                    String[] split = map.get(code).get(j).split(",");
                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("timestamp", split[0].replaceAll("\\s+"," "));
                    jsonObject1.put("propVal", "null".equals(split[1])?null:split[1]);
                    valTimesArr.add(jsonObject1);
                }
                //根据时间排序
                jsonObject.put("valTimes", sortByTime(valTimesArr));
            }else if (TopicConstant.POINT_TOPIC.equals(topic)){
                //组装断面消息返回值
                //返回消息样例： [{"dataCode": "PGY02001_SPD01001_STPDZ01001_UMXLLG003_EQ000000000000_MPZYD2001", "timestamp": "2024-05-01 00:00:00","propVal": "10"}]
                for (int t = 0; t < map.get(code).size(); t++) {
                    String[] split = map.get(code).get(t).split(",");
                    jsonObject.put("timestamp", split[0].replaceAll("\\s+"," "));
                    jsonObject.put("propVal", "null".equals(split[1])?null:split[1]);
                }
            }
            returnArray.add(jsonObject);
        }
    }

    /**
     * 解析返回结果，生成map:k:点位信息 v:时间与值
     * @param dataCodes 查询的点位编码
     * @param data 返回的信息
     * @param map 返回的解析结果
     */
    private static void analysisResults(cn.hutool.json.JSONArray dataCodes, JSONObject data, Map<String, List<String>> map) {
        for (Map.Entry entry : data.entrySet()) {
            Object key = entry.getKey();
            JSONObject jsonObject = JSONObject.parseObject(entry.getValue().toString());
            List<String> listCode = new ArrayList<>();
            for (Map.Entry str : jsonObject.entrySet()) {
                Object key1 = str.getKey();
                listCode.add(String.valueOf(key1));
                Object value = str.getValue();
                List<String> list = new ArrayList<>();
                if (map.containsKey(String.valueOf(key1))){
                    List<String> strings = map.get(String.valueOf(key1));
                    strings.add(key+","+ value);
                }else {
                    list.add(key+","+ value);
                    map.put(String.valueOf(key1), list);
                }
            }
            for (int i = 0; i < dataCodes.size(); i++) {
                if (listCode.contains(dataCodes.get(i))) {
                    continue;
                }
                List<String> list = new ArrayList<>();
                if (map.containsKey(String.valueOf(dataCodes.get(i)))){
                    List<String> strings = map.get(String.valueOf(dataCodes.get(i)));
                    strings.add(key+","+ null);
                }else {
                    list.add(key+","+ null);
                    map.put(String.valueOf(dataCodes.get(i)), list);
                }
            }
        }

    }

    /**
     * 返回结果按照时间排序
     * @param valTimesArr 需要排序的数组
     * @return 排序后的数据
     */
    private static List<Object> sortByTime(JSONArray valTimesArr) {

        return valTimesArr.stream().sorted((d1, d2) -> {
            JSONObject r1 = (JSONObject) JSONObject.toJSON(d1);
            JSONObject r2 = (JSONObject) JSONObject.toJSON(d2);
            Date dt1 = null;
            Date dt2 = null;
            try {
                dt1 = format.parse(objTurnStr(r1.get("timestamp")));
                dt2 = format.parse(objTurnStr(r2.get("timestamp")));
            } catch (ParseException e) {
                throw new BizException("时间格式转换异常!");
            }
            return Long.compare(dt1.getTime(), dt2.getTime());
        }).collect(Collectors.toList());
    }

    public static String objTurnStr(Object o){
        if (null != o){
            return o.toString();
        }else {
            return null;
        }
    }

    /**
     * 1.开始时间和结束时间不能为空
     * 2.开始时间不能大于结束时间
     * 3.步长和步长单位必须同时设置
     * 4.步长单位不正确
     * 5.查询属性不能为空
     * @param json 入参
     * @return 返回参数
     */
    private String checkParameter(JSONObject json){
        Object tsUnit = json.get(TopicConstant.TS_UNIT);
        Object interval = json.get(TopicConstant.TS);
        if (tsUnit == null && interval == null){
            return "步长和步长单位必须同时设置";
        }
        assert tsUnit != null;
        String tsUnitConvert = convertTsUnit(tsUnit.toString());
        if (StrUtil.isBlank(tsUnitConvert)){
            return "步长单位不正确";
        }
        //转换请求单位
        json.put(TopicConstant.TS_UNIT, tsUnitConvert);
        Object sTime = json.get(TopicConstant.START_TIME);
        Object eTime = json.get(TopicConstant.END_TIME);
        if (eTime == null || sTime == null){
            return "开始时间和结束时间不能为空";
        }
        if (DateUtils.isTimeFormatValid(sTime.toString(), format)){
            return "开始时间格式不正确";
        }
        if (DateUtils.isTimeFormatValid(eTime.toString(), format)){
            return "结束时间格式不正确";
        }
        if (sTime.toString().compareTo(eTime.toString()) > 0){
            return "开始时间不能大于结束时间";
        }
        return null;
    }

    /**
     * 转换时间单位：Y:年;M:月;D:日;H:小时;MIN:分-->m(分)，h(小时)，d(天)，n(月)，y(年)
     * @param tsUnit 入参步长单位
     * @return 转换结果
     */
    private static String  convertTsUnit(String tsUnit) {
        String result = null;
        switch (tsUnit) {
            case "Y":
                result = "y";
                break;
            case "M":
                result = "n";
                break;
            case "D":
                result = "d";
                break;
            case "H":
                result = "h";
                break;
            case "MIN":
                result = "m";
                break;
        }
        return result;
    }
}
