package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: ZhangZanWu
 * @CreateTime: 2024-09-03
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("开始组件指标集参数Dto")
public class StartNodeIndicatorColDto {

  /*  @ApiModelProperty(value = "组件名称", example = "开始组件(指标列表)", position = 1)
    private String name;*/

    @ApiModelProperty(value = "指标集id",position = 1,required = true)
    public Long ruleColId;

    @ApiModelProperty(value = "开始时间开始时间,示例值:2024-08-08 10:00:00",position = 2,required = true)
    public String startTime;

    @ApiModelProperty(value = "结束时间,示例值:2024-08-08 10:00:00",position = 3,required = true)
    public String endTime;

    @ApiModelProperty(value = "步长",position = 4,required = true)
    public Integer interval;

    @ApiModelProperty(value = "步长单位:(Y:年;M:月;D:日;H:小时;MIN:分),示例值(D)",position = 5,required = true)
    public String tsUnit;
}
