package com.siact.energy.cal.common.pojo.dto.flow;

import com.siact.energy.cal.common.pojo.vo.flow.CustomIndicatorVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: ZhangZanWu
 * @CreateTime: 2024-09-03
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("自定义计算组件dto")
public class CustomCalDto {

    @ApiModelProperty(value = "组件名称", example = "自定义计算组件", position = 1)
    private String name;

    @ApiModelProperty(value = "自定义计算指标", position = 2)
    private CustomIndicatorVO customIndicator;

    @ApiModelProperty(value = "指标名称", position = 3)
    private String indicatorName;
}
