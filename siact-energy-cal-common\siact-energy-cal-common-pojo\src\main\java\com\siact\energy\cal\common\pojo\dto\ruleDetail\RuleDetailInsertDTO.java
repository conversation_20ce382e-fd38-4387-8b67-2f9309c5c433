package com.siact.energy.cal.common.pojo.dto.ruleDetail;


import com.siact.energy.cal.common.pojo.enums.CalTypeEnum;
import com.siact.energy.cal.common.pojo.enums.RuleTypeEnum;
import com.siact.energy.cal.common.pojo.validator.EnumValidator;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 指标详情表(RuleDetail) 新增DTO
 *
 * <AUTHOR>
 * @since 2024-05-21 10:07:01
 */
@ApiModel("指标详情表新增DTO")
@Data
public class RuleDetailInsertDTO {

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", position = 1, required = true, example = "变压器指标属性")
    @NotBlank(message = "指标名称不能为空")
    private String ruleName;

    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述", position = 2, example = "变压器指标属性描述")
    private String ruleDes;
    /**
     * 计算模式: 0-表达式模式, 1-流程模式
     */
    @ApiModelProperty(value = "计算模式", position = 3)
    private Integer calculationMode;

    /**
     * 关联的流程链ID
     */
    @ApiModelProperty(value = "流程链ID", position = 4)
    private Long flowChainId;

    /**
     * 组件流信息 (流程模式下使用，JSON格式)
     * 包含完整的LogicFlow对象信息
     */
    @ApiModelProperty(value = "组件流信息", position = 5)
    private String flowContent;
    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    @ApiModelProperty(value = "指标类型（0-实例级规则，1-模型级规则）", required = true, position = 6, example = "1")
    @NotNull(message = "指标类型不能为空")
    @EnumValidator(enumClass = RuleTypeEnum.class, existGetMethod = "getValue", message = "指标类型无效")
    private Integer ruleType;

    /**
     * 计算类型（0-数值计算，1-逻辑计算，2-正则表达式）
     */
    @ApiModelProperty(value = "计算类型（0-数值计算，1-逻辑计算，2-正则表达式）", required = true, position = 7, example = "0")
    @NotNull(message = "计算类型不能为空")
    @EnumValidator(enumClass = CalTypeEnum.class, existGetMethod = "getValue", message = "计算类型无效")
    private Integer calType;

    /**
     * 节点（模型）编码
     */
    @ApiModelProperty(value = "节点（模型）编码", position = 8, required = true, example = "PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MP0000")
    @NotBlank(message = "节点（模型）编码不能为空")
    private String devCode;

    /**
     * 节点（模型）名称
     */
    @ApiModelProperty(value = "节点（模型）名称", required = true, position = 9, example = "变压器")
    @NotBlank(message = "节点（模型）名称不能为空")
    private String devName;

    /**
     * 属性编码，模型编码
     */
    @ApiModelProperty(value = "属性编码，模型编码", position = 10, required = true, example = "PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPSHL2")
    @NotBlank(message = "属性编码，模型编码不能为空")
    private String devProperty;

    /**
     * 属性（模型）名称
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 11, required = true, example = "损耗率")
    @NotBlank(message = "属性（模型）名称不能为空")
    private String propName;

    /**
     * 指标表达式
     */
    @ApiModelProperty(value = "指标表达式", position = 12, required = true, example = "[\"#[math.abs]\", \"(\", \"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPSHL2]\", \")\", \"*\", \"1\", \"0\", \"+\", \"#[sqrt]\", \"(\", \"@[PGY02_SPD01_STPDS01_UBYQDY_EQPD01BYQ01_MPFZL2]\", \")\", \"/\", \"1\", \"0\", \"0\"]")
    @NotEmpty(message = "指标表达式不能为空")
    private List<String> ruleFormula;

    /**
     * 公式表达式
     */
    @ApiModelProperty(value = "指标表达式", position = 13, required = true, example = "[\"Abs\", \"(\", \"变压器/损耗率\", \")\", \"*\", \"1\", \"0\", \"+\", \"Sqrt\", \"(\", \"变压器/负载率\", \")\", \"/\", \"1\", \"0\", \"0\"]")
    @NotEmpty(message = "指标表达式不能为空")
    private List<String> ruleFormulaShow;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 14, required = true, example = "10000")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 所属规则集id
     */
    @ApiModelProperty(value = "所属指标集id", position = 15, required = true, example = "1792491288844308481")
    @NotNull(message = "所属指标集不能为空")
    private Long ruleColId;

}

