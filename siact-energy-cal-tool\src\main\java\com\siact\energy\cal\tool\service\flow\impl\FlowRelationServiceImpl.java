package com.siact.energy.cal.tool.service.flow.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.tool.dao.flow.SiComRelationMapper;
import com.siact.energy.cal.tool.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.tool.service.flow.IFlowRelationService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-18
 * @Description: 组件流与组件的关系实现类
 * @Version: 1.0
 */
@Service
public class FlowRelationServiceImpl extends ServiceImpl<SiComRelationMapper, SiComRelationEntity> implements IFlowRelationService {

    static {
        System.out.println("FlowRelationServiceImpl");
        HashMap<String, String> map = new HashMap<>();
        map.put("aym", "AymService");
        map.put("hy", "HyService");
    }
    @Resource
    SiComRelationMapper siComRelationMapper;
    @Override
    public SiComRelationEntity getSiComRelationEntity(String flowId, String nodeId) {
        LambdaQueryWrapper<SiComRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SiComRelationEntity::getFlowId, flowId);
        wrapper.eq(SiComRelationEntity::getNodeId, nodeId);
        return siComRelationMapper.selectOne(wrapper);
    }

    @Override
    public T getComponentConfigByRelation(SiComRelationEntity SiComRelationEntity, Class tClass) {
        List<CommonNodeDto> list = JSONUtil.toList(SiComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        Object bean = BeanUtil.toBean(collect, tClass);
        return null;
    }


}
