package com.siact.energy.cal.tool.convertor.common;

import com.siact.api.common.api.vo.common.DypropInsVO;
import com.siact.api.common.api.vo.common.ModelPropertyVO;
import com.siact.api.common.api.vo.common.StpropInsVO;
import com.siact.energy.cal.common.pojo.vo.common.DigitalTwinTreeVO;
import com.siact.ins.server.common.vo.common.InsTreeVo;
import com.siact.model.vo.common.ModelTreeVo;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * DigitalTwinConvertor
 *
 * <AUTHOR>
 * @since 2024-05-21 14:32:55
 */
@Mapper
public interface DigitalTwinConvertor {

    DigitalTwinConvertor INSTANCE = Mappers.getMapper(DigitalTwinConvertor.class);

    /**
     * 数字孪生模型树对象集合转简化对象集合
     *
     * @param modelTreeVoList 数字孪生模型树对象集合
     * @return 简化对象集合
     */
    List<DigitalTwinTreeVO> modelTreeVos2DigitalTwinTreeVOs(List<ModelTreeVo> modelTreeVoList);

    /**
     * 数字孪生模型树对象转简化对象
     *
     * @param vo 数字孪生模型树对象
     * @return 简化对象
     */
    @Mapping(source = "modelName", target = "name")
    DigitalTwinTreeVO modelTreeVo2DigitalTwinTreeVO(ModelTreeVo vo);

    /**
     * 模型属性vo集合转简化对象集合
     *
     * @param modelPropertyVOList 模型属性vo集合
     * @param propGroup 属性组
     * @return 简化对象集合
     */
    List<DigitalTwinTreeVO> propVOs2DigitalTwinTreeVOs(List<ModelPropertyVO> modelPropertyVOList, @Context String propGroup);

    /**
     * 模型属性vo转简化对象
     *
     * @param vo 模型属性vo
     * @return 简化对象
     */
    @Mapping(source = "propName", target = "name")
    @Mapping(target = "nodeType", expression = "java(propGroup)")
    @Mapping(source = "dataCode", target = "dataCode")
    @Mapping(target = "children", expression = "java(com.google.common.collect.Lists.newArrayList())")
    DigitalTwinTreeVO propVO2DigitalTwinTreeVO(ModelPropertyVO vo, @Context String propGroup);

    /**
     * 数字孪生实例树对象集合转简化对象集合
     *
     * @param insTreeVoList 数字孪生实例对象集合
     * @return 简化对象集合
     */
    List<DigitalTwinTreeVO> insTreeVos2DigitalTwinTreeVOs(List<InsTreeVo> insTreeVoList);

    /**
     * 数字孪生实例树对象转简化对象
     *
     * @param vo 数字孪生实例树对象
     * @return 简化对象
     */
    @Mapping(source = "insName", target = "name")
    DigitalTwinTreeVO insTreeVo2DigitalTwinTreeVO(InsTreeVo vo);

    /**
     * 数字孪生动态属性实例对象集合转简化对象集合
     *
     * @param dypropInsVOList 数字孪生动态属性实例对象集合
     * @return 简化对象集合
     */
    List<DigitalTwinTreeVO> dypropInsVOs2DigitalTwinTreeVOs(List<DypropInsVO> dypropInsVOList, @Context String propGroup);

    /**
     * 数字孪生动态属性实例对象转简化对象
     *
     * @param dto 数字孪生动态属性实例对象
     * @return 简化对象
     */
    @Mapping(source = "propName", target = "name")
    @Mapping(target = "nodeType", expression = "java(propGroup)")
    @Mapping(source = "dataCode", target = "dataCode")
    @Mapping(target = "children", expression = "java(com.google.common.collect.Lists.newArrayList())")
    DigitalTwinTreeVO dypropInsVO2DigitalTwinTreeVO(DypropInsVO dto, @Context String propGroup);

    /**
     * 数字孪生静态属性实例对象集合转简化对象集合
     *
     * @param stpropInsVOList 数字孪生静态属性实例对象集合
     * @return 简化对象集合
     */
    List<DigitalTwinTreeVO> stpropInsVOs2DigitalTwinTreeVOs(List<StpropInsVO> stpropInsVOList, @Context String propGroup);

    /**
     * 数字孪生静态属性实例对象集合转简化对象集合
     *
     * @param dto 数字孪生静态属性实例对象集合
     * @return 简化对象集合
     */
    @Mapping(source = "propName", target = "name")
    @Mapping(target = "nodeType", expression = "java(propGroup)")
    @Mapping(source = "dataCode", target = "dataCode")
    @Mapping(target = "children", expression = "java(com.google.common.collect.Lists.newArrayList())")
    DigitalTwinTreeVO stpropInsVO2DigitalTwinTreeVO(StpropInsVO dto, @Context String propGroup);
}
