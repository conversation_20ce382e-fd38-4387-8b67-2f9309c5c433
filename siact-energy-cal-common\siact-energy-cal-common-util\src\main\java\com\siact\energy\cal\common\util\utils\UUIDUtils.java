package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.util.IdUtil;

import java.util.UUID;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 临时表名生成工具类
 * @Version: 1.0
 */
public class UUIDUtils {

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 获取视图名称
     */
    public static String uuidViewName() {
        return "_" + UUID.randomUUID().toString().replace("-", "") + "_view";
    }


    /**
     * 获取临时表名
     */
    public static String uuidStdTableName() {
        return "std_" + UUID.randomUUID().toString().replace("-", "") + "_temp";
    }

    public static String uuidOutTableName() {
        return "out_" + IdUtil.simpleUUID() + "_temp";
    }

    public static String uuidTaskName() {
        return "task_" + IdUtil.simpleUUID();
    }
}
