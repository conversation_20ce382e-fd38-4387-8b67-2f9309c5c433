package com.siact.energy.cal.common.pojo.dto.dataTrans;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 数据传输表（mqtt）(DataTrans) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:55
 */
@ApiModel("数据传输表（mqtt）更新DTO")
@Data
public class DataTransUpdateDTO extends DataTransInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1, example = "1790665515066048514")
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

