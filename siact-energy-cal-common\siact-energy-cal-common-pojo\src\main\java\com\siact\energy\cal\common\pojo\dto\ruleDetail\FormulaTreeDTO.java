package com.siact.energy.cal.common.pojo.dto.ruleDetail;/**
 * @Package com.siact.energy.cal.common.pojo.dto.ruleDetail
 * @description: 公式层级树
 * <AUTHOR>
 * @create 2024/12/18 15:06
 */

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.List;

/**
 * @ClassName FormulaTreeDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/18 15:06
 * @Version 1.0
 **/
@Data
@ApiOperation(value = "公式层级树")
public class FormulaTreeDTO {


    /**
     * 节点ID,用于树形结构
     */
    @ApiModelProperty(value = "节点ID", position = 1)
    private String id;

    /**
     * 父节点ID,用于树形结构
     */
    @ApiModelProperty(value = "父节点ID", position = 2)
    private String pid;
    /**
     * 节点编码 (设备编码)
     */
    @ApiModelProperty(value = "节点编码", position = 3, required = true, example = "PGY02006_SGRXT001_STHRZ01007_UHRDY1002_EQ000000000000_MP0000000")
    private String devCode;

    /**
     * 节点名称 (设备名称)
     */
    @ApiModelProperty(value = "节点名称", position = 4, required = true, example = "万科新里程一期高换热站")
    private String devName;
    /**
     * 设备属性编码
     */
    @ApiModelProperty(value = "属性编码", position = 5, required = true, example = "PGY02006_SGRXT001_STHRZ01007_UHRDY1002_EQ000000000000_MPXA12001")
    private String devProperty;

    /**
     * 设备属性名称
     */
    @ApiModelProperty(value = "属性名称", position = 6, required = true, example = "1#循环泵电流平均值")
    private String propName;

    /**
     * 计算公式
     */
    @ApiModelProperty(value = "计算公式", position = 7, required = true, example = "avg(@[PGY02006_SGRXT001_STHRZ01007_UHRDY1002_EQGRXTXHB02001_MPDLF2001])")
    private String formula;

    /**
     * 计算类型（1-基础指标，1-聚合指标，2-衍生指标）
     */
    @ApiModelProperty(value = "计算类型(1-基础指标，1-聚合指标，2-衍生指标)", position = 8, required = true, example = "聚合指标")
    private String calType;

    /**
     * 子节点列表
     */
    @ApiModelProperty(value = "子节点列表", position = 9, required = true, example = "")
    private List<FormulaTreeDTO> children;
}
