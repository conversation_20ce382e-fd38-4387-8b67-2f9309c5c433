package com.siact.energy.cal.tool.service.feign;

import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.vo.flow.FlowRunResultVO;
import com.siact.energy.cal.tool.service.feign.fallback.DatasourceServiceFallbackFactory;
import com.siact.energy.cal.tool.service.feign.fallback.ProcessServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "siact-energy-cal-server",
        contextId = "process-client",
        url = "${service.backend.url}",
        path = "/process",
        fallbackFactory = ProcessServiceFallbackFactory.class)
public interface ProcessFeignClient {

    /**
     * 调用服务后端执行组件流
     *
     * @param id 组件流ID
     * @param paramList 参数列表
     * @return 执行结果
     */
    @PostMapping("/run/{id}")
    R<FlowRunResultVO> runFlow(@PathVariable("id") String id, @RequestBody List<ParamDto> paramList);
}