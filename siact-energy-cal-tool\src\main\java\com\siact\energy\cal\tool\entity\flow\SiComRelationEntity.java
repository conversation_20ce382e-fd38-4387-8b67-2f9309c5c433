package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-30
 * @Description: 节点参数
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("si_component_relation")
public class SiComRelationEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField("flow_id")
    private Integer flowId;
    @TableField("node_id")
    private String nodeId;
    private String type;
    private String text;

    private String formula;
    @TableLogic
    private Integer deleted;
}
