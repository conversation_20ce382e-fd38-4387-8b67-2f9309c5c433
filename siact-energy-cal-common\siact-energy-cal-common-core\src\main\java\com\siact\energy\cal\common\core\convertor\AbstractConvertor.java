package com.siact.energy.cal.common.core.convertor;

import java.util.List;

/**
 * AbstractConvertor
 *
 * <AUTHOR>
 * @since 2024-05-13 16:01:51
 */
public interface AbstractConvertor<VO, ENTITY> {

    /**
     * vo转实体
     *
     * @param vo vo
     * @return 实体
     */
    ENTITY vo2Entity(VO vo);

    /**
     * List<vo>转List<Entity>
     *
     * @param vo
     * @return
     */
    List<ENTITY> vos2Entities(List<VO> vo);

    /**
     * List<Entity>转List<vo>
     *
     * @param entitys
     * @return
     */
    List<VO> entities2Vos(List<ENTITY> entitys);

    /**
     * 实体转vo
     *
     * @param entity 实体
     * @return vo
     */
    VO entity2Vo(ENTITY entity);

}
