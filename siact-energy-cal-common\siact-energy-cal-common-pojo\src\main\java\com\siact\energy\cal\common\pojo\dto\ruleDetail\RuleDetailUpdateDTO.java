package com.siact.energy.cal.common.pojo.dto.ruleDetail;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 指标详情表(RuleDetail) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-21 10:07:01
 */
@ApiModel("指标详情表更新DTO")
@Data
public class RuleDetailUpdateDTO extends RuleDetailInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

