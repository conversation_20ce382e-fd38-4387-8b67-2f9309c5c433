package com.siact.energy.cal.common.pojo.vo.dataSource;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 数据源表(DataSource) VO
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:31
 */
@ApiModel("数据源表VO")
@Data
public class DataSourceVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    private Long id;

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称", position = 2)
    private String databaseName;

    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", position = 3)
    private Integer dbType;

    /**
     * 数据源ip
     */
    @ApiModelProperty(value = "数据源ip", position = 4)
    private String databaseIp;

    /**
     * 数据源端口
     */
    @ApiModelProperty(value = "数据源端口", position = 5)
    private String databasePort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称", position = 6)
    private String db;

    /**
     * 超级表名
     */
    @ApiModelProperty(value = "超级表名", position = 7)
    private String tableName;

    /**
     * 状态（0-正常/1-中断），描述数据库的连接状态
     */
    @ApiModelProperty(value = "状态（0-正常/1-中断），描述数据库的连接状态", position = 8)
    private Integer status;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 9)
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 10)
    private String password;

    /**
     * 数据库连接串
     */
    @ApiModelProperty(value = "数据库连接串", position = 11)
    private String jdbcUrl;

    /**
     * 项目id,如果是数仓数据库，id为空
     */
    @ApiModelProperty(value = "项目id", position = 12)
    private Long projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", position = 13)
    private String projectName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 14)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 15)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}

