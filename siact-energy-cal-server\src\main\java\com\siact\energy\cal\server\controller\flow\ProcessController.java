package com.siact.energy.cal.server.controller.flow;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.vo.flow.FlowRunResultVO;
import com.siact.energy.cal.server.service.flow.impl.ProcessServiceImpl;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 执行对象
 * @Version: 1.0
 */
@Api(tags = "工作流管理")
@RestController
@ApiSort(60)
@RequestMapping("/process")
@Slf4j
public class ProcessController {

    @Resource
    ProcessServiceImpl processService;

    @ApiOperation(value = "执行组件流", notes = "执行组件流")
    @ApiOperationSupport(order = 20)
    @PostMapping("/run/{id}")
    public R<FlowRunResultVO> run(@PathVariable String id, @RequestBody List<ParamDto> paramList) {
        FlowRunResultVO flowRunResultVO = null;
        try {
            flowRunResultVO = processService.runFlow(id, paramList);
        } catch (Exception e) {
           log.error("执行组件流失败,{}",e.getMessage(), e);
           return R.ERROR(e.getMessage());
        }
        return R.OK(flowRunResultVO);
    }
}
