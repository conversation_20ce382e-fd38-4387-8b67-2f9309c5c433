package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-11
 * @Description: 接口类
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ApiConfigDto {

    @ApiModelProperty(value = "api接口id", position = 1, example = "1")
    private String id;
    @ApiModelProperty(value = "api接口名称id", position = 2, example = "历史接口")
    private String apiName;
    @ApiModelProperty(value = "api接口url", position = 3, example = "/api/v1/history")
    private String apiUrl;
    @ApiModelProperty(value = "接口类型", position = 4, example = "POST")
    private String requestType;
    @ApiModelProperty(value = "创建时间", position = 5, example = "2024-06-12 10:35:52")
    private String creatTime;
    @ApiModelProperty(value = "更新时间", position = 6, example = "2024-06-12 10:35:52")
    private String updateTime;
    @ApiModelProperty(value = "接口描述", position = 7, example = "历史接口")
    private String remark;
    @ApiModelProperty(value = "绑定组件流id", position = 8, example = "102")
    private String flowId;
}
