
package com.siact.energy.cal.core.model;

/**
 * ColumnType
 *
 * <AUTHOR>
 **/
public enum FlinkColumnType {

    STRING("java.lang.String", "STRING"),
    JAVA_LANG_BOOLEAN("java.lang.Boolean", "BOOLEAN"),
    BOOLEAN("Boolean", "BOOLEAN NOT NULL"),
    JAVA_LANG_BYTE("java.lang.Byte", "TINYINT"),
    BYTE("byte", "TINYINT NOT NULL"),
    JAVA_LANG_SHORT("java.lang.Short", "SMALLINT"),
    SHORT("short", "SMALLINT NOT NULL"),
    INTEGER("java.lang.Integer", "INT"),
    INT("int", "INT NOT NULL"),
    JAVA_LANG_LONG("java.lang.Long", "BIGINT"),
    LONG("long", "BIGINT NOT NULL"),
    JAVA_LANG_FLOAT("java.lang.Float", "FLOAT"),
    FLOAT("float", "FLOAT NOT NULL"),
    JAVA_LANG_DOUBLE("java.lang.Double", "DOUBLE"),
    DOUBLE("double", "DOUBLE NOT NULL"),
    DATE("java.sql.Date", "DATE"),
    LOCALDATE("java.time.LocalDate", "DATE"),
    TIME("java.sql.Time", "TIME"),
    LOCALTIME("java.time.LocalTime", "TIME"),
    TIMESTAMP("java.sql.Timestamp", "TIMESTAMP"),
    LOCALDATETIME("java.time.LocalDateTime", "TIMESTAMP"),
    OFFSETDATETIME("java.time.OffsetDateTime", "TIMESTAMP WITH TIME ZONE"),
    INSTANT("java.time.Instant", "TIMESTAMP_LTZ"),
    DURATION("java.time.Duration", "INVERVAL SECOND"),
    PERIOD("java.time.Period", "INTERVAL YEAR TO MONTH"),
    DECIMAL("java.math.BigDecimal", "DECIMAL"),
    BYTES("byte[]", "BYTES"),
    T("T[]", "ARRAY"),
    MAP("java.util.Map<K, V>", "MAP");

    private String javaType;
    private String flinkType;

    FlinkColumnType(String javaType, String flinkType) {
        this.javaType = javaType;
        this.flinkType = flinkType;
    }

    public String getJavaType() {
        return javaType;
    }

    public String getFlinkType() {
        return flinkType;
    }

    public static FlinkColumnType getByJavaType(String javaType) {
		for (FlinkColumnType columnType : FlinkColumnType.values()) {
			if (columnType.javaType.equals(javaType)) {
				return columnType;
			}
		}
		return STRING;
	}
}
