package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 组件流定义
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel("组件流")
public class ComponentFlowDto {

    @ApiModelProperty(value = "组件流ID", position = 1)
    private String id;

    @ApiModelProperty(value = "组件流名称", position = 2)
    @NotBlank(message = "组件流名称不能为空")
    private String flowName;

    @ApiModelProperty(value = "创建时间开始", position = 3)
    private String createTimeStart;

    @ApiModelProperty(value = "创建时间结束", position = 4)
    private String updateTimeEnd;

    @ApiModelProperty(value = "创建人", position = 5)
    private String createName;

    @ApiModelProperty(value = "组件流描述", position = 6)
    private String flowDec;

    @ApiModelProperty(value = "创建时间", position = 7)
    private String createTime;

}
