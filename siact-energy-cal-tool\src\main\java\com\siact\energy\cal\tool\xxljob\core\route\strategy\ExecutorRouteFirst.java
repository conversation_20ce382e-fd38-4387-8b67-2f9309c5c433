package com.siact.energy.cal.tool.xxljob.core.route.strategy;

import com.siact.energy.cal.tool.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.tool.xxljob.biz.model.TriggerParam;
import com.siact.energy.cal.tool.xxljob.core.route.ExecutorRouter;

import java.util.List;

/**
 * Created by x<PERSON>uel<PERSON> on 17/3/10.
 */
public class ExecutorRouteFirst extends ExecutorRouter {

    @Override
    public ReturnT<String> route(TriggerParam triggerParam, List<String> addressList){
        return new ReturnT<String>(addressList.get(0));
    }

}
