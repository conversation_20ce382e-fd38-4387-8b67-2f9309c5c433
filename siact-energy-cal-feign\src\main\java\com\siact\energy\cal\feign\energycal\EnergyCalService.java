package com.siact.energy.cal.feign.energycal;

import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.energycal.DataIntervalTimeDTO;
import com.siact.energy.cal.common.pojo.dto.energycal.QueryDataByIntervalTimeDTO;
import com.siact.energy.cal.common.pojo.dto.energycal.QueryDataByPointTimeDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataIntervalQueryVo;
import com.siact.energy.cal.common.pojo.vo.energycal.DataPointQueryVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.feign.energycal
 * @description: 计算框架接口
 * @create 2025/5/7 11:33
 */
@FeignClient(value = "siact-energy-cal", url = "${energyCal.api.url}", path = "/api", contextId = "energycal-service")
public interface EnergyCalService {
    @PostMapping("/history/getEquallySpacedTimeData")
    @ApiOperation("获取历史数据等时间间隔采样数据")
    public R<List<DataIntervalQueryVo>> getEquallySpacedTimeData(
            @RequestBody QueryDataByIntervalTimeDTO oldDTO);


    @PostMapping("/history/getTimeIntervalData")
    @ApiOperation("获取时间区间数据")
    public R<List<DataPointQueryVo>> getTimeIntervalData(@RequestBody DataIntervalTimeDTO dataIntervalTimeDTO);

    @PostMapping("/history/getTimeSliceData")
    @ApiOperation("获取指定时间断面数据,最近15分钟最后一包数据")
    public R<List<DataPointQueryVo>> getTimeSliceData(@RequestBody QueryDataByPointTimeDTO queryDataByPointTimeDTO);

}
