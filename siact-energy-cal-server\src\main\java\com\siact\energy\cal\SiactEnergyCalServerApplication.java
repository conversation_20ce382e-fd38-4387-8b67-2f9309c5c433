package com.siact.energy.cal;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * SiactEnergyCalServerApplication
 *
 * <AUTHOR>
 * @since 2024-05-13 16:17:18
 */
@SpringBootApplication
@EnableSwagger2WebMvc
@Import(BeanValidatorPluginsConfiguration.class)
@EnableFeignClients({"com.siact"})
@MapperScan("com.siact.energy.cal.server.dao")
@EnableScheduling
@EnableCaching
@ComponentScan(basePackages = {
        "com.siact.energy.cal",
        "com.siact"  // 添加这个包路径
})
public class SiactEnergyCalServerApplication {

    public static void main(String[] args) {
        // 处理nacos自带的logback配置导致自定义contextName，启动报错的问题（https://github.com/alibaba/nacos/issues/3251）
        System.setProperty("nacos.logging.default.config.enabled", "false");
        SpringApplication.run(SiactEnergyCalServerApplication.class, args);
    }

}
