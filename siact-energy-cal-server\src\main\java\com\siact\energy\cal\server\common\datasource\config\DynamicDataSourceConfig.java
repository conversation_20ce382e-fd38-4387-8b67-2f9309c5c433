package com.siact.energy.cal.server.common.datasource.config;

import com.siact.energy.cal.server.common.datasource.factory.TDengineDriver;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * DynamicDataSourceConfig
 *
 * <AUTHOR>
 * @since 2024-05-23 16:28:10
 */
@Configuration
public class DynamicDataSourceConfig {

    @Bean
    @ConditionalOnMissingBean
    public TDengineDriver getTDengineDriver(){
        return new TDengineDriver();
    }

}
