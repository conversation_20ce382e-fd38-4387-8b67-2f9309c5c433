package com.siact.energy.cal.common.pojo.dto.dataProject;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * 项目表(DataProject) 新增DTO
 *
 * <AUTHOR>
 * @since 2024-05-14 15:36:45
 */
@ApiModel("项目表新增DTO")
@Data
public class DataProjectInsertDTO {

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", position = 1)
    private String projectName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码", position = 2)
    private String projectCode;

}

