<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.ruleDetail.RuleDetailInstanceDao">

    <resultMap type="com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance" id="RuleDetailInstanceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ruleDetailId" column="rule_detail_id" jdbcType="INTEGER"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="ruleDes" column="rule_des" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
        <result property="calType" column="cal_type" jdbcType="INTEGER"/>
        <result property="devCode" column="dev_code" jdbcType="VARCHAR"/>
        <result property="devName" column="dev_name" jdbcType="VARCHAR"/>
        <result property="devProperty" column="dev_property" jdbcType="VARCHAR"/>
        <result property="propName" column="prop_name" jdbcType="VARCHAR"/>
        <result property="ruleFormula" column="rule_formula" jdbcType="VARCHAR"/>
        <result property="ruleFormulaShow" column="rule_formula_show" jdbcType="VARCHAR" />
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="ruleColId" column="rule_col_id" jdbcType="INTEGER"/>
        <result property="activeState" column="active_state" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 根据四个关键字段查询规则实例记录 -->
    <select id="findInstancesByKeyFields" resultType="com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance">
        SELECT
        *
        FROM
        rule_detail_instance
        WHERE
        rule_detail_id = #{ruleDetailId}
        AND rule_col_id = #{ruleColId}
        AND rule_type = #{ruleType}
        <if test="devProperties != null and devProperties.size() > 0">
            AND dev_property IN
            <foreach collection="devProperties" item="property" open="(" separator="," close=")">
                #{property}
            </foreach>
        </if>
        ORDER BY
        update_time DESC
    </select>
    <select id="selectListByProjectAndCol" resultType="com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance">
        SELECT
            id, rule_detail_id, rule_col_id, rule_name, rule_formula,
            cal_type, rule_type, dev_property, project_code,
            active_status,deleted
        FROM rule_detail_instance
        WHERE project_code = #{projectCode}
          AND rule_col_id = #{ruleColId}
          AND active_status = #{isActive}
          AND deleted = #{isDeleted}
    </select>

</mapper>

