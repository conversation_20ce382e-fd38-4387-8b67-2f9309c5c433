package com.siact.energy.cal.tool.task;

import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.tool.service.ruleDetail.RuleDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@EnableScheduling
public class RuleDetailSyncTask {

    @Autowired
    private RuleDetailService ruleDetailService;

    /**
     * 定时同步规则公式到实例表
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void syncRuleDetailToInstance() {
        log.info("开始执行规则公式同步任务...");
        long startTime = System.currentTimeMillis();

        try {
            // 调用服务执行同步
            ruleDetailService.syncAllFormulaToInstance();

            long costTime = System.currentTimeMillis() - startTime;
            log.info("规则公式同步任务执行完成，耗时: {}ms", costTime);
        } catch (Exception e) {
            log.error("规则公式同步任务执行失败: {}", e.getMessage(), e);
        }
    }
}