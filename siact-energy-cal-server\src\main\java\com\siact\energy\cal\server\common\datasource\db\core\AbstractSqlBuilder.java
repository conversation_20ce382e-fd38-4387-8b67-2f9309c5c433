package com.siact.energy.cal.server.common.datasource.db.core;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;

import java.util.List;
import java.util.Map;

/**
 * SQL生成抽象类
 */
public abstract class AbstractSqlBuilder {
    
    /**
     * 生成基础查询SQL
     */
    public abstract String buildBasicQuerySql(TimeQueryDTO queryDTO, DataSourceVo dataSourceVo);
    
    /**
     * 生成时间区间聚合SQL
     */
    public abstract String buildTimeRangeAggSql(String function,
                                               Map<String, String> propMapping,
                                                TimeQueryDTO queryDTO,
                                                String tableName);
    
    /**
     * 生成等间隔采样聚合SQL
     */
    public abstract String buildIntervalAggSql(String function,
                                               Map<String, String> propMapping,
                                               TimeQueryDTO queryDTO,
                                               String tableName);

    /**
     * 生成时间截面采样SQL
     */
    public abstract String buildTimeSliceDataSql(String startTime,
                                                 String endTime,
                                                 List<String> dataCOdes,
                                               String tableName);
    /**
     * 生成差值计算SQL
     */
    public abstract String buildDiffSql(Map<String, String> propMapping,
                                      String tableName,
                                      String startTime,
                                      String endTime,
                                      String interval,
                                      String unit);

}