package com.siact.energy.cal.common.pojo.dto.dataProject;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 项目表(DataProject) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-14 15:36:45
 */
@ApiModel("项目表更新DTO")
@Data
public class DataProjectUpdateDTO extends DataProjectInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

