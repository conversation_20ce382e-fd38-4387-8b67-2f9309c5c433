package com.siact.energy.cal.tool.common.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * CacheManagerConfig
 *
 * <AUTHOR>
 * @since 2024-05-22 10:59:14
 */
@EnableCaching
@Configuration
public class CacheManagerConfig {

    /**
     * 写入后多长时间过期
     */
    @Value("${caffeine.expire-after-write.seconds:3600}")
    private long expireAfterWriteSeconds;

    /**
     * 初始化容量
     */
    @Value("${caffeine.initial-capacity:50}")
    private int initialCapacity;

    /**
     * 最大容量
     */
    @Value("${caffeine.maximum-size:100}")
    private int maximumSize;

    /**
     * caffeine缓存配置
     *
     * @return 缓存bean
     */
    @Bean("caffeineCacheManager")
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 设置过期时间，写入后五分钟过期
                .expireAfterWrite(expireAfterWriteSeconds, TimeUnit.SECONDS)
                // 初始化缓存空间大小
                .initialCapacity(initialCapacity)
                // 最大的缓存条数
                .maximumSize(maximumSize)
        );
        return cacheManager;
    }

}
