package com.siact.energy.cal.common.type;// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////

import java.util.Arrays;

/**
 * 数据库产品类型的枚举定义
 *
 * <AUTHOR>
 */
public enum ProductTypeEnum {
	/**
	 * 未知数据库类型
	 */
	UNKNOWN(0, "UNKNOWN",null, null, null),

	/**
	 * MySQL数据库类型
	 */
	MYSQL(1,"MYSQL", "com.mysql.jdbc.Driver","/* ping */ SELECT 1", "jdbc:mysql://{host}:{port}/{database}?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true"),

	/**
	 * Oracle数据库类型
	 */
	ORACLE(2, "ORACLE","oracle.jdbc.driver.OracleDriver","SELECT 'Hello' from DUAL", "jdbc:oracle:thin:@{host}:{port}:{database}"),

	/**
	 * SQLServer 2000数据库类型
	 */
	SQLSERVER2000(3,"SQLSERVER2000", "com.microsoft.sqlserver.jdbc.SQLServerDriver","SELECT 1+2 as a", "jdbc:sqlserver://{host}:{port};DatabaseName={database}"),

	/**
	 * SQLServer数据库类型
	 */
	SQLSERVER(4, "SQLSERVER","com.microsoft.sqlserver.jdbc.SQLServerDriver","SELECT 1+2 as a", "jdbc:sqlserver://{host}:{port};DatabaseName={database}"),

	/**
	 * PostgreSQL数据库类型
	 */
	POSTGRESQL(5, "POSTGRESQL","org.postgresql.Driver","SELECT 1", "jdbc:postgresql://{host}:{port}/{database}"),

	/**
	 * [国产]达梦数据库类型
	 */
	DM(6, "DM","dm.jdbc.driver.DmDriver","SELECT 'Hello' from DUAL", "jdbc:dm://{host}:{port}/{database}"),

	/**
	 * [国产]人大金仓数据库类型
	 */
	KINGBASE(7, "KINGBASE","com.kingbase8.Driver","SELECT 1", "jdbc:kingbase8://{host}:{port}/{database}"),

	/**
	 * [国产]TDENGINE时序数据库
	 */
	TDENGINE(8, "TDENGINE","com.taosdata.jdbc.rs.RestfulDriver", "select 1", "jdbc:TAOS-RS://{host}:{port}/{database}?charset=utf-8&timestampFormat=UTC"),

	/**
	 * [国产]南大通用GBase8a数据库
	 */
	GBASE8A(9, "GBASE8A","com.gbase.jdbc.Driver", "/* ping */ SELECT 1", "jdbc:gbase://{host}:{port}/{database}"),

	/**
	 * HIVE数据库
	 */
	HIVE(10,"HIVE", "org.apache.hive.jdbc.HiveDriver", "SELECT 1", "jdbc:hive2://{host}:{port}/{database}"),

	/**
	 * SQLite数据库
	 */
	SQLITE3(11, "SQLITE3","org.sqlite.JDBC", "SELECT 1", "jdbc:sqlite::resource:{file}");


	private Integer index;
	private String desc;
	private String driveClassName;
	private String testSql;
	private String url;

	public String getTestSql() {
		return testSql;
	}

	public String getUrl() {
		return url;
	}

	public String getDriveClassName() {
		return driveClassName;
	}

	ProductTypeEnum(Integer idx,String desc, String driveClassName, String testSql, String url) {
		this.index = idx;
		this.desc = desc;
		this.driveClassName = driveClassName;
		this.testSql = testSql;
		this.url = url;
	}

	public Integer getIndex() {
		return index;
	}
	public String getDesc() {
		return desc;
	}


	public static ProductTypeEnum getByIndex(Integer index) {
		return Arrays.stream(ProductTypeEnum.values()).filter(productTypeEnum -> productTypeEnum.getIndex().equals(index)).findFirst().orElse(ProductTypeEnum.UNKNOWN);
	}


	public boolean noCommentStatement() {
		return Arrays.asList(
				ProductTypeEnum.MYSQL,
				ProductTypeEnum.GBASE8A,
				ProductTypeEnum.HIVE,
				ProductTypeEnum.SQLITE3
		).contains(this);
	}

}
