package com.siact.energy.cal.tool.common.config;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import com.siact.security.key.encrypt.utils.MeOSEncryptUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName DataSourceSecurityAspect
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/17 15:36
 * @Version 1.0
 **/

@Aspect
@Component
public class DataSourceSecurityAspect {

    private static final Logger log = LoggerFactory.getLogger(DataSourceSecurityAspect.class);

    /**
     * 拦截返回 DataSourceVO 的方法，加密密码
     */
    @AfterReturning(
            pointcut = "execution(* com.siact.energy.cal.tool.service.dataSource.*.listPage(..)) || " +
                    "execution(* com.siact.energy.cal.tool.service.dataSource.*.get*(..)) || " +
                    "execution(* com.siact.energy.cal.tool.service.dataSource.*.find*(..)) || " +
                    "execution(* com.siact.energy.cal.tool.service.dataSource.*.query*(..)) || " +
                    "execution(* com.siact.energy.cal.tool.service.dataSource.*.select*(..))",
            returning = "result")
    public void encryptPassword(JoinPoint jp, Object result) {
        // 处理返回结果
        if (result instanceof PageBean) {
            PageBean<?> pageBean = (PageBean<?>) result;
            encryptPasswordInList(pageBean.getRecords());
        } else if (result instanceof List) {
            encryptPasswordInList((List<?>) result);
        } else if (result instanceof DataSourceVO) {
            encryptPasswordInVO((DataSourceVO) result);
        }
    }

    /**
     * 拦截接收 DataSourceVO 参数的方法，解密密码
     */
    @Before("execution(* com.siact.energy.cal.tool.controller.dataSource.*.insert(..)) || " +
            "execution(* com.siact.energy.cal.tool.controller.dataSource.*.update(..)) || " +
            "execution(* com.siact.energy.cal.tool.controller.dataSource.*.dbTest(..))")
    public void decryptPassword(JoinPoint jp) {
        Object[] args = jp.getArgs();
        for (Object arg : args) {
            if (arg instanceof DataSourceInsertDTO) {
                decryptPasswordInVO((DataSourceInsertDTO) arg);
            }else if(arg instanceof DataSourceTestDTO){
                decryptPasswordInTestDTO((DataSourceTestDTO) arg);
            }
        }
    }

    // 处理集合中的 DataSourceVO 对象
    private void encryptPasswordInList(List<?> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        if (list.get(0) instanceof DataSourceVO) {
            for (Object obj : list) {
                encryptPasswordInVO((DataSourceVO) obj);
            }
        }
    }

    // 加密单个 DataSourceVO 对象的密码
    private void encryptPasswordInVO(DataSourceVO vo) {
        if (vo != null && vo.getPassword() != null && !vo.getPassword().isEmpty()) {
            try {
                vo.setPassword(MeOSEncryptUtil.encrypt(vo.getPassword()));
            } catch (Exception e) {
                log.error("密码加密失败", e);
            }
        }
    }

    // 解密单个 DataSourceVO 对象的密码
    private void decryptPasswordInVO(DataSourceInsertDTO vo) {
        if (vo != null && vo.getPassword() != null && !vo.getPassword().isEmpty()) {
            try {
                vo.setPassword(MeOSEncryptUtil.decrypt(vo.getPassword()));
            } catch (Exception e) {
                log.error("密码解密失败", e);
                // 解密失败时可以设置为空或保持原样
                // vo.setPassword("");
            }
        }
    }


    // 解密单个 DataSourceVO 对象的密码
    private void decryptPasswordInTestDTO(DataSourceTestDTO vo) {
        if (vo != null && vo.getPassword() != null && !vo.getPassword().isEmpty()) {
            try {
                vo.setPassword(MeOSEncryptUtil.decrypt(vo.getPassword()));
            } catch (Exception e) {
                log.error("密码解密失败", e);
                // 解密失败时可以设置为空或保持原样
                // vo.setPassword("");
            }
        }
    }
}