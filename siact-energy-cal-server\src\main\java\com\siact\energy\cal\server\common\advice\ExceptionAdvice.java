package com.siact.energy.cal.server.common.advice;


import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.RStatus;
import com.siact.energy.cal.common.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ExceptionAdvice
 *
 * <AUTHOR>
 * @since 2024-05-13 16:09:49
 */
@Slf4j
@RestControllerAdvice
public class ExceptionAdvice {

    /**
     * 参数校验异常捕获，统一响应
     *
     * @param exception 参数校验异常
     * @return 响应
     */
    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> bindExceptionHandler(BindException exception) {
        log.warn("", exception);
        // 从异常对象中拿到ObjectError对象
        ObjectError objectError = exception.getBindingResult().getAllErrors().get(0);

        // 如果在枚举中定义了，则取对应的枚举信息
        RStatus rStatus = RStatus.get(objectError.getDefaultMessage());
        if (rStatus != null) {
            return R.FILL(rStatus);
        }
        // 否者直接返回默认响应码，将注解上的message直接返回
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), objectError.getDefaultMessage());
    }

    /**
     * 参数校验异常捕获，统一响应
     *
     * @param exception 参数校验异常
     * @return 响应
     */
    @ExceptionHandler({ConstraintViolationException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> constraintViolationExceptionHandler(ConstraintViolationException exception) {
        log.warn("", exception);
        Set<ConstraintViolation<?>> constraintViolations = exception.getConstraintViolations();
        List<String> codeList = new ArrayList<>();
        String message = constraintViolations.stream().map(constraintViolation -> {
            String msg = constraintViolation.getMessage();
            // 如果在枚举中定义了，则取对应的枚举信息
            RStatus rStatus = RStatus.get(msg);
            if (rStatus != null) {
                log.warn(rStatus.getMessage());
                codeList.add(rStatus.getCode());
                return rStatus.getMessage();
            }
            return msg;
        }).distinct().collect(Collectors.joining(";"));

        return R.FILL(codeList.isEmpty() ? RStatus.CLIENT_ERROR.getCode() : codeList.stream().collect(Collectors.joining(";")), message);
    }

    /**
     * 缺少参数，统一响应
     *
     * @param exception 异常
     * @return 响应
     */
    @ExceptionHandler({MissingServletRequestParameterException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> missingServletRequestParameterExceptionExceptionHandler(MissingServletRequestParameterException exception) {
        log.warn("", exception);
        String msg = String.format("缺少[%s]类型的参数[%s]", exception.getParameterType(), exception.getParameterName());
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), msg);
    }

    /**
     * 参数格式不正确，统一响应
     *
     * @param exception 异常
     * @return 响应
     */
    @ExceptionHandler({HttpMessageNotReadableException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> httpMessageNotReadableExceptionExceptionHandler(HttpMessageNotReadableException exception) {
        log.warn("", exception);
        String msg = String.format("参数格式不正确[%s]", exception.getCause().getMessage());
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), msg);
    }

    /**
     * 参数类型不正确，统一响应
     *
     * @param exception 异常
     * @return 响应
     */
    @ExceptionHandler({MethodArgumentTypeMismatchException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> methodArgumentTypeMismatchExceptionExceptionHandler(MethodArgumentTypeMismatchException exception) {
        log.warn("", exception);
        String msg = String.format("参数[%s]支持的类型为[%s], 但是提供的值是[%s]",
                exception.getName(), exception.getRequiredType().getSimpleName(), exception.getValue());
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), msg);
    }

    /**
     * 请求方式不正确，统一响应
     *
     * @param exception 异常
     * @return 响应
     */
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> httpRequestMethodNotSupportedExceptionExceptionHandler(HttpRequestMethodNotSupportedException exception) {
        log.warn("", exception);
        String msg = String.format("不支持请求方式[%s], 支持的请求方式为%s",
                exception.getMethod(), exception.getSupportedHttpMethods());
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), msg);
    }

    /**
     * Content-type不支持，统一响应
     *
     * @param exception 异常
     * @return 响应
     */
    @ExceptionHandler({HttpMediaTypeNotSupportedException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<Object> httpRequestMethodNotSupportedExceptionExceptionHandler(HttpMediaTypeNotSupportedException exception) {
        log.warn("", exception);
        String msg = String.format("不支持Content-type[%s], 支持的Content-type为%s",
                exception.getContentType(), exception.getSupportedMediaTypes());
        return R.FILL(RStatus.CLIENT_ERROR.getCode(), msg);
    }

    /**
     * 唯一约束异常，统一处理
     *
     * @param exception 参数校验异常
     * @return 响应
     */
    @ExceptionHandler({DuplicateKeyException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Object> uniqueConstraintExceptionHandler(DuplicateKeyException exception) {
        log.error("", exception);
        return R.ERROR("唯一约束错误");
    }

    /**
     * 参数校验异常捕获，统一响应
     *
     * @param exception 参数校验异常
     * @return 响应
     */
    @ExceptionHandler({BizException.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Object> bizExceptionHandler(BizException exception) {
        log.error("", exception);
        // 从异常对象中拿到ObjectError对象
        String msg = exception.getMessage();
        return R.ERROR(msg);
    }

    /**
     * 其他异常捕获，统一响应
     *
     * @param exception 其他异常
     * @return 响应
     */
    @ExceptionHandler({Exception.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Object> defaultExceptionHandler(Exception exception) {
        log.error("", exception);
        return R.ERROR();
    }

}
