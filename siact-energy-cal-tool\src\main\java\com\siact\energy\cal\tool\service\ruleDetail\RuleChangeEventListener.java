package com.siact.energy.cal.tool.service.ruleDetail;

import cn.hutool.core.util.ObjectUtil;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.tool.service.ruleDetail.impl.RuleDetailServiceImpl;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RuleChangeEventListener {

    @Autowired
    private RuleDetailServiceImpl ruleDetailService;

    @EventListener
    public void handleRuleChangeEvent(RuleDetailServiceImpl.RuleChangeEvent event) {
        try {
            RuleDetail rule = event.getRule();
            switch (event.getChangeType()) {
                case ADD:
                case MODIFY:
                case ENABLE:
                case DISABLE:
                    updateRuleCache(rule);
                    break;
                case DELETE:
                    deleteRuleCache(rule);
                    break;
            }
        } catch (Exception e) {
            log.error("处理规则变更事件失败: {}", e.getMessage(), e);
        }
    }

    private void updateRuleCache(RuleDetail rule) {
        try {
            // 获取规则详情
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            ruleDetailService.getInsRuleDetail(ruleDetailVo);
        } catch (Exception e) {
            log.error("更新规则缓存失败，规则ID: {}, 错误: {}", rule.getId(), e.getMessage(), e);
        }
    }

    /**
     * 将RuleDetail转换为RuleDetailVo
     */
    private RuleDetailVo convertRuleToVo(RuleDetail rule) {
        if (rule == null) {
            return null;
        }

        RuleDetailVo vo = new RuleDetailVo();
        vo.setId(rule.getId());
        vo.setRuleName(rule.getRuleName());
        vo.setRuleDes(rule.getRuleDes());
        vo.setRuleType(rule.getRuleType());
        vo.setCalType(rule.getCalType());
        vo.setDevCode(rule.getDevCode());
        vo.setDevName(rule.getDevName());
        vo.setDevProperty(rule.getDevProperty());
        vo.setPropName(rule.getPropName());
        vo.setProjectId(rule.getProjectId());
        vo.setRuleColId(rule.getRuleColId());
        vo.setActiveState(rule.getActiveState());
        vo.setDeleted(rule.getDeleted());

        // 设置流程模式相关字段
        vo.setCalculationMode(rule.getCalculationMode());
        vo.setFlowChainId(rule.getFlowChainId());

        // 转换公式格式
        if (rule.getRuleFormula() != null && !rule.getRuleFormula().isEmpty()) {
            vo.setRuleFormula(String.join(",", rule.getRuleFormula()));
            vo.setRuleFormulaShow(String.join(",", rule.getRuleFormulaShow()));
        }

        return vo;
    }

    private void removeRuleCache(RuleDetail rule) {
        try {
            // 构建要删除的规则列表
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            if (ruleDetailVo != null) {
                List<RuleDetailVo> ruleDetails = new ArrayList<>();
                ruleDetails.add(ruleDetailVo);
                ruleDetailService.removeBatchFromRuleDetailInstance(ruleDetails);
            }
        } catch (Exception e) {
            log.error("删除规则缓存失败: {}", rule.getDevProperty(), e);
        }
    }

    private void deleteRuleCache(RuleDetail rule) {
        try {
            // 构建要删除的规则列表
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            if (ruleDetailVo != null) {
                List<RuleDetailVo> ruleDetails = new ArrayList<>();
                ruleDetails.add(ruleDetailVo);
                ruleDetailService.deleteBatchFromRuleDetailInstance(ruleDetails);
            }
        } catch (Exception e) {
            log.error("删除规则缓存失败: {}", rule.getDevProperty(), e);
        }
    }
}