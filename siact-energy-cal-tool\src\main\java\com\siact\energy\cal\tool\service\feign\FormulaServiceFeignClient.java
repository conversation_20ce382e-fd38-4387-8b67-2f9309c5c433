package com.siact.energy.cal.tool.service.feign;

import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.FormulaTreeDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailViewDTO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.FormulaTreeVO;
import com.siact.energy.cal.tool.service.feign.fallback.FormulaServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "siact-energy-cal-server",
        contextId = "formula-client",
    url = "${service.backend.url}",
    path = "/api/formula",
    fallbackFactory = FormulaServiceFallbackFactory.class
)
public interface FormulaServiceFeignClient {
    
    /**
     * 查看公式详情
     */
    @GetMapping("/view")
    R<PageBean<RuleDetailViewDTO>> viewFormula(@RequestParam("id") Long id,
                                               PageBean<RuleDetailViewDTO> page);
    
    /**
     * 查看公式树结构
     */
    @GetMapping("/view-tree")
    R<FormulaTreeDTO> viewFormulaTree(@RequestBody FormulaTreeVO formulaTreeVO);
}