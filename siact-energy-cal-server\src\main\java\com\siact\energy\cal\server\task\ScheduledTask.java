package com.siact.energy.cal.server.task;
/**
 * @Package com.siact.energycal.task
 * @description: 定时任务
 * <AUTHOR>
 * @create 2024/7/12 16:50
 */

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClassName ScheduledTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/12 16:50
 * @Version 1.0
 **/
@Component
@Slf4j
public class ScheduledTask {


    @Autowired
    private DataSourceService dataSourceService;


    @Autowired
    EnergyCalService energyCalService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private CompletableFuture<Void> formulaConfigFuture = new CompletableFuture<>();



    /*
     * <AUTHOR>
     * @Description //定时执行基础指标计算任务,每分钟执行一次,计算当前时间前后五分钟的基础指标
     * @Date 10:47 2024/8/14
     * @Param
     * @return
     **/
    @Scheduled(fixedRate = 1000 * 60)
    public void calculateBaseIndicators() {
        formulaConfigFuture.thenRun(() -> {
            log.info("开始计算基础指标...");
            //获取当前所有可用的数据源相关的项目实例编码
            List<String> projectCodeList = dataSourceService.getAllDataSource()
                    .stream()
                    .map(DataSourceVo::getProjectCode)
                    .collect(Collectors.toList());
            //计算当前时间的前后五分钟,即开始时间和结束时间,步长默认为1m
            String currentTime = DateUtils.getCurrentimeMinuteStr();
            String stime = DateUtils.format(DateUtils.minuteOffset(DateUtils.parse(currentTime, DateUtils.YMD_HMS), -5),DateUtils.YMD_HMS);
            String etime = DateUtils.format(DateUtils.minuteOffset(DateUtils.parse(currentTime, DateUtils.YMD_HMS), 5),DateUtils.YMD_HMS);
            TimeQueryDTO queryDataByIntervalTimeDTO = new TimeQueryDTO(stime, etime,projectCodeList, 1, "MIN", TimeQueryDTO.TimeQueryType.EQUALLY_SPACED);
            energyCalService.calBaseIndicatorByProjectCode(queryDataByIntervalTimeDTO);
        });

    }


    /**
     * 计算定时触发的时间区间。
     *
     * @param currentTimeMillis 当前时间（以毫秒为单位）。
     * @param intervalMinutes   时间间隔，分钟为单位。
     * @return 一个包含开始时间和结束时间的数组，其中 [0] 是开始时间，[1] 是结束时间。
     */
    public static String[] calculateTimeRange(long currentTimeMillis, int intervalMinutes) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        calendar.setTimeInMillis(currentTimeMillis);

        // 将时间调整到当前时间间隔的起始点
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        int minutesOffset = calendar.get(Calendar.MINUTE) % intervalMinutes;
        if (minutesOffset != 0) {
            calendar.add(Calendar.MINUTE, -minutesOffset);
        }

        // 计算结束时间（当前时间）
        long endTimeMillis = calendar.getTimeInMillis();

        // 计算开始时间（结束时间减去时间间隔）
        long startTimeMillis = endTimeMillis - intervalMinutes * 60 * 1000;

        // 格式化时间
        String startTimeStr = sdf.format(startTimeMillis);
        String endTimeStr = sdf.format(endTimeMillis);

        return new String[]{startTimeStr, endTimeStr};
    }

    public static void main(String[] args) {
        long currentTimeMillis = System.currentTimeMillis();
        String[] intervalTimes = calculateTimeRange(currentTimeMillis, 30);
        System.out.println("Start Time: " + intervalTimes[0]);
        System.out.println("End Time: " + intervalTimes[1]);
    }
}
