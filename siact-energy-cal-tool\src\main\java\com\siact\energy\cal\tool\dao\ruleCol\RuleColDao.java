package com.siact.energy.cal.tool.dao.ruleCol;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.tool.entity.ruleCol.RuleCol;

/**
 * 指标集表(RuleCol)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:14
 */
@Repository
public interface RuleColDao extends BaseMapper<RuleCol> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RuleCol> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<RuleCol> entities);


}

