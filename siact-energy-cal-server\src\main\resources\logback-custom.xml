<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<configuration debug="false" scan="true" scanPeriod="60 seconds" packagingData="false">


    <contextName>siact-energy-cal-server</contextName>

    <springProperty scope="context" source="logging.home" name="logging.home"/>
    <springProperty scope="context" source="spring.application.name" name="spring.application.name"/>
    <springProperty scope="context" source="logging.debugMaxFileSize" name="logging.debugMaxFileSize" defaultValue="100MB"/>
    <springProperty scope="context" source="logging.debugMaxHistory" name="logging.debugMaxHistory" defaultValue="7"/>
    <springProperty scope="context" source="logging.debugTotalSizeCap" name="logging.debugTotalSizeCap" defaultValue="10GB"/>
    <springProperty scope="context" source="logging.infoMaxFileSize" name="logging.infoMaxFileSize" defaultValue="100MB"/>
    <springProperty scope="context" source="logging.infoMaxHistory" name="logging.infoMaxHistory" defaultValue="7"/>
    <springProperty scope="context" source="logging.infoTotalSizeCap" name="logging.infoTotalSizeCap" defaultValue="10GB"/>
    <springProperty scope="context" source="logging.errorMaxFileSize" name="logging.errorMaxFileSize" defaultValue="100MB"/>
    <springProperty scope="context" source="logging.errorMaxHistory" name="logging.errorMaxHistory" defaultValue="15"/>
    <springProperty scope="context" source="logging.errorTotalSizeCap" name="logging.errorTotalSizeCap" defaultValue="10GB"/>
    <springProperty scope="context" source="logging.consoleFilterLevel" name="logging.consoleFilterLevel" defaultValue="INFO"/>
    <springProperty scope="context" source="log.file.suffix" name="log.file.suffix" defaultValue=".log"/>
    <springProperty scope="context" source="log.file.compressSuffix" name="log.file.compressSuffix" defaultValue=".zip"/>

    <property name="encoder.pattern" value="%contextName %yellow(%d{yyyy-MM-dd HH:mm:ss.SSS}) %magenta([%X{traceId}]) %green([%thread]) %highlight(%-5level) %cyan(%logger) - %msg%n"></property>

    <!-- 控制台日志 -->
    <appender name="toConsole" class="ch.qos.logback.core.ConsoleAppender">

        <encoder>
            <Pattern>${encoder.pattern}</Pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="com.siact.energy.cal.server.common.config.XxlJobLogFilter"/>
    </appender>

    <!-- DEBUG日志 -->
    <appender name="toDebugLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${logging.home}/${spring.application.name}-debug${log.file.suffix}</file>

        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${encoder.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${logging.home}/${spring.application.name}-debug-%d{yyyy-MM-dd}-%i${log.file.compressSuffix}</fileNamePattern>
            <!-- 每个日志文件的最大大小 -->
            <maxFileSize>${logging.debugMaxFileSize}</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>${logging.debugMaxHistory}</maxHistory>
            <!-- 日志文件保留的总的最大大小-->
            <totalSizeCap>${logging.debugTotalSizeCap}</totalSizeCap>+
        </rollingPolicy>
    </appender>

    <!-- INFO日志 -->
    <appender name="toInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${logging.home}/${spring.application.name}-info${log.file.suffix}</file>

        <!-- 此日志文件只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <filter class="com.siact.energy.cal.server.common.config.XxlJobLogFilter"/>
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${encoder.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${logging.home}/${spring.application.name}-info-%d{yyyy-MM-dd}-%i${log.file.compressSuffix}</fileNamePattern>
            <!-- 每个日志文件的最大大小 -->
            <maxFileSize>${logging.infoMaxFileSize}</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>${logging.infoMaxHistory}</maxHistory>
            <!-- 日志文件保留的总的最大大小-->
            <totalSizeCap>${logging.infoTotalSizeCap}</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- WARN 及以上级别日志 -->
    <appender name="toErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名 -->
        <file>${logging.home}/${spring.application.name}-error${log.file.suffix}</file>
        <!-- 此日志文件记录warn级别及以上 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
        <!--日志文件输出格式-->
        <encoder>
            <pattern>${encoder.pattern}</pattern>
            <!-- 此处设置字符集 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logging.home}/${spring.application.name}-error-%d{yyyy-MM-dd}-%i${log.file.compressSuffix}</fileNamePattern>
            <!-- 每个日志文件的最大大小 -->
            <maxFileSize>${logging.errorMaxFileSize}</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>${logging.errorMaxHistory}</maxHistory>
            <!-- 日志文件保留的总的最大大小-->
            <totalSizeCap>${logging.errorTotalSizeCap}</totalSizeCap>
        </rollingPolicy>
    </appender>

    <root level="INFO">
        <appender-ref ref="toConsole"/>
        <appender-ref ref="toDebugLog"/>
        <appender-ref ref="toInfoLog"/>
        <appender-ref ref="toErrorLog"/>
    </root>

</configuration>