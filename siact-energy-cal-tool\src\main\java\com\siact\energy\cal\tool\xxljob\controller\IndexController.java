package com.siact.energy.cal.tool.xxljob.controller;


import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.tool.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.tool.xxljob.service.XxlJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 任务概览
 * @Version: 1.0
 */
@Controller
@Api(tags = {"任务概览"})
@ApiSort(90)
public class IndexController {

	@Resource
	private XxlJobService xxlJobService;

    @RequestMapping(value = "/chartInfo", method = RequestMethod.GET)
	@ApiOperation("任务统计")
	@ResponseBody
	public ReturnT<Map<String, Object>> chartInfo(Date startDate, Date endDate) {
        return xxlJobService.chartInfo(startDate, endDate);
    }

	@InitBinder
	public void initBinder(WebDataBinder binder) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		dateFormat.setLenient(false);
		binder.registerCustomEditor(Date.class, new CustomDateEditor(dateFormat, true));
	}
}
