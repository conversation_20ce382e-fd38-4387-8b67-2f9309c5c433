package com.siact.energy.cal.server.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TimeUtils {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 生成时间分组
     */
    public static List<String[]> generateTimeGroups(String startTimeStr, String endTimeStr, int interval, String unit) {
        // 参数验证
        if (interval <= 0) {
            throw new IllegalArgumentException("时间间隔必须大于0");
        }
        if (!Arrays.asList("m", "h", "d", "n", "y").contains(unit)) {
            throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }

        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);
        
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }

        // 对齐开始时间
        LocalDateTime alignedStartTime = alignToIntervalBoundary(startTime, interval, unit);
        List<String[]> timeGroups = new ArrayList<>();
        
        LocalDateTime currentStartTime = alignedStartTime;
        while (currentStartTime.isBefore(endTime)) {
            LocalDateTime groupEndTime = addInterval(currentStartTime, interval, unit);
            
            // 如果分组结束时间超过了总结束时间，则使用总结束时间
            if (groupEndTime.isAfter(endTime)) {
                groupEndTime = endTime;
            }
            
            timeGroups.add(new String[]{
                currentStartTime.format(DATE_TIME_FORMATTER),
                groupEndTime.format(DATE_TIME_FORMATTER)
            });
            
            currentStartTime = groupEndTime;
        }

        // 处理最后一个时间点
        if (!timeGroups.isEmpty()) {
            LocalDateTime lastGroupStart = LocalDateTime.parse(timeGroups.get(timeGroups.size() - 1)[0], DATE_TIME_FORMATTER);
            LocalDateTime nextGroupStart = addInterval(lastGroupStart, interval, unit);
            
            if (nextGroupStart.equals(endTime)) {
                timeGroups.add(new String[]{
                    endTime.format(DATE_TIME_FORMATTER),
                    endTime.format(DATE_TIME_FORMATTER)
                });
            }
        }

        return timeGroups;
    }

    /**
     * 将时间对齐到间隔边界
     */
    private static LocalDateTime alignToIntervalBoundary(LocalDateTime time, int interval, String unit) {
        switch (unit) {
            case "m":
                return time.withSecond(0).withNano(0)
                        .withMinute((time.getMinute() / interval) * interval);
            case "h":
                return time.withMinute(0).withSecond(0).withNano(0)
                        .withHour((time.getHour() / interval) * interval);
            case "d":
                return time.withHour(0).withMinute(0).withSecond(0).withNano(0);
            case "n":
                return time.withDayOfMonth(1).withHour(0).withMinute(0)
                        .withSecond(0).withNano(0);
            case "y":
                return time.withMonth(1).withDayOfMonth(1).withHour(0)
                        .withMinute(0).withSecond(0).withNano(0);
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }
    }

    /**
     * 添加时间间隔
     */
    private static LocalDateTime addInterval(LocalDateTime time, int interval, String unit) {
        switch (unit) {
            case "m":
                return time.plusMinutes(interval);
            case "h":
                return time.plusHours(interval);
            case "d":
                return time.plusDays(interval);
            case "n":
                return time.plusMonths(interval);
            case "y":
                return time.plusYears(interval);
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }
    }
}