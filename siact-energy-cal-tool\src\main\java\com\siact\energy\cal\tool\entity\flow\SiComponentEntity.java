package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel("组件")
@TableName("si_component")
public class SiComponentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "组件id", example = "1")
    private Long id;

    @ApiModelProperty(value = "组件名称", example = "DataSourceNode")
    private String name;

    /**
     * 组件类型(1:输入;2:计算;3:输出;4:数据准备)
     */
    @ApiModelProperty(value = "组件类型(1:输入;2:计算;3:输出;4:数据准备)", example = "1")
    private String type;

    /**
     * 组件描述
     */
    @ApiModelProperty(value = "组件描述", example = "数据库输入组件")
    private String des;


}
