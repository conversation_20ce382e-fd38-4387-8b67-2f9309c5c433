package com.siact.energy.cal.tool.xxljob.controller.interceptor;


import com.siact.energy.cal.tool.xxljob.service.LoginService;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 权限拦截器
 * @Version: 1.0
 */
@Component
public class PermissionInterceptor implements AsyncHandlerInterceptor {

	@Resource
	private LoginService loginService;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		
//		if (!(handler instanceof HandlerMethod)) {
//			return true;	// proceed with the next interceptor
//		}
//
//		// if need login
//		boolean needLogin = true;
//		boolean needAdminuser = false;
//		HandlerMethod method = (HandlerMethod)handler;
//		PermissionLimit permission = method.getMethodAnnotation(PermissionLimit.class);
//		PermissionLimit methodAnnotation = method.getMethodAnnotation(PermissionLimit.class);
//		if (permission!=null) {
//			needLogin = permission.limit();
//			needAdminuser = permission.adminuser();
//		}
//
//		if (needLogin) {
//			XxlJobUser loginUser = loginService.ifLogin(request, response);
//			if (loginUser == null) {
//				response.setStatus(302);
//				response.setHeader("location", request.getContextPath()+"/toLogin");
//				return false;
//			}
//			if (needAdminuser && loginUser.getRole()!=1) {
//				throw new RuntimeException(I18nUtil.getString("system_permission_limit"));
//			}
//			request.setAttribute(LoginService.LOGIN_IDENTITY_KEY, loginUser);
//		}

		return true;	// proceed with the next interceptor
	}
	
}
