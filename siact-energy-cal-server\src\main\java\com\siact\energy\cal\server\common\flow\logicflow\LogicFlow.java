package com.siact.energy.cal.server.common.flow.logicflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 上下文
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("组件流类")
public class LogicFlow {


    @ApiModelProperty("组件流名称")
    @NotBlank(message = "组件流名称不能为空")
    private String flowName;
    @ApiModelProperty("组件")
    private List<LfNode> nodes;
    @ApiModelProperty("坐标")
    private List<LfEdge> edges;

    @ApiModelProperty("组件描述")
    private String des;

    @ApiModelProperty("组件流参数")
    private String params;

}
