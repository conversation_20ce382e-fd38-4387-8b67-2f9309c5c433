package com.siact.energy.cal.tool.common.pool;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolHolder {
    private static final ThreadPoolExecutor EXECUTOR_SERVICE = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(), // 核心线程数
            2 * Runtime.getRuntime().availableProcessors(), // 最大线程数
            60L, // 空闲线程存活时间
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>() // 任务队列
    );

    private ThreadPoolHolder() {
        // 私有构造方法防止实例化
    }

    public static ThreadPoolExecutor getExecutorService() {
        return EXECUTOR_SERVICE;
    }
    }