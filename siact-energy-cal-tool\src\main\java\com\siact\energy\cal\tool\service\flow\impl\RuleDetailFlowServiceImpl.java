package com.siact.energy.cal.tool.service.flow.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.tool.dao.flow.RuleDetailMapper;
import com.siact.energy.cal.tool.entity.flow.RuleDetailEntity;
import com.siact.energy.cal.tool.service.flow.IRuleDetailFlowService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-04
 * @Description: 规则实现类
 * @Version: 1.0
 */
@Service
public class RuleDetailFlowServiceImpl extends ServiceImpl<RuleDetailMapper, RuleDetailEntity> implements IRuleDetailFlowService {
    @Resource
    RuleDetailMapper ruleDetailMapper;
    @Override
    public RuleDetailEntity getRuleDetail(String id) {
        if (StrUtil.isBlank(id)){
            return null;
        }
        LambdaQueryWrapper<RuleDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuleDetailEntity::getId, id);
        return ruleDetailMapper.selectOne(wrapper);
    }

    @Override
    public Optional<RuleDetailEntity> getRuleDetailOption(String id) {
        if (StrUtil.isBlank(id)){
            return Optional.empty();
        }
        LambdaQueryWrapper<RuleDetailEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuleDetailEntity::getId, id);
        return Optional.of(ruleDetailMapper.selectOne(wrapper));
    }

}
