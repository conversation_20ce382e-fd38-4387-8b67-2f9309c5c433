package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.DataSourceDto;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.MapToDbUtils;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.common.flow.init.TaoSnit;
import com.siact.energy.cal.server.entity.flow.DataSourceEntity;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.IFlowTableService;
import com.siact.energy.cal.server.service.flow.impl.DataSourceFlowServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据源组件
 * @Version: 1.0
 */
@Component("DataSourceNode")
@Slf4j
public class DataSourceComponent extends NodeComponent {

    @Resource
    HikariDataSource hikariDataSource;

    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    TaoSnit taoSnit;

    @Resource
    DataSourceFlowServiceImpl dataSourceFlowService;

    @Resource
    IFlowTableService flowTableService;

    @Resource
    IFlowRelationService flowRelationService;


    @Override
    public void process() {

        Map<String, Set<String>> idAndProperty = taoSnit.idAndProperty;
        //获取上下文
        ResultContext resultContext = this.getContextBean("resultContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = resultContext.getTaskId();
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置查询失败");
        }
        //获取数据源id
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        DataSourceDto dataSourceDto = BeanUtil.toBean(collect, DataSourceDto.class);
        if (StrUtil.isNotBlank(dataSourceDto.getDataSourceId())){
            resultContext.setDataSourceId(dataSourceDto.getDataSourceId());
            //存储所有要查询的字段
            Set<String> fields = new TreeSet<>();
            //获取本次计算的公式
            List<String> formulaList = resultContext.getFormulaList();
            for (String s : formulaList) {
                if (CollUtil.isNotEmpty(idAndProperty.get(s))){
                    fields.addAll(idAndProperty.get(s));
                }
            }
            String tableId = UUIDUtils.uuidStdTableName();
            int tableCount = flowTableService.getTableCount("taosnit_flow_table");

            //创建需要查询数据的临时表
            createTale(tableId, fields);
            try {
                //连接taoS数据库查询数据
                insertTaoSData(tableId, dataSourceDto.getDataSourceId(), fields);
                //将生成的临时表名与组件流的关联起来
                flowViewService.insertFlowTable(Integer.parseInt(flowId), tableId, nodeId, taskId);
            } catch (Exception e) {
                log.error("{}数据源组件执行失败", flowId, e);
            }
            resultContext.setResultTable(tableId);
        }else {
            throw new BizException("DataSourceNode，数据源不存在");
        }
    }


    /**
     * 查询taoS库对应点位的数据并插入本地临时表中红
     * @param tableId 临时表id
     * @param dataSourceId 数据源id
     * @param fields 需要查询的点位代码
     */
    public void insertTaoSData(String tableId, String dataSourceId, Set<String> fields) {
        //查询taoS数据源连接信息
        DataSourceEntity dataSource = dataSourceFlowService.getDataSource(dataSourceId);
        Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(), dataSource.getDatabaseIp(),
                dataSource.getDatabasePort(), dataSource.getDb(), dataSource.getUserName(),
                dataSource.getPassword());
        StringBuilder sqlIn = new StringBuilder();
        for (String field : fields) {
            sqlIn.append("'").append(field).append("',");
        }
        sqlIn.deleteCharAt(sqlIn.lastIndexOf(","));
        StringBuilder sb = new StringBuilder();
        sb.append("select ts, itemvalue, devproperty from test.xych where devproperty in (").append(sqlIn).append(" ) limit 6000");
        try {
            ResultSet rs = DBTools.executeQuerySql(con, sb.toString());
            List<Map<String, String>> resultList = new ArrayList<>();
            while (true) {
                assert rs != null;
                if (!rs.next()) break;    // 判断是否还有下一个数据
                HashMap<String, String> map = new HashMap<>();
                // 根据字段名获取相应的值
                String code = rs.getString("devproperty");
                String itemValue = rs.getString("itemvalue");
                map.put(code, itemValue);
                String ts = rs.getString("ts");
                map.put("ts", ts);
                //输出查到的记录的各个字段的值
                resultList.add(map);
            }
            MapToDbUtils.mapToDb(resultList, tableId);
        }catch (Exception e) {
            log.error("查询失败");
            throw new BizException("DataSourceNode：查询数据失败");
        }finally {
            try {
                assert con != null;
                con.close();
            } catch (SQLException e) {
                log.error("关闭数据库失败，{}", e.getMessage(), e);
                throw new BizException("DataSourceNode：关闭数据库失败");
            }
        }

    }

    /**
     * 创建临时表
     * @param tableId 临时表id
     * @param fields 临时表字段
     */
    private void createTale(String tableId, Set<String> fields){
        StringBuilder sb = new StringBuilder();
        sb.append("create table ").append(tableId).append("( ts varchar(255) ,");
        for (String field : fields) {
            sb.append(field).append(" varchar(255) ,");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        sb.append(")");
        Connection connection = null;
        try {
            connection = hikariDataSource.getConnection();
            DBTools.executeSql(connection, sb.toString());
        }catch (Exception e){
            log.error("sql执行失败", e);
            throw new BizException("DataSourceNode：sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }

}
