package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CalculationDto;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;
import com.siact.energy.cal.common.pojo.enums.FormulaEnum;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.entity.flow.DataSourceEntity;
import com.siact.energy.cal.server.entity.flow.RuleDetailEntity;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.DataSourceFlowServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.siact.energy.cal.server.service.flow.impl.RuleDetailFlowServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 指标计算组件
 * @Version: 1.0
 */
@Component("CommonNodeGroup")
@Slf4j
public class CalculationGroupComponent extends NodeComponent  {

    @Resource
    RuleDetailFlowServiceImpl ruleDetailFlowService;
    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    IFlowRelationService flowRelationService;

    @Resource
    DataSourceFlowServiceImpl dataSourceFlowService;

    @Override
    public void process() {
        ResultContext anyName = this.getContextBean("resultContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = anyName.getTaskId();
        String dataSourceId = anyName.getDataSourceId();
        if (StrUtil.isBlank(dataSourceId)){
            throw new BizException("未配置taoS数据源");
        }
        List<ParamDto> paramList = anyName.getParamList();
        CalculationDto calculationDto = new CalculationDto();
        if (!paramList.isEmpty()){
            Map<String, Object> paramMap = paramList.stream().collect(Collectors.toMap(ParamDto::getParamKey, ParamDto::getParamValue));
            calculationDto = BeanUtil.toBean(paramMap, CalculationDto.class);
        }
        //获取计算组件的页面配置信息
        List<String> calFormulaList = getCalFormula(flowId, nodeId);
        List<List<Map<String,Object>>> dataList = new ArrayList<>();
        String tableName = UUIDUtils.uuidStdTableName();
        if (!calFormulaList.isEmpty()){
            Set<String> fields = new TreeSet<>();
            for (String formulaId : calFormulaList) {
                //Optional<RuleDetailEntity> ruleDetailOption = ruleDetailFlowService.getRuleDetailOption(formulaId);
                Optional<RuleDetailEntity> ruleDetailOption = ruleDetailFlowService.getRuleDetailOption("1802957964350427137");
                if(ruleDetailOption.isPresent()){
                    fields.add(ruleDetailOption.get().getDevCode());
                    if (ruleDetailOption.get().getCalType() == 2){
                        //如果是聚合指标，则通过根据聚合指标的方式拼接taos数据库执行语句
                        //1、根据公式输出字段创建对应的临时表
                        fields.add(ruleDetailOption.get().getDevCode());
                        //2、转换sql，执行sql
                        String ruleFormula = ruleDetailOption.get().getRuleFormula();
                        String sql = "";
                        String taoSSql = getTaoSSql(ruleFormula, "test.xych", calculationDto, ruleDetailOption.get().getDevCode(), sql);
                        sql = "SELECT SUM(itemvalue) as PGY02_SSXT1_ST00000_U00000_EQ000000000_MP0000  FROM test.xych WHERE  ts>='2024-06-01 00:00:00' and ts<='2024-06-10 00:00:00' INTERVAL(1d) FILL(PREV);";

                        List<Map<String, Object>> resultList = getRuleDetailOption(sql, dataSourceId);
                        dataList.add(resultList);

                    }else {
                        throw new BizException("CommonNode:配置的公式不是聚合类指标");
                    }
                }
            }
            //3、生成临时表；
            createTale(tableName, fields);
            List<Map<String, Object>> maps = MapToDbUtils.mergeList(dataList, "ts");
            //4、将数据插入临时表中
            MapToDbUtils.insertDbList(maps, tableName);
            flowViewService.insertFlowTable(Integer.parseInt(flowId), tableName, nodeId, taskId);
            anyName.setResultTable(tableName);

        }else {
            throw new BizException("CommonNode:配置的公式不存在");
        }
        log.info("指标计算组件执行完毕");
    }

    /**
     * 获取公式
     * @param flowId
     * @param nodeId
     * @return
     */
    private List<String> getCalFormula(String flowId, String nodeId) {
        LambdaQueryWrapper<SiComRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SiComRelationEntity::getNodeId, nodeId);
        wrapper.eq(SiComRelationEntity::getFlowId, flowId);
        SiComRelationEntity comRelationEntity = flowRelationService.getOne(wrapper);
        List<CommonNodeDto> list = JSONUtil.toList(comRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        CalculationDto calculation = BeanUtil.toBean(collect, CalculationDto.class);
        return calculation.getCalFormula();
    }

    private List<Map<String, Object>> getRuleDetailOption(String sql, String dataSourceId){
        List<Map<String, Object>> list = new ArrayList<>();
        //获取数据源
        DataSourceEntity dataSource = dataSourceFlowService.getDataSource(dataSourceId);
        Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(), dataSource.getDatabaseIp(),
                dataSource.getDatabasePort(), dataSource.getDb(), dataSource.getUserName(),
                dataSource.getPassword());
        try {
            ResultSet rs = DBTools.executeQuerySql(con, sql);
            while (true){
                if (!rs.next()){
                    break;
                }
                HashMap<String, Object> map = new HashMap<>();
                map.put("ts", rs.getObject(1).toString());
                map.put("value", rs.getObject(2).toString());
                list.add(map);
            }
            return list;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }finally {
            try {
                assert con != null;
                con.close();
            } catch (SQLException e) {
                throw new BizException("关闭连接失败");
            }
        }
    }
    @Resource
    HikariDataSource hikariDataSource;

    /**
     * 创建临时表
     * @param tableId 临时表id
     * @param fields 临时表字段
     */
    private  void createTale(String tableId, Set<String> fields){
        StringBuilder sb = new StringBuilder();
        sb.append("create table ").append(tableId).append("( ts varchar(255) ,");
        for (String field : fields) {
            sb.append(field).append(" DECIMAL(30,10) ,");
        }
        sb.deleteCharAt(sb.lastIndexOf(","));
        sb.append(")");
        Connection connection = null;
        try {
            connection = hikariDataSource.getConnection();
            DBTools.executeSql(connection, sb.toString());
        }catch (Exception e){
            log.error("sql执行失败", e);
            throw new BizException("DataSourceNode：sql执行失败");
        }finally {
            DBConnection.close(connection);
        }
    }

    private String getTaoSSql(String ruleFormula, String dbName,  CalculationDto calculationDto, String devCode, String devProperty){
        String sql = null;
        String startTime = StrUtil.isEmpty(calculationDto.getStartTime())?"2024-07-01 00:00:00": calculationDto.getStartTime();
        String endTime = StrUtil.isEmpty(calculationDto.getEndTime())?"2024-07-10 00:00:00": calculationDto.getEndTime();
        String ts = StrUtil.isEmpty(calculationDto.getTs())?"1": calculationDto.getTs();
        String tsUnit = StrUtil.isEmpty(calculationDto.getTsUnit())?"d": calculationDto.getTsUnit();
        if (ruleFormula.contains(FormulaEnum.MAX.getCode())){
            sql = StrUtil.format(FormulaEnum.MAX.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }else if (ruleFormula.contains(FormulaEnum.MIN.getCode())){
            sql = StrUtil.format(FormulaEnum.MIN.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }else if (ruleFormula.contains(FormulaEnum.FIRST.getCode())){
            sql = StrUtil.format(FormulaEnum.FIRST.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }else if (ruleFormula.contains(FormulaEnum.LAST.getCode())){
            sql = StrUtil.format(FormulaEnum.LAST.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }else if (ruleFormula.contains(FormulaEnum.AVG.getCode())){
            sql = StrUtil.format(FormulaEnum.AVG.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }else if (ruleFormula.contains(FormulaEnum.DIFF.getCode())){
            sql = StrUtil.format(FormulaEnum.DIFF.getFormula(),devProperty, dbName, devCode, startTime, endTime, ts, tsUnit);
        }
        return  sql;
    }



}
