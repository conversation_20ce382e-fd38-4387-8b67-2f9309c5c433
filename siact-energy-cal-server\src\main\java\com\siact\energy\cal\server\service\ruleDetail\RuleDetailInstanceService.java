package com.siact.energy.cal.server.service.ruleDetail;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.pojo.enums.ActiveStateEnum;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.DeletedEnum;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleFormulaDetail;
import com.siact.energy.cal.server.dao.ruleDetail.RuleDetailInstanceDao;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleDetailInstanceService extends ServiceImpl<RuleDetailInstanceDao, RuleDetailInstance> {

    @Autowired
    private RuleDetailInstanceDao ruleDetailInstanceDao;

    /**
     * 根据设备属性编码获取公式
     */
    @Cacheable(value = "formulaCache", key = "#devProperty + ':' + #ruleColId", unless = "#result == null")
    public String getFormula(String devProperty, Long ruleColId) {
        log.debug("Formula cache miss for: {}, ruleColId: {}", devProperty, ruleColId);

        if (StrUtil.isEmpty(devProperty) || ruleColId == null) {
            return null;
        }

        // 优先查询实例级公式
        RuleDetailInstance instance = ruleDetailInstanceDao.selectOne(
                new QueryWrapper<RuleDetailInstance>()
                        .eq("dev_property", devProperty)
                        .eq("rule_col_id", ruleColId)
                        .eq("rule_type", 0) // 实例级公式
                        .eq("active_state", 0) // 已激活
                        .eq("deleted", 0) // 未删除
                        .orderByDesc("update_time")
                        .last("LIMIT 1")
        );
        // 如果没找到实例级公式，尝试查询模型级公式
        if (instance == null) {
            instance = ruleDetailInstanceDao.selectOne(
                    new QueryWrapper<RuleDetailInstance>()
                            .eq("dev_property", devProperty)
                            .eq("rule_col_id", ruleColId)
                            .eq("rule_type", 1) // 模型级公式
                            .eq("active_state", 0) // 已激活
                            .eq("deleted", 0) // 未删除
                            .orderByDesc("update_time")
                            .last("LIMIT 1")
            );
        }

        return instance != null ? instance.getRuleFormula() : null;
    }

    /**
     * 批量获取公式
     */
    @Cacheable(value = "formulaBatchCache", key = "'batch_' + #ruleColId + '_' + T(java.util.Arrays).hashCode(#devProperties.toArray())")
    public Map<String, String> batchGetFormulas(Collection<String> devProperties, Long ruleColId) {
        if (devProperties == null || devProperties.isEmpty() || ruleColId == null) {
            return Collections.emptyMap();
        }

        log.debug("Batch formula cache miss for {} properties, ruleColId: {}", devProperties.size(), ruleColId);

        Map<String, String> result = new HashMap<>();

        // 1. 查询实例级公式
        List<RuleDetailInstance> insInstances = ruleDetailInstanceDao.selectList(
                new QueryWrapper<RuleDetailInstance>()
                        .in("dev_property", devProperties)
                        .eq("rule_col_id", ruleColId)
                        .eq("rule_type", 0) // 实例级
                        .eq("active_state", 0)
                        .eq("deleted", 0)
                        .orderByDesc("update_time")
        );

        // 记录已找到实例级公式的属性
        Set<String> foundProps = insInstances.stream()
                .map(RuleDetailInstance::getDevProperty)
                .collect(Collectors.toSet());

        // 将实例级公式添加到结果
        insInstances.forEach(instance ->
                result.put(instance.getDevProperty(), instance.getRuleFormula()));

        // 2. 查询剩余属性的模型级公式
        Set<String> remainingProps = devProperties.stream()
                .filter(prop -> !foundProps.contains(prop))
                .collect(Collectors.toSet());

        if (!remainingProps.isEmpty()) {
            List<RuleDetailInstance> modelInstances = ruleDetailInstanceDao.selectList(
                    new QueryWrapper<RuleDetailInstance>()
                            .in("dev_property", remainingProps)
                            .eq("rule_col_id", ruleColId)
                            .eq("rule_type", 1) // 模型级
                            .eq("active_state", 0)
                            .eq("deleted", 0)
                            .orderByDesc("update_time")
            );

            // 将模型级公式添加到结果
            modelInstances.forEach(instance ->
                    result.put(instance.getDevProperty(), instance.getRuleFormula()));
        }

        return result;
    }


    /**
     * 从数据库获取计算类型配置映射
     * @param projectCode 项目编码
     * @param ruleColId 规则列ID
     * @return 计算类型到属性列表的映射
     */
    @Cacheable(value = "calTypeConfigCache", key = "#projectCode + ':' + #ruleColId", unless = "#result.isEmpty()")
    public Map<Integer, List<String>> getCalTypeConfigMap(String projectCode, Long ruleColId) {
        Map<Integer, List<String>> calTypeConfigMap = new HashMap<>();

        try {
            // 查询条件：
            // 1. 匹配项目编码
            // 2. 匹配规则列ID
            // 3. 只选择活跃的记录
            // 4. 排除已删除的记录
            List<RuleDetailInstance> rules = ruleDetailInstanceDao.selectListByProjectAndCol(
                    projectCode, ruleColId, ActiveStateEnum.ENABLE.getValue(), DeletedEnum.NORMAL.getValue());

            if (CollUtil.isNotEmpty(rules)) {
                // 按计算类型分组
                Map<Integer, List<RuleDetailInstance>> groupedRules = rules.stream()
                        .collect(Collectors.groupingBy(RuleDetailInstance::getCalType));

                // 转换为所需的Map<Integer, List<String>>格式
                for (Map.Entry<Integer, List<RuleDetailInstance>> entry : groupedRules.entrySet()) {
                    List<String> properties = entry.getValue().stream()
                            .map(RuleDetailInstance::getDevProperty)
                            .distinct()
                            .collect(Collectors.toList());

                    calTypeConfigMap.put(entry.getKey(), properties);
                }

                log.debug("从数据库获取计算类型配置: 项目={}, 规则列={}, 类型数量={}",
                        projectCode, ruleColId, calTypeConfigMap.size());
            }
        } catch (Exception e) {
            log.error("获取计算类型配置失败: 项目={}, 规则列={}", projectCode, ruleColId, e);
        }

        return calTypeConfigMap;
    }
    /**
     * 清除公式缓存
     */
    @CacheEvict(value = "formulaCache", key = "#devProperty + ':' + #ruleColId")
    public void evictFormulaCache(String devProperty, Long ruleColId) {
        log.debug("Evicting formula cache for: {}, ruleColId: {}", devProperty, ruleColId);
    }

    /**
     * 清除所有公式缓存
     */
    @CacheEvict(value = {"formulaCache", "formulaBatchCache"}, allEntries = true)
    public void clearAllFormulaCaches() {
        log.info("Cleared all formula caches");
    }

    /**
     * 兼容旧方法
     */
    public String getFormulaFromRedis(String devProperty) {
        return getFormula(devProperty, ConstantBase.RULECOLID_COMMON);
    }
    public void saveToRuleDetailInstance(RuleDetailVo ruleDetailVo) {
        // 构建RuleDetailInstance对象
        RuleDetailInstance instance = new RuleDetailInstance();
        instance.setRuleDetailId(ruleDetailVo.getId());
        instance.setRuleName(ruleDetailVo.getRuleName());
        instance.setRuleDes(ruleDetailVo.getRuleDes());
        instance.setRuleType(ruleDetailVo.getRuleType());
        instance.setCalType(ruleDetailVo.getCalType());
        instance.setDevCode(ruleDetailVo.getDevCode());
        instance.setDevName(ruleDetailVo.getDevName());
        instance.setDevProperty(ruleDetailVo.getDevProperty());
        instance.setPropName(ruleDetailVo.getPropName());
        instance.setRuleFormula(ruleDetailVo.getRuleFormula()); // 将List<String>转换为String
        instance.setRuleFormulaShow(StrUtil.join(ruleDetailVo.getRuleFormulaShow(), ""));
        instance.setProjectId(ruleDetailVo.getProjectId());
        instance.setRuleColId(ruleDetailVo.getRuleColId());
        instance.setActiveState(ruleDetailVo.getActiveState());
        instance.setDeleted(ruleDetailVo.getDeleted());

        // 构建查询参数
        Set<String> devProperties = new HashSet<>();
        devProperties.add(ruleDetailVo.getDevProperty());

        // 查询
        List<RuleDetailInstance> existingInstances = ruleDetailInstanceDao.findInstancesByKeyFields(
                ruleDetailVo.getId(),  // ruleDetailId
                ruleDetailVo.getRuleColId(),  // ruleColId
                ruleDetailVo.getRuleType(),  // ruleType
                devProperties  // devProperties
        );

        // 判断是否存在
        RuleDetailInstance existingInstance = existingInstances.isEmpty() ? null : existingInstances.get(0);
        if (existingInstance != null) {
            instance.setId(existingInstance.getId());
            updateById(instance);
        } else {
            save(instance);
        }
    }

    public List<RuleDetailInstance> findInstancesByKeyFields(Long modelRuleDetailId, Long modelRuleColId, Integer sourceRuleType, Set<String> instanceDevProperties) {
        return ruleDetailInstanceDao.findInstancesByKeyFields(modelRuleDetailId, modelRuleColId, sourceRuleType, instanceDevProperties);
    }
}