package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 数据源连接
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DataSourceDto {
    @ApiModelProperty(value = "数据源类型：taos、MQTT", position = 1)
    private String type;
    @ApiModelProperty(value = "组件名称", position = 2)
    private String flowName;
    @ApiModelProperty(value = "数据连接名称", position = 3, example = "taoS数据库")
    private String name;
    @ApiModelProperty(value = "ip", position = 4, example = "127.0.0.1")
    private String ip;
    @ApiModelProperty(value = "数据库连接名称", position = 5, example = "3306")
    private String port;
    @ApiModelProperty(value = "数据库连接用户名", position = 6, example = "root")
    private String userName;
    @ApiModelProperty(value = "数据库连接密码", position = 7, example = "root")
    private String password;
    @ApiModelProperty(value = "表名称", position = 8, example = "test")
    private String tableName;
    @ApiModelProperty(value = "组件描述", position = 9)
    private String des;
    @ApiModelProperty(value = "数据源id", position = 10)
    private String dataSourceId;
    @ApiModelProperty(value = "broker地址", position = 11)
    private String broker;
    @ApiModelProperty(value = "topic", position = 12)
    private String topic;
    @ApiModelProperty(value = "数据库名称", position = 8, example = "test")
    private String dbName;
    @ApiModelProperty(value = "自定义sql", position = 14)
    private String sql;

}
