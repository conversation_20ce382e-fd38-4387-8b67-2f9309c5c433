package com.siact.energy.cal.tool.service.ruleCol.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColUpdateDTO;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.common.util.utils.ClassUtil;
import com.siact.energy.cal.tool.convertor.ruleCol.RuleColConvertor;
import com.siact.energy.cal.tool.dao.ruleCol.RuleColDao;
import com.siact.energy.cal.tool.entity.ruleCol.RuleCol;
import com.siact.energy.cal.tool.service.ruleCol.RuleColService;
import com.siact.energy.cal.tool.service.dataProject.DataProjectService;
import com.siact.energy.cal.common.util.utils.InsCodeUtil;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.LinkedHashMap;

/**
 * 指标集表(RuleCol)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:13
 */
@Service("ruleColService")
public class RuleColServiceImpl extends ServiceImpl<RuleColDao, RuleCol> implements RuleColService {

  /*  @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;*/

    @Autowired
    private RuleColDao ruleColDao;

    @Autowired
    private DataProjectService dataProjectService;



    @Override
    public PageBean<RuleColVO> listPage(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO) {
        // 先获取分页数据
        PageBean<RuleColVO> result = page(page, ruleColQueryDTO);

        // 过滤每个指标集的attrList
        if (result.getRecords() != null) {
            result.getRecords().forEach(ruleColVO -> {
                if (ruleColQueryDTO.getProjectId() == null) {
                    // 如果projectId为空，则属性列表也为空
                    ruleColVO.setAttrList(new ArrayList<>());
                } else {
                    // 如果指定了项目ID，则过滤attrList
                    DataProjectVO project = dataProjectService.getVoById(ruleColQueryDTO.getProjectId());
                    if (project != null && ruleColVO.getAttrList() != null) {
                        String targetProjectCode = project.getProjectCode();

                        // 先转换整个列表，再进行过滤
                        List<AttrObject> convertedAttrList = convertAttrList(ruleColVO.getAttrList());
                        List<AttrObject> filteredAttrList = convertedAttrList.stream()
                            .filter(attr -> attr != null && belongsToProject(attr.getAttrCode(), targetProjectCode))
                            .collect(Collectors.toList());
                        ruleColVO.setAttrList(filteredAttrList);
                    } else {
                        // 项目不存在时，设置为空列表
                        ruleColVO.setAttrList(new ArrayList<>());
                    }
                }
            });
        }

        return result;
    }

    @Override
    public RuleColVO getVoById(Serializable id) {
        RuleCol ruleCol = getById(id);
        return RuleColConvertor.INSTANCE.entity2Vo(ruleCol);
    }

    @Override
    public Boolean save(RuleColInsertDTO ruleColInsertDTO) {
        RuleCol ruleCol = RuleColConvertor.INSTANCE.insertDTO2Entity(ruleColInsertDTO);
        return save(ruleCol);
    }

    @Override
    public Boolean updateVoById(RuleColUpdateDTO ruleColUpdateDTO) {
        RuleCol ruleCol = RuleColConvertor.INSTANCE.updateDTO2Entity(ruleColUpdateDTO);
        return updateById(ruleCol);
    }

    @Override
    public boolean toggle(Integer activeState, List<Long> ids) {

        LambdaUpdateWrapper<RuleCol> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(RuleCol::getId, ids)
                .set(RuleCol::getActiveState, activeState);

        return update(updateWrapper);

    }

    @Override
    public List<RuleColVO> listAll() {
        // 创建查询条件，只获取激活状态的指标集
        LambdaQueryWrapper<RuleCol> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleCol::getActiveState, 0); // 0-激活,1-未激活
        // 获取所有激活的指标集并转换为VO
        return RuleColConvertor.INSTANCE.entities2Vos(list(queryWrapper));
    }

    public void updateAttrList(Long ruleColId, List<AttrObject> inputList) {
        if (ruleColId == null) {
            throw new IllegalArgumentException("规则ID不能为空");
        }

        // 获取规则列
        RuleCol ruleCol = getById(ruleColId);
        if (ruleCol == null) {
            throw new RuntimeException("未找到ID为" + ruleColId + "的规则列");
        }

        // 合并现有属性列表
        List<AttrObject> existingAttrList = ruleCol.getAttrList();
        if (existingAttrList != null && !existingAttrList.isEmpty()) {

            inputList.addAll(existingAttrList);
        }

        // 使用Map去重
        Map<String, AttrObject> uniqueAttrs = new HashMap<>();
        for (AttrObject attrObject : inputList) {
            uniqueAttrs.put(attrObject.getAttrCode(), attrObject);
        }

        // 设置去重后的属性列表
        List<AttrObject> finalAttrList = new ArrayList<>(uniqueAttrs.values());
        ruleCol.setAttrList(finalAttrList);

        // 更新到数据库
        updateById(ruleCol);
    }


    /**
     * 分页查询
     *
     * @param page            分页对象
     * @param ruleColQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<RuleColVO> page(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO) {

        // 转换器
        RuleColConvertor convertor = RuleColConvertor.INSTANCE;
        // VO转实体
        RuleCol ruleCol = convertor.queryDTO2Entity(ruleColQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<RuleCol> queryWrapper = new LambdaQueryWrapper<>(ruleCol);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(RuleColVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<RuleCol> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<RuleColVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }



    /**
     * 转换属性列表，处理类型转换问题
     *
     * @param attrList 原始属性列表
     * @return 转换后的AttrObject列表
     */
    private List<AttrObject> convertAttrList(List<AttrObject> attrList) {
        if (attrList == null) {
            return new ArrayList<>();
        }

        List<AttrObject> result = new ArrayList<>();
        for (Object obj : attrList) {
            AttrObject converted = convertToAttrObject(obj);
            if (converted != null) {
                result.add(converted);
            }
        }
        return result;
    }

    /**
     * 将对象转换为AttrObject
     * 处理MyBatis Plus JacksonTypeHandler反序列化时的类型问题
     *
     * @param obj 可能是AttrObject或LinkedHashMap的对象
     * @return AttrObject对象，转换失败时返回null
     */
    @SuppressWarnings("unchecked")
    private AttrObject convertToAttrObject(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof AttrObject) {
            // 如果已经是AttrObject类型，直接返回
            return (AttrObject) obj;
        }

        if (obj instanceof LinkedHashMap) {
            try {
                // 如果是LinkedHashMap，手动转换
                LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) obj;
                AttrObject attrObject = new AttrObject();
                attrObject.setAttrCode((String) map.get("attrCode"));
                attrObject.setAttrName((String) map.get("attrName"));
                return attrObject;
            } catch (Exception e) {
                // 转换失败时返回null
                return null;
            }
        }

        // 其他类型无法转换
        return null;
    }

    /**
     * 判断属性编码是否属于指定项目
     *
     * @param attrCode 属性编码
     * @param targetProjectCode 目标项目编码
     * @return true-属于该项目，false-不属于该项目
     */
    private boolean belongsToProject(String attrCode, String targetProjectCode) {
        if (attrCode == null || targetProjectCode == null) {
            return false;
        }

        try {
            // 使用InsCodeUtil提取属性编码中的项目编码
            String attrProjectCode = InsCodeUtil.getProjectDataCode(attrCode);
            return targetProjectCode.equals(attrProjectCode);
        } catch (Exception e) {
            // 如果编码格式不正确，默认不过滤（显示该属性）
            return true;
        }
    }



}

