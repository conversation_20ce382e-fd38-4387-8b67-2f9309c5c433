// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbcommon.database.impl;

import javax.sql.DataSource;

/**
 * Greenplum数据库实现类
 *
 * <AUTHOR>
 */
public class GreenplumDatabaseOperator extends PostgreSqlDatabaseOperator {

  public GreenplumDatabaseOperator(DataSource dataSource) {
    super(dataSource);
  }
}
