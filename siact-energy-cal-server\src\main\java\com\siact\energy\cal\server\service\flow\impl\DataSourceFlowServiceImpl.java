package com.siact.energy.cal.server.service.flow.impl;

import com.siact.energy.cal.server.dao.flow.DataSourceMapper;
import com.siact.energy.cal.server.entity.flow.DataSourceEntity;
import com.siact.energy.cal.server.service.flow.IDataSourceFlowService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-31
 * @Description: 数据源实现类
 * @Version: 1.0
 */
@Service
public class DataSourceFlowServiceImpl implements IDataSourceFlowService {
    @Resource
    DataSourceMapper dataSourceMapper;
    @Override
    public DataSourceEntity getDataSource(String id) {
        return  dataSourceMapper.selectById(id);
    }
}
