package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.DataSourceDto;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-16
 * @Description: 导入数据源组件
 * @Version: 1.0
 */
@Component("ImportDataSourceNode")
@Slf4j
public class ImportDataSourceComponent extends NodeComponent {


    @Resource
    FlowViewServiceImpl flowViewService;

    @Resource
    IFlowRelationService flowRelationService;


    @Override
    public void process() {
        //获取上下文
        ResultContext resultContext = this.getContextBean("resultContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置查询失败");
        }
        //组件配置信息
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        DataSourceDto dataSourceDto = BeanUtil.toBean(collect, DataSourceDto.class);
        String tableId = UUIDUtils.uuidStdTableName();
        String sql = null;
        if (StrUtil.isBlank(dataSourceDto.getSql())){
            sql = "create table "+tableId +  " AS select * from "+ dataSourceDto.getTableName();
        }else {
            sql = "create table "+tableId + dataSourceDto.getSql();
        }
        Connection connection = null;
        try {
            connection = DBConnection.connection(dataSourceDto.getType(), dataSourceDto.getIp(), dataSourceDto.getPort(), dataSourceDto.getDbName(), dataSourceDto.getUserName(), dataSourceDto.getPassword());
            DBTools.executeSql(connection,sql);
        }catch (Exception e){
            throw new BizException("导入数据源失败");
        }finally {
            DBConnection.close(connection);
        }
        resultContext.setResultTable(tableId);
        flowViewService.insertFlowTable(Integer.parseInt(flowId), tableId, nodeId, resultContext.getTaskId());
    }
}
