package com.siact.energy.cal.common.pojo.vo.flow;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-20
 * @Description: 组件流执行返回结果VO
 * @Version: 1.0
 */
@ApiModel("组件流执行返回结果VO")
@Data
public class FlowRunResultVO {

    @ApiModelProperty(value = "组件id", position = 1, example = "1")
    private String flowId;

    @ApiModelProperty(value = "组件名称", position = 2, example = "断面流量计算")
    private String flowName;

    @ApiModelProperty(value = "组件详细数据", position = 3)
    private Map<String, FlowRunResultDetailVO> nodeResult;
}
