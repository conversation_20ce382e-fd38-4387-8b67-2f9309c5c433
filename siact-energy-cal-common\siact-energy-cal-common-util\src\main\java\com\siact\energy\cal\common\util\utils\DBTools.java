package com.siact.energy.cal.common.util.utils;


import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 数据库连接工具类
 * @Version: 1.0
 */
@Slf4j
public class DBTools {

    public static ResultSet executeQuerySql(Connection con, String sql) {
        log.info(sql);
        if (con == null)
           // log.error("查询的con为空");
            return null;
        try {
            Statement stmt = con.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            return rs;
        } catch (SQLException e) {
           e.printStackTrace();
            throw new BizException("sql执行失败：executeQuerySql");
        }
    }

    /**
     * 执行创建试图语句
     * @param con
     * @param sql
     */
    public static void executeQuerySqlView(Connection con, String sql) {
        if (con == null)
           return;
        try {
            Statement stmt = con.createStatement();
            stmt.execute(sql);
        } catch (SQLException e) {
            throw new BizException("sql执行失败:executeQuerySqlView");
        }
    }

    public static void executeSql(Connection con, String sql){
        log.info(sql);
        if (con == null || StrUtil.isBlank(sql))
        {
            return;
        }
        Statement stmt = null;
        try {
            stmt = con.createStatement();
            stmt.execute(sql);
            con.setAutoCommit(true);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new BizException("sql执行失败:executeSql");
        }finally {
            try {
                assert stmt != null;
                stmt.close();
            } catch (SQLException e) {
                throw  new BizException("sql连接关闭失败:") ;
            }
        }
    }

    public static void executeUpdateSql(Connection con, String sql){
        log.info(sql);
        if (con == null)
        {
            return;
        }
        Statement stmt = null;
        try {
            stmt = con.createStatement();
            stmt.executeUpdate(sql);
            con.setAutoCommit(true);
        } catch (SQLException e) {
            throw new BizException("sql执行失败:executeUpdateSql");
        }finally {
            try {
                assert stmt != null;
                stmt.close();
            } catch (SQLException e) {
                throw new BizException("sql连接关闭失败:executeUpdateSql");
            }
        }
    }

    public static void executeSql(String sql, DataSource dataSource){
        try (Connection connection = dataSource.getConnection()) {
            Statement statement = connection.createStatement();
            statement.execute(sql);
        }catch (Exception e){
            log.error("sql执行失败", e);
        }
    }

    public static void executeBatchSql(Connection con, List<String> sqlList){
        if (con == null || sqlList == null || sqlList.isEmpty())
        {
            return;
        }
        Statement stmt = null;
        try {
            stmt = con.createStatement();
            for (String sql : sqlList) {
                stmt.addBatch(sql);
            }
            stmt.executeBatch();
            stmt.clearBatch();
            con.setAutoCommit(true);
        } catch (SQLException e) {
            e.printStackTrace();
            throw new BizException("sql执行失败:executeSql");
        }finally {
            try {
                assert stmt != null;
                stmt.close();
            } catch (SQLException e) {
                throw  new BizException("sql连接关闭失败:") ;
            }
        }
    }
}
