package com.siact.energy.cal.common.pojo.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * DigitalTwinTreeVO
 *
 * <AUTHOR>
 * @since 2024-05-21 13:55:55
 */
@ApiModel("数字孪生树VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DigitalTwinTreeVO {

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", position = 1)
    private String name;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型", position = 2)
    private String nodeType;

    /**
     * 数字化编码
     */
    @ApiModelProperty(value = "数字化编码", position = 3)
    private String dataCode;

    /**
     * 子数据列表
     */
    @ApiModelProperty(value = "子数据列表", position = 4)
    private List<DigitalTwinTreeVO> children = new ArrayList<>();

}
