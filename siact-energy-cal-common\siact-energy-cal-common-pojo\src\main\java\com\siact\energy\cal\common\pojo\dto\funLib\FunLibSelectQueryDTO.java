package com.siact.energy.cal.common.pojo.dto.funLib;

import com.siact.energy.cal.common.pojo.enums.CalTypeEnum;
import com.siact.energy.cal.common.pojo.validator.EnumValidator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * FunLibSelectQueryDTO
 *
 * <AUTHOR>
 * @since 2024-06-11 09:53:49
 */
@ApiModel("常用函数库选择查询DTO")
@Data
public class FunLibSelectQueryDTO {

    /**
     * 计算类型（1-基础指标，2-聚合指标）
     */
    @ApiModelProperty(value = "计算类型（1-基础指标，2-聚合指标）", required = true, position = 1)
    @NotNull
    @EnumValidator(enumClass = CalTypeEnum.class, existGetMethod = "getValue", message = "计算类型无效")
    private Integer calType = CalTypeEnum.BASE.getValue();
}
