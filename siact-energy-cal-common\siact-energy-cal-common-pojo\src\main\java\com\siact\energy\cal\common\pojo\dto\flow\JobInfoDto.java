package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-10
 * @Description: 任务管理dto
 * @Version: 1.0
 */
@Data
@ApiModel("任务管理dto")
public class JobInfoDto {
    @ApiModelProperty(value = "任务id", example = "1", required = true, position = 1)
    int id;
    @ApiModelProperty(value = "入参",position = 2)
    String executorParam;
    @ApiModelProperty(value = "执行器地址",position = 3)
    String addressList;

    @ApiModelProperty(value = "任务id",position = 4)
    Integer jobId;

    @ApiModelProperty(value = "任务类型",position = 5)
    Integer type;
}
