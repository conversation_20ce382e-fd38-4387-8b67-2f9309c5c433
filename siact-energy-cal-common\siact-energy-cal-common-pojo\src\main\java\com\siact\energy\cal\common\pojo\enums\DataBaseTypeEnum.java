package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.common.datasource.enums
 * @description:
 * @create 2024/9/7 15:49
 */
public enum DataBaseTypeEnum {


    /**
     * MySQL数据库类型
     */
    MYSQL(1,"MYSQL", "com.mysql.jdbc.Driver","/* ping */ SELECT 1", "jdbc:mysql://{host}:{port}/{database}?useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true"),

    /**
     * Oracle数据库类型
     */
    ORACLE(2, "ORACLE","oracle.jdbc.driver.OracleDriver","SELECT 'Hello' from DUAL", "jdbc:oracle:thin:@{host}:{port}:{database}"),

    /**
     * SQLServer 2000数据库类型
     */
    SQLSERVER2000(3,"SQLSERVER2000", "com.microsoft.sqlserver.jdbc.SQLServerDriver","SELECT 1+2 as a", "jdbc:sqlserver://{host}:{port};DatabaseName={database}"),

    /**
     * SQLServer数据库类型
     */
    SQLSERVER(4, "SQLSERVER","com.microsoft.sqlserver.jdbc.SQLServerDriver","SELECT 1+2 as a", "jdbc:sqlserver://{host}:{port};DatabaseName={database}"),

    /**
     * PostgreSQL数据库类型
     */
    POSTGRESQL(5, "POSTGRESQL","org.postgresql.Driver","SELECT 1", "jdbc:postgresql://{host}:{port}/{database}"),

    /**
     * [国产]达梦数据库类型
     */
    DM(6, "DM","dm.jdbc.driver.DmDriver","SELECT 'Hello' from DUAL", "jdbc:dm://{host}:{port}/{database}"),

    /**
     * [国产]人大金仓数据库类型
     */
    KINGBASE(7, "KINGBASE","com.kingbase8.Driver","SELECT 1", "jdbc:kingbase8://{host}:{port}/{database}"),

    /**
     * [国产]TDENGINE时序数据库
     */
    TDENGINE(8, "TDENGINE","com.taosdata.jdbc.rs.RestfulDriver", "select 1", "jdbc:TAOS-RS://{host}:{port}/{database}?charset=utf-8&timestampFormat=UTC"),

    /**
     * [国产]南大通用GBase8a数据库
     */
    GBASE8A(9, "GBASE8A","com.gbase.jdbc.Driver", "/* ping */ SELECT 1", "jdbc:gbase://{host}:{port}/{database}"),

    /**
     * HIVE数据库
     */
    HIVE(10,"HIVE", "org.apache.hive.jdbc.HiveDriver", "SELECT 1", "jdbc:hive2://{host}:{port}/{database}"),

    /**
     * SQLite数据库
     */
    SQLITE3(11, "SQLITE3","org.sqlite.JDBC", "SELECT 1", "jdbc:sqlite::resource:{file}");

    private Integer index;
    private String desc;
    private String driveClassName;
    private String testSql;
    private String url;

    public String getTestSql() {
        return testSql;
    }

    public String getUrl() {
        return url;
    }

    public String getDriveClassName() {
        return driveClassName;
    }

    DataBaseTypeEnum(Integer idx,String desc, String driveClassName, String testSql, String url) {
        this.index = idx;
        this.desc = desc;
        this.driveClassName = driveClassName;
        this.testSql = testSql;
        this.url = url;
    }

    public Integer getIndex() {
        return index;
    }
    public String getDesc() {
        return desc;
    }


    public static DataBaseTypeEnum getByIndex(Integer index) {
        return Arrays.stream(DataBaseTypeEnum.values()).filter(productTypeEnum -> productTypeEnum.getIndex().equals(index)).findFirst().orElse(DataBaseTypeEnum.MYSQL);
    }
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(DataBaseTypeEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDesc()).value(enumObj.getIndex()).build());
        }

        return list;
    }
}
