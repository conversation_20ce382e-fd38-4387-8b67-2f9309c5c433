package com.siact.energy.cal.common.pojo.vo.flow;

import com.siact.energy.cal.common.pojo.entity.IndicatorCal;
import com.siact.energy.cal.common.pojo.entity.IndicatorScreen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-09-03
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("自定义指标")
public class CustomIndicatorVO {
    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", position = 1, required = true)
    private String indicatorName;
    /**
     * 指标描述
     */
    @ApiModelProperty(value = "指标描述", position = 2, required = false, example = "")
    private String indicatorDesc;
    /**
     * 指标编码(数字孪生的长码)
     * TODO:暂时不传，之后需要再页面上和数字孪生中的长码联动
     */
    @ApiModelProperty(value = "指标编码", position = 3, required = false, example = "数字孪生长码")
    private String indicatorCode;
    /**
     * 指标筛选
     */
    @ApiModelProperty(value = "指标筛选", position = 4, required = true)
    private List<IndicatorScreen> indicatorScreenList;

    @ApiModelProperty(value = "指标计算", position = 5, required = false)
    private List<IndicatorCal> indicatorDataList;

    @ApiModelProperty(value = "窗口大小", position = 6, required = false)
    private Integer windowSize;

    @ApiModelProperty(value = "时间单位", position = 7, required = false)
    private String tsUnit;

    @ApiModelProperty(value = "结果筛选", position = 8, required = false)
    private String resultFilter;
}
