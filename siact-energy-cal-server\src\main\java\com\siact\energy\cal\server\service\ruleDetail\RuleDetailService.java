package com.siact.energy.cal.server.service.ruleDetail;


import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.List;

import com.siact.edu.common.pojo.dto.DriverProjectDTO;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.*;
import com.siact.energy.cal.common.pojo.vo.common.DigitalTwinTreeVO;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetail;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;

/**
 * 指标详情表(RuleDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
public interface RuleDetailService extends IService<RuleDetail> {

    /**
     * 注册列表
     *
     * @param page               分页对象
     * @param ruleDetailQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<RuleDetailVO> listPage(PageBean<RuleDetailVO> page, RuleDetailQueryDTO ruleDetailQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    RuleDetailVO getVoById(Serializable id);

    /**
     * 根据指标集id获取设备属性列表
     *
     * @return 查询结果
     */
    List<RuleDetailVO> getDatacodeByRuleColId(Long ruleColId);
    /**
     * 新增数据
     *
     * @param ruleDetailInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(RuleDetailInsertDTO ruleDetailInsertDTO);

    /**
     * 修改数据
     *
     * @param ruleDetailUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(RuleDetailUpdateDTO ruleDetailUpdateDTO);

    /**
     * 校验新增参数
     *
     * @param dto 参数对象
     */
    void verifyInsertParam(RuleDetailInsertDTO dto);

    /**
     * 校验更新参数
     *
     * @param dto 参数对象
     */
    void verifyUpdateParam(RuleDetailUpdateDTO dto);

    /**
     * 模型树
     *
     * @param dataCode 项目模型数字化编码
     * @return 模型树数据
     */
    List<DigitalTwinTreeVO> modelTree(String dataCode);

    /**
     * 实例树
     *
     * @param dataCode 项目实例数字化编码
     * @return 实例树数据
     */
    List<DigitalTwinTreeVO> insTree(String dataCode);

    /**
     * 清除模型树数据缓存
     *
     * @param dataCode 项目模型数字化编码
     */
    void clearModelTreeCache(String dataCode);

    /**
     * 清除实例树数据缓存
     *
     * @param dataCode 项目实例数字化编码
     */
    void clearInsTreeCache(String dataCode);

    /**
     * 新增数据
     *
     * @param dto 实体对象
     * @return 新增结果
     */
    boolean verifyExpression(RuleDetailVerifyExpressionDTO dto);

    /**
     * 启用/未启用
     *
     * @param activeState 启用/未启用
     * @param ids  主键集合
     * @return 返回
     */
    Boolean toggle(Integer activeState, List<Long> ids);

    List<RuleDetailVO> ruleList(String type);

    List<SelectOptionVO> getIndicatorList();

    boolean deleteByIds(List<Long> ids);

    /**
     * 根据项目ID获取指标ID列表
     *
     * @param projectId 项目ID
     * @return 指标ID列表
     */
    List<Long> getIdsByProjectId(Long projectId);

    /**
     * 根据设备属性、项目ID和指标集ID获取指标详情
     *
     * @param devProperty 设备属性编码
     * @param projectId 项目ID
     * @param ruleColId 指标集ID
     * @return 指标详情
     */
    RuleDetail getByDevProperty(String devProperty, Long projectId, Long ruleColId);

}

