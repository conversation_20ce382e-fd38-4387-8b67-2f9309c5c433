package com.siact.energy.cal.server.entity.dataTrans;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.siact.energy.cal.common.datasource.common.BaseEntity;

/**
 * 数据传输表（mqtt）(DataTrans)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:36
 */
@Data
public class DataTrans extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 主机名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String host;

    /**
     * 端口
     */
    @TableField(condition = SqlCondition.LIKE)
    private String port;

    /**
     * 用户名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String userName;

    /**
     * 密码
     */
    @TableField(condition = SqlCondition.LIKE)
    private String password;

    /**
     * 主题
     */
    @TableField(condition = SqlCondition.LIKE)
    private String topic;

}

