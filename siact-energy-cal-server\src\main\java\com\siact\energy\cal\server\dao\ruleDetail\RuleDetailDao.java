package com.siact.energy.cal.server.dao.ruleDetail;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailQueryDTO;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetail;

/**
 * 指标详情表(RuleDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Repository
public interface RuleDetailDao extends BaseMapper<RuleDetail> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RuleDetail> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<RuleDetail> entities);

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param ruleDetailQueryDTO 查询参数
     * @return 查询结果
     */
    PageBean<RuleDetail> listPage(PageBean<RuleDetailVO> page,
                                    @Param("query") RuleDetailQueryDTO ruleDetailQueryDTO);

    List<RuleDetailVo> queryByCalType(Integer calType);

    List<RuleDetailVo> getAllFormulaDetail();

    List<RuleDetailVo> getRuleListByIds(List<Long> ids);

    RuleDetailVo getFormulaDetailByDevproperty(@Param("projectId")Long projectid,@Param("devProperty")String devproperty);
}

