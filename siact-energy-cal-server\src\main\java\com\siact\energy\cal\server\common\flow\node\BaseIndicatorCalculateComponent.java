package com.siact.energy.cal.server.common.flow.node;

import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-19
 * @Description: 基础指标计算组件
 * @Version: 1.0
 */
@Component("BaseIndicatorCalNode")
@Slf4j
public class BaseIndicatorCalculateComponent extends NodeComponent  {

    @Resource
    EnergyCalService energyCalService;
    @Resource
    HikariDataSource hikariDataSource;
    @Resource
    FlowViewServiceImpl flowViewService;
    @Resource
    DataSourceService dataSourceService;
    @Override
    public void process() {
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        //查询规则resultContext
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = calculateContext.getTaskId();
        String resultTable = calculateContext.getResultTable();
        log.info("基础指标计算组件开始执行，tag:{}",nodeId);
        //获取上下文中的项目计算类型属性列表
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = calculateContext.getResultMap();
        Map<String, Map<String, List<String>>> projectCalTypeMap = calculateContext.getProjectCalTypeMap();
        //从projectCalTypeMap中拿出所有基础计算指标
        Set<String> baseIndicators = new HashSet<>();
        projectCalTypeMap.entrySet().forEach(entry -> {
            String projectCode = entry.getKey();
            Map<String, List<String>> calTypePropMap = entry.getValue();
            //获取数据源配置信息
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (!ObjectUtils.isEmpty(dataSourceVo)) {
                TimeQueryDTO queryDataByIntervalTimeDTO = new TimeQueryDTO();
                queryDataByIntervalTimeDTO.setStartTime(calculateContext.getStartTime());
                queryDataByIntervalTimeDTO.setEndTime(calculateContext.getEndTime());
                queryDataByIntervalTimeDTO.setInterval(calculateContext.getInterval());
                queryDataByIntervalTimeDTO.setTsUnit(calculateContext.getTsUnit());
                //计算基础指标
                List<String> baseIndicatorsByProject = calTypePropMap.getOrDefault(ConstantBase.BASE_PROP, null);
                if(!ObjectUtils.isEmpty(baseIndicators)){
                    queryDataByIntervalTimeDTO.setDataCodes(baseIndicatorsByProject);
                    //生成timeGroups
                    List<String[]>  timeGroups = EnergyCalService.generateTimeGroups(calculateContext.getStartTime(),
                            calculateContext.getEndTime(),
                            calculateContext.getInterval(),
                            calculateContext.getTsUnit());
                    energyCalService.processBaseIndicators(resultMap,calTypePropMap,queryDataByIntervalTimeDTO, dataSourceVo, timeGroups,ConstantBase.EQUALLY_INTERVAL_CAL);
                    baseIndicators.addAll(calTypePropMap.getOrDefault(ConstantBase.ORI_BASE_PROP, new ArrayList<>()));
                }
            }
        });

        //将resultMap放入开始组件创建的临时表中
        updateResultMapToTempTable(resultTable,resultMap,baseIndicators);
        flowViewService.insertFlowTable(Integer.parseInt(flowId), resultTable, nodeId, taskId);
        calculateContext.setResultMap(resultMap);
        log.info("基础指标计算组件执行完毕");
    }

    public void updateResultMapToTempTable(String resultTable, ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,Set<String> indicatorList) {
        Map<String, Map<String, BigDecimal>> tsPropValueMap = EnergyCalService.transformMap(resultMap);
        List<String> sqlList = generateInsertSqls(resultTable, tsPropValueMap, indicatorList);
        Connection connection = null;
        try {
            connection = hikariDataSource.getConnection();
            DBTools.executeBatchSql(connection, sqlList);
        }catch (Exception e){
            throw new BizException("更新计算临时表失败");
        }finally {
            DBConnection.close(connection);
        }

    }
    public List<String> generateInsertSqls(String resultTable,Map<String, Map<String, BigDecimal>> tsPropValueMap,Set<String> indicatorList) {
        List<String> sqlList = new ArrayList<>();

        String columns = "ts, " + String.join(", ", indicatorList);
        for (Map.Entry<String, Map<String, BigDecimal>> entry : tsPropValueMap.entrySet()) {
            String ts = entry.getKey();
            Map<String, BigDecimal> propertyValues = entry.getValue();

            StringBuilder values = new StringBuilder();
            values.append("'");
            values.append(ts);
            values.append("', ");

            StringBuilder updates = new StringBuilder();
            for (String code : indicatorList) {
                BigDecimal value = propertyValues.get(code);
                if (value != null) {
                    values.append(value);
                    updates.append(code).append("=VALUES(").append(code).append("), ");
                } else {
                    values.append("NULL");
                    updates.append(code).append("=NULL, ");
                }
                values.append(", ");
            }

            // 移除最后一个逗号和空格
            values.setLength(values.length() - 2);
            // 移除最后一个逗号和空格
            updates.setLength(updates.length() - 2);

            String sql = "INSERT INTO " + resultTable + " (" + columns + ") VALUES (" + values.toString() + ") " +
                                           "ON DUPLICATE KEY UPDATE " + updates.toString() + ";";
            log.info("生成SQL: {}", sql);
            sqlList.add(sql);
        }

        return sqlList;
    }




}
