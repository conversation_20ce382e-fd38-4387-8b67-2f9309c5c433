package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-09
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("计算组件dto")
public class CalculationDto {

    @ApiModelProperty(value = "计算组件名称", example = "计算组件", position = 1)
    private String name;
    @ApiModelProperty(value = "计算公式id", example = "1+2", position = 2)
    private List<String> calFormula;

    @ApiModelProperty(value = "开始时间", example = "2024-07-09 10:00:00", position = 3)
    private String startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-07-09 10:00:00", position = 4)
    private String endTime;

    @ApiModelProperty(value = "步长", example = "秒", position = 5)
    private String ts;

    @ApiModelProperty(value = "时间单位", example = "秒", position = 6)
    private String tsUnit;
}
