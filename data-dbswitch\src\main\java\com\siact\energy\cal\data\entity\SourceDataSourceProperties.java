// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.data.entity;

import com.siact.energy.cal.common.entity.PatternMapper;
import com.siact.energy.cal.common.type.DBTableType;
import lombok.Data;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Data
public class SourceDataSourceProperties {

  private String url;
  private String driverClassName;
  private String username;
  private String password;
  private Long connectionTimeout = TimeUnit.SECONDS.toMillis(60);
  private Long maxLifeTime = TimeUnit.MINUTES.toMillis(60);

  private Integer fetchSize = 5000;
  private DBTableType tableType;
  private String sourceSchema = "";
  private Integer includeOrExclude;
  private String sourceIncludes = "";
  private String sourceExcludes = "";
  private List<PatternMapper> regexTableMapper;
  private List<PatternMapper> regexColumnMapper;
}
