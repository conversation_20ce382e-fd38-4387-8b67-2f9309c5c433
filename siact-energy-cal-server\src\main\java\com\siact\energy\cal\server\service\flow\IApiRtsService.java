package com.siact.energy.cal.server.service.flow;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;

import java.util.concurrent.ExecutionException;

public interface IApiRtsService extends IService<ApiConfigEntity> {
    /**
     * 执行接口绑定的组件流
     * @param url 对外提供的接口url
     * @param body 请求体
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    R<JSONArray> apiPost(String url, String body) throws ExecutionException, InterruptedException;
}
