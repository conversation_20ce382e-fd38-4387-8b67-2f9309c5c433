package com.siact.energy.cal.tool.controller.ruleCol;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.domain.ResponseCodeConstant;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleCol.RuleColUpdateDTO;
import com.siact.energy.cal.common.pojo.enums.ActiveStateEnum;
import com.siact.energy.cal.common.pojo.validator.EnumValidator;
import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.ruleCol.RuleColVO;
import com.siact.energy.cal.tool.service.ruleCol.RuleColService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 指标集表(RuleCol)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:12
 */
@Api(tags = {"指标集表"})
@ApiSort(40)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/rule/col")
public class RuleColController {

    private final RuleColService ruleColService;

    /**
     * 是否启用列表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "是否启用列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 9)
    @GetMapping("/activeState/list")
    public R<List<SelectOptionVO>> listActiveState() {
        return R.OK(ActiveStateEnum.list());
    }

    /**
     * 分页列表
     *
     * @param page            分页对象
     * @param ruleColQueryDTO 查询实体
     * @return 查询结果
     */
    @ApiOperation(value = "分页列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 10, ignoreParameters = {"total", "pages", "records", "orders", "id"})
    @GetMapping("/listPage")
    public R<PageBean<RuleColVO>> listPage(PageBean<RuleColVO> page, RuleColQueryDTO ruleColQueryDTO) {
        return R.OK(ruleColService.listPage(page, ruleColQueryDTO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    @ApiOperation(value = "通过主键查询单条数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 20)
    @GetMapping("{id}")
    public R<RuleColVO> selectOne(@PathVariable Serializable id) {
        return R.OK(ruleColService.getVoById(id));
    }

    /**
     * 新增数据
     *
     * @param ruleColInsertDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "新增数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ruleColInsertDTO", value = "实体对象", dataType = "指标集表新增DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping
    public R<Boolean> insert(@RequestBody @Validated RuleColInsertDTO ruleColInsertDTO) {
        return R.OK(ruleColService.save(ruleColInsertDTO));
    }

    /**
     * 修改数据
     *
     * @param ruleColUpdateDTO 实体对象
     * @return 修改结果
     */
    @ApiOperation(value = "修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ruleColUpdateDTO", value = "实体对象", dataType = "指标集表更新DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PutMapping
    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) RuleColUpdateDTO ruleColUpdateDTO) {
        return R.OK(ruleColService.updateVoById(ruleColUpdateDTO));
    }

    /**
     * 删除数据
     *
     * @param ids 主键集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping
    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        return R.OK(ruleColService.removeByIds(ids));
    }

    /**
     * 启用/未启用
     *
     * @param activeState 启用/未启用
     * @param ids  主键集合
     * @return 默认成功返回
     */
    @ApiOperation(value = "启用/未启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "activeState", value = ActiveStateEnum.TIPS, dataType = "String", required = true),
            @ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @PutMapping("/toggle/{activeState}")
    public R<Boolean> toggle(
            @PathVariable("activeState") @EnumValidator(enumClass = ActiveStateEnum.class, existGetMethod = "getValue", message = ActiveStateEnum.TIPS) Integer activeState,
            @RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {
        return R.OK(ruleColService.toggle(activeState, ids));
    }

    /**
     * 列表(全部)
     *
     * @return 查询结果
     */
    @ApiOperation(value = "列表(全部)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @GetMapping("/listAll")
    public R<List<RuleColVO>> listAll() {
        return R.OK(ruleColService.listAll());
    }

}

