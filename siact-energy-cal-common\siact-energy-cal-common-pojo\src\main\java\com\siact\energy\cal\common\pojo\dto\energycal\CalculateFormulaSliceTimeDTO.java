package com.siact.energy.cal.common.pojo.dto.energycal;/**
 * @Package com.siact.energy.cal.common.pojo.dto.energycal
 * @description:
 * <AUTHOR>
 * @create 2024/10/8 10:15
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CalculateFormulaDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/8 10:15
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "实时计算公式时间断面DTO")
public class CalculateFormulaSliceTimeDTO {
    @ApiModelProperty(value = "列表",position = 1,required = true)
    private List<CalFormula> calFormulaList;
    @ApiModelProperty(value = "时间;如果为空,默认最新一包数据",position = 2,required = false)
    private String ts;

}
