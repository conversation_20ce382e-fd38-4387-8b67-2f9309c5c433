package com.siact.energy.cal.common.datasource.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.siact.energy.cal.common.datasource.util.DatabaseCompatibilityUtil;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatisPlusConfig
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:31
 */
@Configuration
public class MyBatisPlusConfig {

    @Autowired
    private DatabaseCompatibilityUtil databaseCompatibilityUtil;

    @Autowired
    private DatabaseIdProvider databaseIdProvider;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {

        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();

        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        paginationInnerInterceptor.setOverflow(true);
        paginationInnerInterceptor.setMaxLimit(Long.MAX_VALUE);

        // 动态检测数据库类型
        DbType dbType = convertToMybatisPlusDbType(databaseCompatibilityUtil.detectDatabaseType());
        paginationInnerInterceptor.setDbType(dbType);
        mybatisPlusInterceptor.addInnerInterceptor(paginationInnerInterceptor);

        return mybatisPlusInterceptor;
    }



    /**
     * 将自定义数据库类型转换为MyBatis Plus的DbType
     */
    private DbType convertToMybatisPlusDbType(DatabaseCompatibilityUtil.DatabaseType databaseType) {
        switch (databaseType) {
            case MYSQL:
                return DbType.MYSQL;
            case POSTGRESQL:
                return DbType.POSTGRE_SQL;
            case ORACLE:
                return DbType.ORACLE;
            case SQLSERVER:
                return DbType.SQL_SERVER;
            case KINGBASE:
                return DbType.KINGBASE_ES;
            case DM:
                return DbType.DM;
            default:
                return DbType.MYSQL; // 默认使用MySQL
        }
    }

}
