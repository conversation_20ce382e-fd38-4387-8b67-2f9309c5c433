package com.siact.energy.cal.tool.entity.ruleCol;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import lombok.Data;
import com.siact.energy.cal.common.datasource.common.BaseEntity;

import java.util.List;

/**
 * 指标集表(RuleCol)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-20 11:23:13
 */
@Data
@TableName(value = "rule_col", autoResultMap = true)
public class RuleCol extends BaseEntity {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 指标集名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String ruleColName;

    /**
     * 指标集描述
     */
    @TableField(condition = SqlCondition.LIKE)
    private String ruleColDes;

    /**
     * 指标集构建
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<AttrObject> attrList;
    /**
     * 是否激活（0-激活,1-未激活）
     */
    private Integer activeState;


}

