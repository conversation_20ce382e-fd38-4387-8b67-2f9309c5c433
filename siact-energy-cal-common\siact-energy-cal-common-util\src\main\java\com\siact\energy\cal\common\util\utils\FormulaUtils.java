package com.siact.energy.cal.common.util.utils;

import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyKey;
import com.siact.energy.cal.common.pojo.dto.energycal.FormulaClass;
import lombok.extern.slf4j.Slf4j;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.ParseException;

import java.math.BigDecimal;
import java.util.*;

/**
 * @ClassName FormulaUtils
 * @Description
 * <AUTHOR>
 * @Date 2024/7/13 14:09
 * @Version 1.0
 **/
@Slf4j
public class FormulaUtils {

    /*
     * <AUTHOR>
     * @Description //将字符串数组转化为计算公式
     * @Date 14:10 2024/7/13
     * @Param
     * @return
     **/

    public static String convert(String formulaStr) {
        // 去掉头尾的中括号，并拆分成数组
        String[] formulaArr = parseStringToArray(formulaStr);

        // 生成表达式
        StringBuilder expression = new StringBuilder();
        for (String formulaVal : formulaArr) {
            expression.append(processFormulaVal(formulaVal));
        }

        // 获取生成的表达式
        String result = expression.toString();

        // 去除结果中可能出现的外层引号
        if (result.startsWith("\"") && result.endsWith("\"")) {
            result = result.substring(1, result.length() - 1);
        }

        return result;
    }

/*
 * <AUTHOR>
 * @Description // 提取公式中的变量
 * @Date 14:21 2024/7/13
 * @Param
 * @return
 **/

    public static List<String> getVarList(String expression) {
        List<String> dependencies = new ArrayList<>();
        if(StrUtil.isNotBlank(expression)){
            while (expression.indexOf("@[") >= 0) {
                dependencies.add(expression.substring(expression.indexOf("@[") + 2, expression.indexOf("]")));
                expression = expression.substring(expression.indexOf("@[") + 2);
                expression = expression.substring(expression.indexOf("]") + 1);
            }
        }
        return dependencies;
    }
    private static String[] parseStringToArray(String input) {
        // 去掉头尾的中括号和引号，并拆分成数组
        input = input.substring(1, input.length() - 1); // 去掉头尾的中括号
        List<String> tokens = new ArrayList<>();
        StringBuilder token = new StringBuilder();
        boolean inQuote = false;
        char[] chars = input.toCharArray();
        for (char ch : input.toCharArray()) {
            if (ch == '"') {
                inQuote = !inQuote;
                if (!inQuote && token.length() > 0) {
                    tokens.add(token.toString());
                    token = new StringBuilder();
                }
            } else if (inQuote) {
                token.append(ch);
            }
        }

        return tokens.toArray(new String[0]);
    }

    private static String processFormulaVal(String token) {
        if (token.startsWith("#[")) {
            return token.substring(2, token.length() - 1);
        } else if (token.startsWith("@[")) {
            return token;
        } else {
            return token;
        }
    }

    public static List<String> calculateOrder(List<FormulaClass> formulaList) {
        Map<String, List<String>> dependencyGraph = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();
        List<String> calculationOrder = new ArrayList<>();
        List<String> selfReferencingFormulas = new ArrayList<>();
        // 构建依赖图和初始化入度表
        for (FormulaClass formula : formulaList) {
            String field = formula.getField();
            String expression = formula.getExpression();
            List<String> dependencies = FormulaUtils.getVarList(expression);

            // 检查自引用并记录
            if (dependencies.contains(field)) {
                selfReferencingFormulas.add(field);
                continue; // 不处理其依赖关系
            }
            dependencyGraph.putIfAbsent(field, new ArrayList<>());
            inDegree.putIfAbsent(field, 0);

            for (String dependency : dependencies) {
                dependencyGraph.computeIfAbsent(dependency, k -> new ArrayList<>()).add(field);
                inDegree.putIfAbsent(dependency, 0); // 确保依赖变量也在入度表中
//                if(inDegree.keySet().contains(dependency)){
                inDegree.put(field, inDegree.getOrDefault(field, 0) + 1);
//                }

            }
        }
        // 将自引用公式放在最前面
        Queue<String> queue = new LinkedList<>(selfReferencingFormulas);
        for (String field : selfReferencingFormulas) {
            inDegree.remove(field); // 移除自引用公式的入度记录
        }
        // 拓扑排序

        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.add(entry.getKey());
            }
        }

        while (!queue.isEmpty()) {
            String field = queue.poll();
            calculationOrder.add(field);
            for (String dependentField : dependencyGraph.getOrDefault(field, Collections.emptyList())) {
                inDegree.put(dependentField, inDegree.get(dependentField) - 1);
                if (inDegree.get(dependentField) == 0) {
                    queue.add(dependentField);
                }
            }
        }

        // 检查是否存在循环依赖
        if (calculationOrder.size() != (inDegree.size() + selfReferencingFormulas.size())) {
            throw new RuntimeException("Cycle detected in formulas");
        }

        return calculationOrder;
    }


    public static Map<String, Map<DevPropertyKey, String>> calculateFormula(String formula, Map<String, BigDecimal> calValueMap, String projectCode, String devcode, String targetProp, Map<String, Map<DevPropertyKey, String>> propValueMap) {
        try {
            BigDecimal calc = JepUtils.calc(formula, calValueMap, 2, true);
            CommonUtils.putDataToMap(propValueMap, projectCode, devcode, targetProp, calc.toString());
        } catch (ParseException e) {
            log.error("公式计算错误:"+e.getMessage());
            throw new RuntimeException(e);
        } catch (EvaluationException e) {
            log.error("公式计算错误:"+e.getMessage());
            throw new RuntimeException(e);
        }
        return propValueMap;
    }

    public static BigDecimal calcFormula(String formula, Map<String, BigDecimal> calValueMap) {
        BigDecimal calc = null;
        try {
            calc = JepUtils.calc(formula, calValueMap, 6, true);
        } catch (ParseException e) {
            log.error("公式计算错误:formula:{},{}",formula,e.getMessage());
            throw new RuntimeException(e);
        } catch (EvaluationException e) {
            log.error("公式计算错误:formula:{},{}",formula,e.getMessage());
            throw new RuntimeException(e);
        }
        return calc;
    }
}
