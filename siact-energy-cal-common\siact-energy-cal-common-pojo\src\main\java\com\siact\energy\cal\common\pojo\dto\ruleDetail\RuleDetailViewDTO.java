package com.siact.energy.cal.common.pojo.dto.ruleDetail;/**
 * @Package com.siact.energy.cal.common.pojo.dto.ruleDetail
 * @description: 计算公式查看DTO
 * <AUTHOR>
 * @create 2024/12/16 9:53
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName RuleDetailViewDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/16 9:53
 * @Version 1.0
 **/
@ApiModel("查看公式DTO")
@Data
public class RuleDetailViewDTO {

    /**
     * 指标类型（0-实例级规则，1-模型级规则）
     */
    @ApiModelProperty(value = "指标类型（0-实例级规则，1-模型级规则）", position = 1)
    private Integer ruleType;

    /**
     * 计算类型（0-数值计算，1-逻辑计算，2-正则表达式）
     */
    @ApiModelProperty(value = "计算类型（0-数值计算，1-逻辑计算，2-正则表达式）", position = 2)
    private Integer calType;

    /**
     * 节点（模型）名称
     */
    @ApiModelProperty(value = "节点（模型）名称", position = 3)
    private String devName;

    /**
     * 节点（模型）编码
     */
    @ApiModelProperty(value = "节点（模型）名称", position = 4)
    private String devNameCode;

    /**
     * 属性（模型）名称
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 5)
    private String propName;

    /**
     * 属性（模型）编码
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 6)
    private String propNameCode;

    /**
     * 属性（模型）编码
     */
    @ApiModelProperty(value = "计算公式", position = 7)
    private String formulaShow;
    /**
     * 指标集ID
     */
    @ApiModelProperty(value = "指标集名称", position = 8)
    private String ruleColId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID", position = 9)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
}
