package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.List;

/**
 * ActiveStateEnum
 *
 * <AUTHOR>
 * @since 2024-05-20 16:12:49
 */
public enum ActiveStateEnum {

    /**
     * 启用
     */
    ENABLE(0, "启用"),

    /**
     * 未启用
     */
    DISABLE(1, "未启用");

    public static final String TIPS = "启用/未启用，0:启用,1:未启用";

    private final Integer value;

    private final String description;

    ActiveStateEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(ActiveStateEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDescription()).value(enumObj.getValue()).build());
        }

        return list;
    }

}
