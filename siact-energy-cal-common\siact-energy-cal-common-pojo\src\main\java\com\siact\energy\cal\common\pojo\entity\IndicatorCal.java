package com.siact.energy.cal.common.pojo.entity;/**
 * @Package com.siact.energy.cal.common.pojo.entity
 * @description: 指标筛选实体类
 * <AUTHOR>
 * @create 2024/9/3 14:16
 */

import com.siact.energy.cal.common.pojo.enums.IndicatorFuncEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName IndicatorCal
 * @Description
 * <AUTHOR>
 * @Date 2024/9/3 14:16
 * @Version 1.0
 **/
@Data
@ApiModel("指标计算")
public class IndicatorCal {
    /**
     * 计算表达式
     */
    @ApiModelProperty(value = "计算表达式", position = 1, required = false, example = "a/b")
    private String calFormula;
    /**
     * 别名
     */
    @ApiModelProperty(value = "别名", position = 2, required = false, example = "别名")
    private String aliasNew;
}
