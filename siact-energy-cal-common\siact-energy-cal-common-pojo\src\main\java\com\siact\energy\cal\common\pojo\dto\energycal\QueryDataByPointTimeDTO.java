package com.siact.energy.cal.common.pojo.dto.energycal;/**
 * @Package com.siact.energycal.dto
 * @description:
 * <AUTHOR>
 * @create 2024/8/8 21:53
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName QueryDataByPointTimeDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/8 21:53
 * @Version 1.0
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "请求时间截面数据接口")
public class QueryDataByPointTimeDTO {
    @ApiModelProperty(value = "属性编码列表",position = 1,required = true)
    public List<String> dataCodes;

    @ApiModelProperty(value = "时间,示例值:2024-08-08 10:00:00,如果不传返回最新值",position = 2,required = false)
    public String ts;

}
