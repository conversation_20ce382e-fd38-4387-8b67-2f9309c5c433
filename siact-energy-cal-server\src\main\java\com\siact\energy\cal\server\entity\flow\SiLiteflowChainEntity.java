package com.siact.energy.cal.server.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("si_liteflow_chain")
public class SiLiteflowChainEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String applicationName;

    private String chainName;

    private String chainDesc;

    private String elData;

    private String route;

    private String namespace;

    private LocalDateTime createTime;

    private Boolean enable;


}
