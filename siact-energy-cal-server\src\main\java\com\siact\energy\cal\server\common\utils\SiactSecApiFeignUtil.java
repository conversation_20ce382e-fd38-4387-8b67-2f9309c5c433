package com.siact.energy.cal.server.common.utils;

import com.siact.api.common.api.vo.common.R;
import com.siact.common.core.vo.PageVo;

import java.util.List;
import java.util.Objects;

/**
 * SiactSecApiFeignUtil
 *
 * <AUTHOR>
 * @since 2024-05-14 16:09:36
 */
public class SiactSecApiFeignUtil<T> {

    /**
     * 从R中获取对象
     *
     * @param r R
     * @return 对象
     */
    public static <T> T obj(R<T> r) {
        checkRStatus(r);
        return r.getData();
    }

    /**
     * 从R中获取对象集合
     *
     * @param r R
     * @return 对象集合
     */
    public static <T> List<T> list(R<List<T>> r) {
        checkRStatus(r);
        return r.getData();
    }

    /**
     * 从R中获取分页对象
     *
     * @param r R
     * @return 分页对象
     */
    public static <T> PageVo<T> page(R<PageVo<T>> r) {
        checkRStatus(r);
        return r.getData();
    }

    /**
     * 检查R状态
     *
     * @param r R
     */
    private static void checkRStatus(R r) {
        if(!Objects.equals(R.OK().getCode(), r.getCode())) {
            throw new RuntimeException(String.format("获取数字孪生结果异常：code: %s, msg: %s", r.getCode(), r.getMsg()));
        }
    }

}