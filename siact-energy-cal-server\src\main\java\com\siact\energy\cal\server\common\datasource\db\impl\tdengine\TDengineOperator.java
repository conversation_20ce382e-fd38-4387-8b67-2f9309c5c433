package com.siact.energy.cal.server.common.datasource.db.impl.tdengine;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.enums.DBATypeEnum;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DateUtils;
import com.siact.energy.cal.server.common.datasource.db.core.AbstractDbOperator;
import com.siact.energy.cal.server.entity.energycal.DiffCal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service("tdengineOperator")
@Slf4j
public class TDengineOperator extends AbstractDbOperator {
    @Autowired
    public TDengineOperator() {
    }

    @Override
    public Connection getConnection(DataSourceVo dataSourceVo) {
        try {
            Integer dbType = dataSourceVo.getDbType();
            DBATypeEnum dbTypeEnum = DBATypeEnum.getByIndex(dbType);
            Class.forName(dbTypeEnum.TDENGINE.getDriveClassName());
            String url = DBATypeEnum.TDENGINE.getUrl()
                    .replace("{host}", dataSourceVo.getDatabaseIp())
                    .replace("{port}", dataSourceVo.getDatabasePort())
                    .replace("{database}", dataSourceVo.getDb());

            return DriverManager.getConnection(
                    url,
                    dataSourceVo.getUserName(),
                    dataSourceVo.getPassword()
            );
        } catch (Exception e) {
            log.error("TDengine connection failed: {}", e.getMessage(), e);
            throw new BizException("连接失败");
        }
    }

    @Override
    public void executeBasicSql(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo) {
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                // 获取时间戳并格式化
                String ts = DateUtils.parse(rs.getString("ts"));
                String property = rs.getString("devproperty");
                String itemvalue = rs.getString("itemvalue");
                if(StrUtil.isNotBlank(itemvalue)) {
                    // 将itemvalue转换为Double类型
                    BigDecimal value = null;
                    try {

                        value = new BigDecimal(itemvalue);

                    } catch (NumberFormatException e) {
                        log.error("转换itemvalue异常", e.getMessage());
//                    throw new RuntimeException(e);
                    }
                    resultMap.computeIfAbsent(property, k -> new ConcurrentHashMap<>())
                            .put(ts, value);
                }
            }
        } catch (SQLException e) {
            log.error("TDengine 查询SQL执行失败: {}", e.getMessage(), e);
            throw new BizException("TDengine 查询SQL执行失败");
        }
    }

    @Override
    public void executeAggQuery(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String sql,
            DataSourceVo dataSourceVo,
            Map<String, String> propMapping) {
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                String ts = DateUtils.parse(rs.getString("ts"));
                String devproperty = rs.getString("devproperty");
                String itemvalue = rs.getString("itemvalue");
                String targetProp = propMapping.getOrDefault(devproperty, null);

                if (StrUtil.isNotBlank(itemvalue) && StrUtil.isNotBlank(targetProp)) {
                    BigDecimal value = new BigDecimal(itemvalue);
                    resultMap.computeIfAbsent(targetProp, k -> new ConcurrentHashMap<>())
                            .put(ts, value);
                }
            }
        } catch (Exception e) {
            log.error("数值转换异常: {}", e.getMessage());
//            throw new BizException("数值转换异常", e);
        }
    }

    @Override
    public void executeDiffQuery(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String sql, DataSourceVo dataSourceVo, Map<String, String> propMapping) {
        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            List<DiffCal> list = new ArrayList<>();
            while (rs.next()) {
                String ts = DateUtils.parse(rs.getString("ts"));
                String itemvalue = rs.getString("itemvalue");
                String devProp = rs.getString("devproperty");
                String targetProp = propMapping.getOrDefault(devProp, null);
                if (StrUtil.isNotBlank(targetProp)) {
                    DiffCal diffCal = new DiffCal();
                    diffCal.setTs(ts);
                    diffCal.setTargetProp(targetProp);
                    diffCal.setItemvalue(itemvalue);
                    list.add(diffCal);
                }
            }
            //排序
            list.sort(Comparator.comparing(DiffCal::getTs));
            //分组并计算差值
            calculateDiffByDevproperty(resultMap, list);

        } catch (SQLException e) {
            log.error("Execute query error: {}", e.getMessage(), e);
            throw new BizException("Query execution failed");
        }
    }

    @Override
    public void insertData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> dataCodes, DataSourceVo dataSourceVo) {

        try (Connection conn = getConnection(dataSourceVo);
             Statement stmt = conn.createStatement()) {
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append(" insert into ");
            for (String devprop : dataCodes) {
                Map<String, BigDecimal> tsValueMap = resultMap.getOrDefault(devprop, null);
                if (!ObjectUtils.isEmpty(tsValueMap)) {
                    for (Map.Entry<String, BigDecimal> entryValue : tsValueMap.entrySet()) {
                        try {
                            String ts = entryValue.getKey();
                            BigDecimal itemvalue = entryValue.getValue();
                            sqlBuffer.append(devprop)
                                    .append(" using " + dataSourceVo.getTableName() + " tags ('" + null + "','" + null + "','" + devprop + "','" + null + "','" + null + "','" + null + "') (ts,itemvalue) values ('" + ts + "'," + itemvalue + ") ");
                            if (sqlBuffer.length() > 63 * 1024) {
                                String sql = sqlBuffer.toString();
                                if (sql.contains("using")) {
                                    log.debug(sql);
                                    stmt.addBatch(sql);
                                }
                                sqlBuffer = new StringBuffer();
                                sqlBuffer.append("insert into ");
                            }
                        } catch (SQLException e) {
                            log.error("数据入库时拼接sql异常" + e.getMessage());
                            throw new BizException("数据入库时拼接sql异常");
                        }
                    }
                    try {
                        String sql = sqlBuffer.toString();

                        if (sql.contains("using")) {
                            log.debug(sql);
                            stmt.addBatch(sql);
                        }
                        int[] ints = stmt.executeBatch();

                        if (ints.length != 0) {
                            log.info(dataSourceVo.getProjectCode() + "===项目数据批量插入成功");
                        }
                    } catch (SQLException e) {
                        log.error("数据批量插入失败" + "========" + e.getMessage());
                        throw new BizException("数据批量插入失败");

                    } finally {
                        DBConnection.close(conn);
                    }

                }
            }

        } catch (SQLException e) {
            log.error("Execute query error: {}", e.getMessage(), e);
            throw new BizException("Query execution failed");
        }
    }


    @Override
    public boolean testConnection(DataSourceVo testDTO) {
        try (Connection conn = getConnection(testDTO);
             Statement stmt = conn.createStatement()) {
            // TDengine 特有的服务器状态查询
            ResultSet rs = stmt.executeQuery("SHOW DATABASES");
            return rs != null && rs.next();
        } catch (SQLException e) {
            log.error("TDengine 连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }
    private void calculateDiffByDevproperty(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<DiffCal> list) {
        Map<String, List<DiffCal>> groupedData = list.stream()
                .collect(Collectors.groupingBy(DiffCal::getTargetProp));

        for (Map.Entry<String, List<DiffCal>> entry : groupedData.entrySet()) {
            List<DiffCal> dataPoints = entry.getValue();
            for (int i = 0; i < dataPoints.size() - 1; i++) {
                DiffCal current = dataPoints.get(i);
                DiffCal next = dataPoints.get(i + 1);
                BigDecimal diff = null;
                if (StrUtil.isNotBlank(current.getItemvalue()) && StrUtil.isNotBlank(next.getItemvalue())) {
                    BigDecimal nextValue = new BigDecimal(next.getItemvalue());
                    BigDecimal currValue = new BigDecimal(current.getItemvalue());

                    diff = nextValue.subtract(currValue);
                }

                if (ObjectUtil.isNotEmpty(diff)) {
                    resultMap.computeIfAbsent(current.getTargetProp(), k -> new ConcurrentHashMap<>())
                            .put(current.getTs(), diff);
                }

            }

        }
    }
}