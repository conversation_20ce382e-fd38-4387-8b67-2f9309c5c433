package com.siact.energy.cal.tool.service;

import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.tool.entity.dataTrans.DataTrans;
import com.siact.energy.cal.tool.service.dataTrans.DataTransService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 自动填充功能测试
 * 验证creator和updater字段是否能够自动填充
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class AutoFillTest {

    @Autowired
    private DataTransService dataTransService;

    @Autowired
    private BaseService baseService;

    @Test
    public void testAutoFillCreatorAndUpdater() {
        // 创建测试数据
        DataTransInsertDTO dto = new DataTransInsertDTO();
        dto.setDataTransName("测试数据传输");
        dto.setDataTransDes("测试自动填充功能");
        dto.setMqttTopic("test/topic");
        dto.setMqttClientId("test_client");
        dto.setMqttHost("localhost");
        dto.setMqttPort(1883);

        // 保存数据
        Boolean result = dataTransService.save(dto);
        
        log.info("保存结果: {}", result);
        log.info("当前登录用户ID: {}", baseService.getLoginUserId());
        
        // 验证自动填充是否生效
        // 注意：这里只是演示测试结构，实际测试需要模拟登录用户
        assert result != null && result;
    }
}
