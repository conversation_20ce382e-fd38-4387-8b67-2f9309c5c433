package com.siact.energy.cal.common.pojo.dto.dataSource;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;


/**
 * DataSourceTestDTO
 *
 * <AUTHOR>
 * @since 2024-05-15 10:28:41
 */
@ApiModel("数据源测试DTO")
@Data
public class DataSourceTestDTO {

    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称", position = 1)
    private String databaseName;

    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", required = true, position = 2)
    @NotBlank(message = "数据源类型不能为空")
    private Integer dbType;

    /**
     * 数据源ip
     */
    @ApiModelProperty(value = "数据源ip", position = 2)
    private String databaseIp;

    /**
     * 数据源端口
     */
    @ApiModelProperty(value = "数据源端口", position = 3)
    private String databasePort;

    /**
     * 数据库名称
     */
    @ApiModelProperty(value = "数据库名称", position = 4)
    private String db;

    /**
     * 超级表名
     */
    @ApiModelProperty(value = "超级表名", position = 5)
    private String tableName;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 6)
    @NotBlank(message = "用户名不能为空")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 7)
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 数据库连接串
     */
    @ApiModelProperty(value = "数据库连接串", position = 8)
    private String jdbcUrl;

    /**
     * 项目id,如果是数仓数据库，id为空
     */
    @ApiModelProperty(value = "项目id，编辑的时候不能更换项目ID", position = 9)
    private Long projectId;

}

