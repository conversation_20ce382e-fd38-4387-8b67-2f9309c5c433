package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.common.datasource.enums
 * @description:
 * @create 2024/9/7 15:49
 */
public enum DBATypeEnum {


    /**
     * [国产]TDENGINE时序数据库
     */
    TDENGINE(0, "TDENGINE","com.taosdata.jdbc.rs.RestfulDriver", "select 1", "jdbc:TAOS-RS://{host}:{port}/{database}?charset=utf-8&timestampFormat=UTC"),

    /**
     * InfluxDB数据库
     */
    INFLUXDB(1, "InfluxDB","", "/* ping */ SELECT 1", "http://{host}:{port}"),;

    private Integer index;
    private String desc;
    private String driveClassName;
    private String testSql;
    private String url;

    public String getTestSql() {
        return testSql;
    }

    public String getUrl() {
        return url;
    }

    public String getDriveClassName() {
        return driveClassName;
    }

    DBATypeEnum(Integer idx, String desc, String driveClassName, String testSql, String url) {
        this.index = idx;
        this.desc = desc;
        this.driveClassName = driveClassName;
        this.testSql = testSql;
        this.url = url;
    }

    public Integer getIndex() {
        return index;
    }
    public String getDesc() {
        return desc;
    }


    public static DBATypeEnum getByIndex(Integer index) {
        return Arrays.stream(DBATypeEnum.values()).filter(productTypeEnum -> productTypeEnum.getIndex().equals(index)).findFirst().orElse(DBATypeEnum.TDENGINE);
    }
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(DBATypeEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDesc()).value(enumObj.getIndex()).build());
        }

        return list;
    }
}
