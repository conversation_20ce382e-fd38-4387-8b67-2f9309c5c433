package com.siact.energy.cal.common.pojo.dto.dataSource;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-19
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiOperation("属性断面数据批量查询入参")
public class PointMsgDTO {

    @ApiModelProperty(value = "节点编码", position = 1)
    private List<String> dataCodes;
    @ApiModelProperty(value = "步长", position = 2, example = "2024-06-18 00:00:00")
    private String ts;
}
