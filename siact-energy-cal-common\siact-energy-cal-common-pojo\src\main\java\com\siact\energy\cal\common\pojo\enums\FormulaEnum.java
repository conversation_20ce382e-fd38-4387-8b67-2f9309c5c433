package com.siact.energy.cal.common.pojo.enums;

import lombok.Getter;

@Getter
public enum FormulaEnum {
    MIN("MIN", "SELECT MIN(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    FIRST("FIRST", "SELECT FIRST(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    LAST("LAST", "SELECT LAST(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    SUM("LAST", "SELECT SUM(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    AVG("AVG", "SELECT SUM(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    DIFF("DIFF", "SELECT SUM(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);"),
    MAX("MAX", "SELECT MAX(itemvalue) as {} FROM {} WHERE devproperty = '{}' and ts>='{}' and ts<='{}' INTERVAL({}) FILL(PREV);");

    private final String code;
    private final String formula;
    FormulaEnum(String code, String formula){
        this.formula = formula;
        this.code = code;
    }

}
