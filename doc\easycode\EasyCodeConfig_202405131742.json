{"author": "高卫东", "version": "1.2.7", "userSecure": "", "currTypeMapperGroupName": "<PERSON><PERSON><PERSON>", "currTemplateGroupName": "siact-energy-cal", "currColumnConfigGroupName": "<PERSON><PERSON><PERSON>", "currGlobalConfigGroupName": "<PERSON><PERSON><PERSON>", "typeMapper": {"FLOAT": {"name": "FLOAT", "elementList": []}, "Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.time.LocalTime"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.Bo<PERSON>an"}, {"matchType": "ORDINARY", "columnType": "float", "javaType": "java.lang.Float"}, {"matchType": "ORDINARY", "columnType": "double", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "decimal(10)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "json", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "float(12,2)", "javaType": "java.lang.Float"}, {"matchType": "ORDINARY", "columnType": "double(12,2)", "javaType": "java.lang.Float"}, {"matchType": "ORDINARY", "columnType": "CLOB", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "TIMESTAMP(6)", "javaType": "java.util.Date"}, {"matchType": "ORDINARY", "columnType": "double(36,6)", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "float(24,2)", "javaType": "java.lang.Float"}, {"matchType": "ORDINARY", "columnType": "bigint(20) unsigned zerofill", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "tinyint(1) unsigned zerofill", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "bigint(20) unsigned", "javaType": "java.lang.Long"}]}}, "template": {"siact-energy-cal": {"name": "siact-energy-cal", "elementList": [{"name": "controller.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Controller\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/controller/\",${customPackageName}), \"Controller.java\")\n\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"controller.\", ${customPackageName}))\n##定义服务名\n#set($serviceName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), \"Service\"))\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;\nimport com.github.xiaoymin.knife4j.annotations.ApiSort;\n\nimport org.springframework.web.bind.annotation.*;\nimport io.swagger.annotations.*;\n\nimport org.springframework.validation.annotation.Validated;\n\nimport java.io.Serializable;\nimport java.util.List;\nimport javax.validation.constraints.NotEmpty;\n\nimport lombok.RequiredArgsConstructor;\n\nimport com.siact.energy.cal.common.core.domain.ResponseCodeConstant;\nimport com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;\nimport com.siact.energy.cal.common.core.domain.R;\nimport com.siact.energy.cal.common.datasource.common.PageBean;\n\nimport $!{tableInfo.savePackageName}.service.${customPackageName}.$!{tableInfo.name}Service;\nimport com.siact.energy.cal.common.pojo.vo.${customPackageName}.$!{tableInfo.name}VO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}QueryDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}InsertDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}UpdateDTO;\n\n##表注释（宏定义）\n#tableComment(\"表控制层\")\n@Api(tags = {\"$!{tableInfo.comment}\"})\n@ApiSort(1)\n@Validated\n@RequiredArgsConstructor\n@RestController\n@RequestMapping(\"/$!{tableInfo.obj.name.replace('_','/').toLowerCase()}\")\npublic class $!{tableName} {\n    \n    private final $!{tableInfo.name}Service $!{serviceName};\n\n    /**\n     * 列表\n     *\n     * @param page 分页对象\n     * @param $!{entityName}QueryDTO 查询实体\n     * @return 查询结果\n     */\n    @ApiOperation(value = \"列表\")\n    @ApiResponses({\n            @ApiResponse(code = 200, message = \"成功\")\n    })\n    @ApiOperationSupport(order = 10, ignoreParameters = {\"total\", \"pages\", \"records\", \"orders\", \"id\"})\n    @GetMapping(\"/list\")\n    public R<PageBean<$!{tableInfo.name}VO>> listPage(PageBean<$!{tableInfo.name}VO> page, $!{tableInfo.name}QueryDTO $!{entityName}QueryDTO) {\n        return R.OK($!{serviceName}.listPage(page, $!{entityName}QueryDTO));\n    }\n\n    /**\n     * 通过主键查询单条数据\n     *\n     * @param id 主键\n     * @return 返回数据\n     */\n    @ApiOperation(value = \"通过主键查询单条数据\")\n    @ApiImplicitParams({\n            @ApiImplicitParam(name = \"id\", value = \"主键\" )\n    })\n    @ApiResponses({\n            @ApiResponse(code = 200, message = \"成功\")\n    })\n    @ApiOperationSupport(order = 20)\n    @GetMapping(\"{id}\")\n    public R<$!{tableInfo.name}VO> selectOne(@PathVariable Serializable id) {\n        return R.OK($!{serviceName}.getVoById(id));\n    }\n\n    /**\n     * 新增数据\n     *\n     * @param $!{entityName}InsertDTO 实体对象\n     * @return 新增结果\n     */\n    @ApiOperation(value = \"新增数据\")\n    @ApiImplicitParams({\n            @ApiImplicitParam(name = \"$!{entityName}InsertDTO\", value = \"实体对象\", dataType = \"$!{tableInfo.comment}新增DTO\", required = true)\n    })\n    @ApiResponses({\n            @ApiResponse(code = 200, message = \"成功\")\n    })\n    @ApiOperationSupport(order = 30, ignoreParameters = {\"id\"})\n    @PostMapping\n    public R<Boolean> insert(@RequestBody @Validated $!{tableInfo.name}InsertDTO $!{entityName}InsertDTO) {\n        return R.OK($!{serviceName}.save($!{entityName}InsertDTO));\n    }\n\n    /**\n     * 修改数据\n     *\n     * @param $!{entityName}UpdateDTO 实体对象\n     * @return 修改结果\n     */\n    @ApiOperation(value = \"修改数据\")\n    @ApiImplicitParams({\n            @ApiImplicitParam(name = \"$!{entityName}UpdateDTO\", value = \"实体对象\", dataType = \"$!{tableInfo.comment}更新DTO\", required = true)\n    })\n    @ApiResponses({\n            @ApiResponse(code = 200, message = \"成功\")\n    })\n    @ApiOperationSupport(order = 40)\n    @PutMapping\n    public R<Boolean> update(@RequestBody @Validated(UpdateValidGroup.class) $!{tableInfo.name}UpdateDTO $!{entityName}UpdateDTO) {\n        return R.OK($!{serviceName}.updateVoById($!{entityName}UpdateDTO));\n    }\n\n    /**\n     * 删除数据\n     *\n     * @param ids 主键集合\n     * @return 删除结果\n     */\n    @ApiOperation(value = \"删除数据\")\n    @ApiImplicitParams({\n            @ApiImplicitParam(name = \"ids\", value = \"主键集合\", dataType = \"List<Long>\", required = true)\n    })\n    @ApiResponses({\n            @ApiResponse(code = 200, message = \"成功\")\n    })\n    @ApiOperationSupport(order = 50)\n    @DeleteMapping\n    public R<Boolean> delete(@RequestBody @NotEmpty(message = ResponseCodeConstant.RC_40000001) List<Long> ids) {\n        return R.OK($!{serviceName}.removeByIds(ids));\n    }\n\t\n}\n"}, {"name": "service.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Service\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/service/\",${customPackageName}), \"Service.java\")\n\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"service.\", ${customPackageName}))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.baomidou.mybatisplus.extension.service.IService;\nimport java.io.Serializable;\n\nimport com.siact.energy.cal.common.datasource.common.PageBean;\nimport $!{tableInfo.savePackageName}.entity.${customPackageName}.$!{tableInfo.name};\nimport com.siact.energy.cal.common.pojo.vo.${customPackageName}.$!{tableInfo.name}VO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}QueryDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}InsertDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}UpdateDTO;\n\n##表注释（宏定义）\n#tableComment(\"表服务接口\")\npublic interface $!{tableName} extends IService<$!tableInfo.name> {\n\t\n\t/**\n     * 注册列表\n     *\n     * @param page 分页对象\n     * @param $!{entityName}QueryDTO 查询实体\n     * @return 查询结果\n     */\n    PageBean<$!{tableInfo.name}VO> listPage(PageBean<$!{tableInfo.name}VO> page, $!{tableInfo.name}QueryDTO $!{entityName}QueryDTO);\n\n    /**\n     * 通过主键查询单条数据\n     *\n     * @param id 主键\n     * @return 返回数据\n     */\n    $!{tableInfo.name}VO getVoById(Serializable id);\n\n    /**\n     * 新增数据\n     *\n     * @param $!{entityName}InsertDTO 实体对象\n     * @return 新增结果\n     */\n    Boolean save($!{tableInfo.name}InsertDTO $!{entityName}InsertDTO);\n\n    /**\n     * 修改数据\n     *\n     * @param $!{entityName}UpdateDTO 实体对象\n     * @return 修改结果\n     */\n    Boolean updateVoById($!{tableInfo.name}UpdateDTO $!{entityName}UpdateDTO);\n\n}\n"}, {"name": "serviceImpl.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"ServiceImpl\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/service/\",${customPackageName}, \"/impl/\"), \"ServiceImpl.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($tool.append(\"service.\",${customPackageName}, \".impl\"))\n\n##定义实体对象名\n#set($entityName = $!tool.firstLowerCase($!tableInfo.name))\n\nimport com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;\nimport com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\n\nimport org.springframework.stereotype.Service;\n\nimport java.io.Serializable;\nimport java.util.List;\n\nimport $!{tableInfo.savePackageName}.dao.${customPackageName}.$!{tableInfo.name}Dao;\nimport $!{tableInfo.savePackageName}.entity.${customPackageName}.$!{tableInfo.name};\nimport $!{tableInfo.savePackageName}.service.${customPackageName}.$!{tableInfo.name}Service;\nimport com.siact.energy.cal.common.datasource.common.PageBean;\nimport $!{tableInfo.savePackageName}.convertor.${customPackageName}.$!{tableInfo.name}Convertor;\nimport com.siact.energy.cal.common.pojo.vo.${customPackageName}.$!{tableInfo.name}VO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}QueryDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}InsertDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}UpdateDTO;\nimport com.siact.energy.cal.common.util.utils.ClassUtil;\n\n##表注释（宏定义）\n#tableComment(\"表服务实现类\")\n@Service(\"$!tool.firstLowerCase($tableInfo.name)Service\")\npublic class $!{tableName} extends ServiceImpl<$!{tableInfo.name}Dao, $!{tableInfo.name}> implements $!{tableInfo.name}Service {\n\t\n    @Override\n    public PageBean<$!{tableInfo.name}VO> listPage(PageBean<$!{tableInfo.name}VO> page, $!{tableInfo.name}QueryDTO $!{entityName}QueryDTO) {\n        return page(page, $!{entityName}QueryDTO);\n    }\n    \n    @Override\n    public $!{tableInfo.name}VO getVoById(Serializable id) {\n        $!{tableInfo.name} $!{entityName} = getById(id);\n        return $!{tableInfo.name}Convertor.INSTANCE.entity2Vo($!{entityName});\n    }\n\n    @Override\n    public Boolean save($!{tableInfo.name}InsertDTO $!{entityName}InsertDTO) {\n        return save($!{tableInfo.name}Convertor.INSTANCE.insertDTO2Entity($!{entityName}InsertDTO));\n    }\n\n    @Override\n    public Boolean updateVoById($!{tableInfo.name}UpdateDTO $!{entityName}UpdateDTO) {\n        return updateById($!{tableInfo.name}Convertor.INSTANCE.updateDTO2Entity($!{entityName}UpdateDTO));\n    }\n\t\t\n\t/**\n     * 分页查询\n     * \n     * @param page               分页对象\n     * @param $!{entityName}QueryDTO 查询实体\n     * @return 分页数据\n     */\n\tprivate PageBean<$!{tableInfo.name}VO> page(PageBean<$!{tableInfo.name}VO> page, $!{tableInfo.name}QueryDTO $!{entityName}QueryDTO) {\n\n        // 转换器\n        $!{tableInfo.name}Convertor convertor = $!{tableInfo.name}Convertor.INSTANCE;\n        // VO转实体\n        $!{tableInfo.name} $!{entityName} = convertor.queryDTO2Entity($!{entityName}QueryDTO);\n\n        // 创建查询对象\n        LambdaQueryWrapper<$!{tableInfo.name}> queryWrapper = new LambdaQueryWrapper<>($!{entityName});\n\t\t\n        List<String> voFieldNameList = ClassUtil.getClassAllFields($!{tableInfo.name}VO.class);\n        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));\n\n        // 查询实体数据\n        Page<$!{tableInfo.name}> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);\n\n        // 实体分页转VO分页\n        PageBean<$!{tableInfo.name}VO> voPageBean = convertor.entityPage2VoPageBean(entityPage);\n\n        return voPageBean;\n    }\n\n}\n"}, {"name": "entity.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/entity/\",${customPackageName}), \".java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"entity.\", ${customPackageName}))\n\n##自动导入包（全局变量）\n$!autoImport\nimport com.baomidou.mybatisplus.annotation.IdType;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport com.baomidou.mybatisplus.annotation.SqlCondition;\nimport com.baomidou.mybatisplus.annotation.TableField;\nimport lombok.Data;\nimport com.siact.energy.cal.common.datasource.common.BaseEntity;\n#set($hasDate = false)\n#set($hasBigDecimal = false)\n#foreach($column in $tableInfo.fullColumn)\n    #if(${hasDate}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='Date' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.util.Date;\n            #set($hasDate = true)\n\t    #end\n\t#end\n\t#if(${hasBigDecimal}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='BigDecimal' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.math.BigDecimal;\n            #set($hasBigDecimal = true)\n\t    #end\n\t#end\n#end\n\n##表注释（宏定义）\n#tableComment(\"表实体类\")\n@Data\npublic class $!{tableInfo.name} extends BaseEntity {\n\n#foreach($column in $tableInfo.fullColumn)\n    #if(${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\n    #if(${column.comment})\n    /**\n     * ${column.comment}\n     */\n\t#end\n\t#if(${column.name}=='id')\n\t@TableId(value = \"id\",type = IdType.ASSIGN_ID)\n\t#end\n\t#if($!{tool.getClsNameByFullName($column.type)}=='String')\n\t@TableField(condition = SqlCondition.LIKE)\n\t#end\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n    \n    #end\n#end\n}\n"}, {"name": "convertor.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/convertor/\",${customPackageName}), \"Convertor.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"convertor.\", ${customPackageName}))\n\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport org.mapstruct.Mapper;\nimport org.mapstruct.factory.Mappers;\nimport $!{tableInfo.savePackageName}.entity.${customPackageName}.$!{tableInfo.name};\nimport com.siact.energy.cal.common.core.convertor.AbstractConvertor;\nimport com.siact.energy.cal.common.datasource.common.PageBean;\nimport com.siact.energy.cal.common.pojo.vo.${customPackageName}.$!{tableInfo.name}VO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}QueryDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}InsertDTO;\nimport com.siact.energy.cal.common.pojo.dto.${customPackageName}.$!{tableInfo.name}UpdateDTO;\n\n##表注释（宏定义）\n#tableComment(\" Convertor\")\n@Mapper\npublic interface $!{tableInfo.name}Convertor extends AbstractConvertor<$!{tableInfo.name}VO, $!{tableInfo.name}> {\n    \n    $!{tableInfo.name}Convertor INSTANCE = Mappers.getMapper($!{tableInfo.name}Convertor.class);\n\n    /**\n     * 实体分页转VO分页\n     *\n     * @param entityPage 实体分页\n     * @return VO 分页\n     */\n    PageBean<$!{tableInfo.name}VO> entityPage2VoPageBean(Page<$!{tableInfo.name}> entityPage);\n\n    /**\n     * VO分页转实体分页\n     *\n     * @param voPageBean 实体分页\n     * @return VO分页\n     */\n    Page<$!{tableInfo.name}> voPageBean2EntityPage(PageBean<$!{tableInfo.name}VO> voPageBean);\n\n    /**\n     * 查询DTO转实体\n     *\n     * @param dto DTO\n     * @return 实体\n     */\n    $!{tableInfo.name} queryDTO2Entity($!{tableInfo.name}QueryDTO dto);\n\n    /**\n     * 新增DTO转实体\n     *\n     * @param dto DTO\n     * @return 实体\n     */\n    $!{tableInfo.name} insertDTO2Entity($!{tableInfo.name}InsertDTO dto);\n\n    /**\n     * 更新DTO转实体\n     *\n     * @param dto DTO\n     * @return 实体\n     */\n    $!{tableInfo.name} updateDTO2Entity($!{tableInfo.name}UpdateDTO dto);\n    \n}"}, {"name": "dao.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"Dao\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/dao/\",${customPackageName}), \"Dao.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"dao.\", ${customPackageName}))\n\nimport java.util.List;\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.stereotype.Repository;\nimport $!{tableInfo.savePackageName}.entity.${customPackageName}.$!tableInfo.name;\n\n##表注释（宏定义）\n#tableComment(\"表数据库访问层\")\n@Repository\npublic interface $!{tableName} extends BaseMapper<$!tableInfo.name> {\n\n\t/**\n\t* 批量新增数据\n\t*\n\t* @param entities 实例对象列表\n\t* @return 影响行数\n\t*/\n\tint insertBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n\t/**\n\t* 批量新增或按主键更新数据\n\t*\n\t* @param entities 实例对象列表\n\t* @return 影响行数\n\t*/\n\tint insertOrUpdateBatch(@Param(\"entities\") List<$!{tableInfo.name}> entities);\n\n}\n"}, {"name": "mapper.xml.vm", "code": "##引入mybatis支持\n$!{mybatisSupport.vm}\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##设置保存名称与保存位置\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"Dao.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper\"))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n#end\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{tableInfo.savePackageName}.dao.${customPackageName}.$!{tableInfo.name}Dao\">\n\n    <resultMap type=\"$!{tableInfo.savePackageName}.entity.${customPackageName}.$!{tableInfo.name}\" id=\"$!{tableInfo.name}Map\">\n#foreach($column in $tableInfo.fullColumn)\n        <result property=\"$!column.name\" column=\"$!column.obj.name\" jdbcType=\"$!column.ext.jdbcType\"/>\n#end\n    </resultMap>\n\n    <!-- 批量插入 -->\n    <insert id=\"insertBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($velocityHasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n        (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($velocityHasNext), #end#end)\n        </foreach>\n    </insert>\n    <!-- 批量插入或按主键更新 -->\n    <insert id=\"insertOrUpdateBatch\" keyProperty=\"$!pk.name\" useGeneratedKeys=\"true\">\n        insert into $!{tableInfo.obj.name}(#foreach($column in $tableInfo.otherColumn)$!column.obj.name#if($velocityHasNext), #end#end)\n        values\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            (#foreach($column in $tableInfo.otherColumn)#{entity.$!{column.name}}#if($velocityHasNext), #end#end)\n        </foreach>\n        on duplicate key update\n         #foreach($column in $tableInfo.otherColumn)$!column.obj.name = values($!column.obj.name) #if($velocityHasNext), #end#end\n    </insert>\n\n</mapper>\n"}, {"name": "vo.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"VO\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/vo/\",${customPackageName}), \"VO.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"vo.\", ${customPackageName}))\n\n##自动导入包（全局变量）\n$!autoImport\nimport lombok.Data;\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\n#set($hasDate = false)\n#set($hasBigDecimal = false)\n#foreach($column in $tableInfo.fullColumn)\n    #if(${hasDate}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='Date' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.util.Date;\n            #set($hasDate = true)\n\t    #end\n\t#end\n\t#if(${hasBigDecimal}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='BigDecimal' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.math.BigDecimal;\n            #set($hasBigDecimal = true)\n\t    #end\n\t#end\n#end\n\n##表注释（宏定义）\n#tableComment(\" VO\")\n@ApiModel(\"$!{tableInfo.comment}VO\")\n@Data\npublic class $!{tableName} {\n\n#set($i = 1)\n#foreach($column in $tableInfo.fullColumn)\n\t#if(${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\n    #if(${column.comment})\n    /**\n     * ${column.comment}\n     */\n\t#end\n\t@ApiModelProperty(value = \"${column.comment}\", position = $i)\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n    #set($i = $i + 1)\n    \n\t#end\n#end\n}\n"}, {"name": "query-dto.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"QueryDTO\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/dto/\",${customPackageName}), \"QueryDTO.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"dto.\", ${customPackageName}))\n\n##自动导入包（全局变量）\n$!autoImport\nimport lombok.Data;\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\n#set($hasDate = false)\n#set($hasBigDecimal = false)\n#foreach($column in $tableInfo.fullColumn)\n    #if(${hasDate}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='Date' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.util.Date;\n            #set($hasDate = true)\n\t    #end\n\t#end\n\t#if(${hasBigDecimal}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='BigDecimal' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.math.BigDecimal;\n            #set($hasBigDecimal = true)\n\t    #end\n\t#end\n#end\n\n##表注释（宏定义）\n#tableComment(\" 查询DTO\")\n@ApiModel(\"$!{tableInfo.comment}查询DTO\")\n@Data\npublic class $!{tableName} {\n\n#set($i = 1)\n#foreach($column in $tableInfo.fullColumn)\n\t#if(${column.name}!='id' &&  ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\n    #if(${column.comment})\n    /**\n     * ${column.comment}\n     */\n\t#end\n\t@ApiModelProperty(value = \"${column.comment}\", position = $i)\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n    #set($i = $i + 1)\n    \n\t#end\n#end\n}\n"}, {"name": "insert-dto.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"InsertDTO\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/dto/\",${customPackageName}), \"InsertDTO.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"dto.\", ${customPackageName}))\n\n##自动导入包（全局变量）\n$!autoImport\n\nimport lombok.Data;\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\n\n#set($hasDate = false)\n#set($hasBigDecimal = false)\n#foreach($column in $tableInfo.fullColumn)\n    #if(${hasDate}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='Date' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.util.Date;\n            #set($hasDate = true)\n\t    #end\n\t#end\n\t#if(${hasBigDecimal}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='BigDecimal' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.math.BigDecimal;\n            #set($hasBigDecimal = true)\n\t    #end\n\t#end\n#end\n\n##表注释（宏定义）\n#tableComment(\" 新增DTO\")\n@ApiModel(\"$!{tableInfo.comment}新增DTO\")\n@Data\npublic class $!{tableName} {\n\n#set($i = 1)\n#foreach($column in $tableInfo.fullColumn)\n\t#if(${column.name}!='id' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\n    #if(${column.comment})\n    /**\n     * ${column.comment}\n     */\n\t#end\n\t@ApiModelProperty(value = \"${column.comment}\", position = $i)\n    private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n    #set($i = $i + 1)\n    \n\t#end\n#end\n}\n"}, {"name": "update-dto.java.vm", "code": "##导入宏定义\n$!{define.vm}\n\n##设置表后缀（宏定义）\n#setTableSuffix(\"UpdateDTO\")\n\n## 设置自定义包名称\n#set ($customPackageName=$tool.getJavaName($!{tableInfo.obj.name}))\n\n##保存文件（宏定义）\n#save($tool.append(\"/dto/\",${customPackageName}), \"UpdateDTO.java\")\n\n##包路径（宏定义）\n#setPackageSuffix($!tool.append(\"dto.\", ${customPackageName}))\n\n##自动导入包（全局变量）\n$!autoImport\n\nimport com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;\nimport lombok.Data;\nimport io.swagger.annotations.ApiModel;\nimport io.swagger.annotations.ApiModelProperty;\n\nimport javax.validation.constraints.NotNull;\n\n#set($hasDate = false)\n#set($hasBigDecimal = false)\n#foreach($column in $tableInfo.fullColumn)\n    #if(${hasDate}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='Date' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.util.Date;\n            #set($hasDate = true)\n\t    #end\n\t#end\n\t#if(${hasBigDecimal}==false)\n        #if($!{tool.getClsNameByFullName($column.type)}=='BigDecimal' && ${column.name}!='createTime' && ${column.name}!='creator' && ${column.name}!='updateTime' && ${column.name}!='updater' && ${column.name}!='deleted')\nimport java.math.BigDecimal;\n            #set($hasBigDecimal = true)\n\t    #end\n\t#end\n#end\n\n##表注释（宏定义）\n#tableComment(\" 更新DTO\")\n@ApiModel(\"$!{tableInfo.comment}更新DTO\")\n@Data\npublic class $!{tableName} extends $!{tableInfo.name}InsertDTO {\n\n    /**\n     * ID\n     */\n    @ApiModelProperty(value = \"ID\", position = 1)\n    @NotNull(groups = UpdateValidGroup.class, message = \"ID不能为空\")\n    private Long id;\n\n}\n"}]}}, "columnConfig": {}, "globalConfig": {}}