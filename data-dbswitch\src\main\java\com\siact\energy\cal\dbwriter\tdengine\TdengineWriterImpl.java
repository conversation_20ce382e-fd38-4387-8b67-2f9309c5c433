package com.siact.energy.cal.dbwriter.tdengine;

import com.siact.energy.cal.dbwriter.AbstractDatabaseWriter;
import com.siact.energy.cal.dbwriter.IDatabaseWriter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Package com.siact.dwenergy.datadbswitch.dbwriter.tdengine
 * @description:
 * @create 2023/9/4 14:14
 */
public class TdengineWriterImpl extends AbstractDatabaseWriter implements IDatabaseWriter {


    public TdengineWriterImpl(DataSource dataSource) {
        super(dataSource);
    }

    @Override
    protected String getDatabaseProductName() {
        return "TDengine";
    }

    @Override
    public long write(List<String> fieldNames, List<Object[]> recordValues) {
        if (recordValues.isEmpty()) {
            return 0;
        }
        List<String> valueList = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        for (String fieldName : fieldNames) {
            if ("ts".equals(fieldName) || "propValue".equals(fieldName)
                    || "correctvalue".equals(fieldName) || "correctid".equals(fieldName)) {
                valueList.add(fieldName);
            } else {
                tagList.add(fieldName);
            }
        }
        String sqlInsert = String.format("INSERT INTO %s USING  %s.%s TAGS( %s ) VALUES ( %s )",
                "?",
                schemaName, tableName,
                StringUtils.join(Collections.nCopies(tagList.size(), "?"), ","),
                StringUtils.join(Collections.nCopies(valueList.size(), "?"), ","));
        long count = 0;
        try {
            Connection connection = dataSource.getConnection();
            PreparedStatement ps = connection.prepareStatement(sqlInsert);

            for (int i = 0; i < recordValues.size(); i++) {
                Object[] objects = recordValues.get(i);

                ps.setObject(1, objects[1]);
                ps.setObject(2, objects[1]);
                ps.setObject(3, objects[0]);
                ps.setObject(4, objects[2]);
                ps.addBatch();


                if (i % 1000 == 0) {
                    int[] ints = ps.executeBatch();
                    count += ints.length;
                    ps.clearBatch();
                } else if (i == recordValues.size() - 1) {
                    int[] ints = ps.executeBatch();
                    count += ints.length;
                    ps.clearBatch();
                }

            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return count;
    }

    @Override
    public void prepareWrite(String schemaName, String tableName, List<String> fieldNames) {
        String sql = this.selectTableMetaDataSqlString(schemaName, tableName, fieldNames);
        Map<String, Integer> columnMetaData = new HashMap<>();
        jdbcTemplate.execute((Connection conn) -> {
            try (Statement stmt = conn.createStatement();
                 ResultSet rs = stmt.executeQuery(sql);) {
                ResultSetMetaData rsMetaData = rs.getMetaData();
                for (int i = 0, len = rsMetaData.getColumnCount(); i < len; i++) {
                    columnMetaData.put(rsMetaData.getColumnName(i + 1), rsMetaData.getColumnType(i + 1));
                }

                return true;
            } catch (Exception e) {
                throw new RuntimeException(
                        String.format("获取表:%s.%s 的字段的元信息时失败. 请联系 DBA 核查该库、表信息.", schemaName, tableName), e);
            }
        });

        this.schemaName = schemaName;
        this.tableName = tableName;
        this.columnType = Objects.requireNonNull(columnMetaData);
        if (this.columnType.isEmpty()) {
            throw new RuntimeException(
                    String.format("获取表:%s.%s 的字段的元信息时失败. 请联系 DBA 核查该库、表信息.", schemaName, tableName));
        }

    }

    protected String selectTableMetaDataSqlString(String schemaName, String tableName,
                                                  List<String> fieldNames) {
        if (CollectionUtils.isEmpty(fieldNames)) {
            return String.format("SELECT *  FROM %s.%s limit 1 ", schemaName, tableName);
        } else {
            return String.format("SELECT %s  FROM %s.%s limit 1 ",
                    StringUtils.join(fieldNames, ","), schemaName, tableName);
        }
    }
}
