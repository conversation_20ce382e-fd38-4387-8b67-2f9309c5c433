package com.siact.energy.cal.common.core.domain;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * RStatus
 *
 * <AUTHOR>
 * @since 2024-05-13 16:02:31
 */
@Getter
@AllArgsConstructor
public enum RStatus {

    /* 成功 **/
    SUCCESS("200", "操作成功"),
    /* 服务器内部错误 **/
    ERROR("500", "服务器内部错误"),

    /**
     * 客户端默认错误
     */
    CLIENT_ERROR("400", "客户端错误"),
    /**
     * 数据库连接错误
     */
    CONNECT_ERROR("401", "数据库连接错误"),


    /**
     * 批量操作-请选择数据，再进行操作（未选择数据操作）
     */
    UNSELECTED_DATA(ResponseCodeConstant.RC_40000001, "请选择数据，再进行操作"),

    ;

    /**
     * 响应状态码
     */
    private final String code;

    /**
     * 响应消息
     */
    private final String message;

    public static RStatus get(String value) {
        return enumObjMap.get(value);
    }

    public String getCode() {
        return code;
    }

    private static final Map<String, RStatus> enumObjMap = new HashMap();

    static {
        initEnumMap();
    }

    private static void initEnumMap() {
        for (RStatus enumObj : values()) {
            enumObjMap.put(enumObj.getCode(), enumObj);
        }
    }
}
