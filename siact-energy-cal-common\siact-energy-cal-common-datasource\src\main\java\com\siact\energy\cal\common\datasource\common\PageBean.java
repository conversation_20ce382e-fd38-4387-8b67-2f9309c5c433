package com.siact.energy.cal.common.datasource.common;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;

/**
 * PageBean
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:19
 */
@ApiModel("分页数据")
public class PageBean<T> extends Page<T> {

    /**
     * 数据列表
     */
    @ApiModelProperty(value = "数据列表", position = 1)
    protected List<T> records = Collections.emptyList();

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页，默认 1 ", position = 2)
    protected long current = 1;

    /**
     * 每页显示条数，默认 10
     */
    @ApiModelProperty(value = "每页条数，默认 10 ", position = 3)
    protected long size = 10;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数", position = 4)
    protected long total = 0;

    @ApiModelProperty(value = "总页数", position = 5)
    private Integer pages;

    /**
     * 排序字段信息
     */
    @Setter
    @ApiModelProperty(value = "排序字段信息", position = 6)
    protected List<OrderItem> orders = new ArrayList<>();

    /**
     * 自动优化 COUNT SQL
     */
    @ApiModelProperty(value = "是否自动优化SQL", position = 7, hidden = true)
    protected boolean optimizeCountSql = true;

    /**
     * 是否进行 count 查询
     */
    @ApiModelProperty(value = "是否查询总数count", position = 8, hidden = true)
    protected boolean searchCount = true;

    /**
     * {@link #optimizeJoinOfCountSql()}
     */
    @Setter
    @ApiModelProperty(value = "是否优化联合查询", position = 9, hidden = true)
    protected boolean optimizeJoinOfCountSql = true;

    /**
     * 单页分页条数限制
     */
    @Setter
    @ApiModelProperty(value = "单页分页条数限制", position = 10, hidden = true)
    protected Long maxLimit;

    /**
     * countId
     */
    @Setter
    @ApiModelProperty(value = "countId", position = 11, hidden = true)
    protected String countId;

    /**
     * 默认无参构造
     */
    public PageBean() {
    }

    /**
     * 分页构造函数
     *
     * @param current 当前页
     * @param size    每页显示条数
     */
    public PageBean(long current, long size) {
        this(current, size, 0);
    }

    public PageBean(long current, long size, long total) {
        this(current, size, total, true);
    }

    public PageBean(long current, long size, boolean searchCount) {
        this(current, size, 0, searchCount);
    }

    /**
     * 复制父类构造方法
     *
     * @param current 当前页
     * @param size 每页条数
     * @param total 总条数
     * @param searchCount 是否查询总数
     */
    public PageBean(long current, long size, long total, boolean searchCount) {
        if (current > 1) {
            this.current = current;
        }
        this.size = size;
        this.total = total;
        this.searchCount = searchCount;
    }

    /**
     * 是否存在上一页
     *
     * @return true / false
     */
    @Override
    public boolean hasPrevious() {
        return this.current > 1;
    }

    /**
     * 是否存在下一页
     *
     * @return true / false
     */
    @Override
    public boolean hasNext() {
        return this.current < this.getPages();
    }

    @Override
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    public Page<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    public long getTotal() {
        return this.total;
    }

    @Override
    public Page<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    public long getSize() {
        return this.size;
    }

    @Override
    public Page<T> setSize(long size) {
        this.size = size;
        return this;
    }

    @Override
    public long getCurrent() {
        return this.current;
    }

    @Override
    public Page<T> setCurrent(long current) {
        this.current = current;
        return this;
    }

    @Override
    public String countId() {
        return this.countId;
    }

    @Override
    public Long maxLimit() {
        return this.maxLimit;
    }

    /**
     * 查找 order 中正序排序的字段数组
     *
     * @param filter 过滤器
     * @return 返回正序排列的字段数组
     */
    private String[] mapOrderToArray(Predicate<OrderItem> filter) {
        List<String> columns = new ArrayList<>(orders.size());
        orders.forEach(i -> {
            if (filter.test(i)) {
                columns.add(i.getColumn());
            }
        });
        return columns.toArray(new String[0]);
    }

    /**
     * 移除符合条件的条件
     *
     * @param filter 条件判断
     */
    private void removeOrder(Predicate<OrderItem> filter) {
        for (int i = orders.size() - 1; i >= 0; i--) {
            if (filter.test(orders.get(i))) {
                orders.remove(i);
            }
        }
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    @Override
    public Page<T> addOrder(OrderItem... items) {
        orders.addAll(Arrays.asList(items));
        return this;
    }

    /**
     * 添加新的排序条件，构造条件可以使用工厂
     *
     * @param items 条件
     * @return 返回分页参数本身
     */
    @Override
    public Page<T> addOrder(List<OrderItem> items) {
        orders.addAll(items);
        return this;
    }

    @Override
    public List<OrderItem> orders() {
        return this.orders;
    }

    @Override
    public boolean optimizeCountSql() {
        return optimizeCountSql;
    }

    @Override
    public boolean optimizeJoinOfCountSql() {
        return optimizeJoinOfCountSql;
    }

    @Override
    public Page<T> setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
        return this;
    }

    @Override
    public Page<T> setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
        return this;
    }

    @Override
    public long getPages() {
        // 解决 github issues/3208
        return super.getPages();
    }

    /**
     * 复制父类静态构建方法
     *
     * @param current 当前页
     * @param size 每页条数
     * @return 分页对象
     * @param <T> 数据类型
     */
    public static <T> PageBean<T> of(long current, long size) {
        return of(current, size, 0);
    }

    /**
     * 复制父类静态构建方法
     *
     * @param current 当前页
     * @param size 每页条数
     * @param total 总条数
     * @return 分页对象
     * @param <T> 数据类型
     */
    public static <T> PageBean<T> of(long current, long size, long total) {
        return of(current, size, total, true);
    }

    /**
     * 复制父类静态构建方法
     *
     * @param current 当前页
     * @param size 每页条数
     * @param total 总条数
     * @param searchCount 是否查询总数
     * @return 分页对象
     * @param <T> 数据类型
     */
    public static <T> PageBean<T> of(long current, long size, long total, boolean searchCount) {
        return new PageBean<>(current, size, total, searchCount);
    }

    @Override
    public boolean searchCount() {
        if (total < 0) {
            return false;
        }
        return searchCount;
    }
}
