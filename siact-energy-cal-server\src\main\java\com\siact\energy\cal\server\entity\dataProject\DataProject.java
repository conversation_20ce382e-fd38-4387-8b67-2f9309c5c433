package com.siact.energy.cal.server.entity.dataProject;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.siact.energy.cal.common.datasource.common.BaseEntity;

/**
 * 项目表(DataProject)表实体类
 *
 * <AUTHOR>
 * @since 2024-05-14 15:36:22
 */
@Data
public class DataProject extends BaseEntity {

    /**
     * ID
     */
    @OrderBy(asc = true, sort = Short.MIN_VALUE)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String projectName;

    /**
     * 项目编码
     */
    @TableField(condition = SqlCondition.LIKE)
    private String projectCode;

}

