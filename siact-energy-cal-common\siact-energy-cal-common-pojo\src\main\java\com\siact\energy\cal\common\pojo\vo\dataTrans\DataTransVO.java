package com.siact.energy.cal.common.pojo.vo.dataTrans;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 数据传输表（mqtt）(DataTrans) VO
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:55
 */
@ApiModel("数据传输表（mqtt）VO")
@Data
public class DataTransVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1, example = "1790665515066048514")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 2, example = "10000")
    private Long projectId;

    /**
     * 主机名
     */
    @ApiModelProperty(value = "主机名", position = 3, example = "**************")
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口", position = 4, example = "1883")
    private String port;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 5, example = "mqttuser")
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 6, example = "mqttuser")
    private String password;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题", position = 7, example = "/#")
    private String topic;

}

