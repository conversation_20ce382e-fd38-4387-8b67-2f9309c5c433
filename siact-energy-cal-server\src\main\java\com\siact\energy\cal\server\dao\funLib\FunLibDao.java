package com.siact.energy.cal.server.dao.funLib;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.server.entity.funLib.FunLib;

/**
 * 常用函数库(FunLib)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-11 09:47:21
 */
@Repository
public interface FunLibDao extends BaseMapper<FunLib> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FunLib> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<FunLib> entities);

}

