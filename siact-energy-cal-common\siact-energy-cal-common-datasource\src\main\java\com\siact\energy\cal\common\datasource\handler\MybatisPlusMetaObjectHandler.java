package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;

/**
 * MyBatis Plus自动填充处理器
 * 处理BaseEntity的自动填充：createTime, updateTime, deleted
 * 注意：creator和updater字段由业务代码设置
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();
        strictInsertFill(metaObject, "createTime", Date.class, date);
        strictInsertFill(metaObject, "updateTime", Date.class, date);
        strictInsertFill(metaObject, "deleted", Integer.class, 0);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
    }

    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }

}
