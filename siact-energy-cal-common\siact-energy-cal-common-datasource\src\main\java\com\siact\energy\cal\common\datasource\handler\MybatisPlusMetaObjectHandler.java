package com.siact.energy.cal.common.datasource.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Supplier;

/**
 * MyBatis Plus自动填充处理器
 * 处理BaseEntity的自动填充：createTime, updateTime, deleted, creator, updater
 * 自动获取当前登录用户填充creator和updater字段
 *
 * <AUTHOR>
 * @since 2024-05-13 16:06:39
 */
@Slf4j
@Component
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler, ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 需要覆盖填充的字段，默认填充是需要原值为null
     */
    private final List<String> needOverlayFillField = Collections.unmodifiableList(Arrays.asList("updateTime"));

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = Objects.requireNonNull(applicationContext, "ApplicationContext must not be null");
    }

    @Override
    public void insertFill(MetaObject metaObject) {
        Date date = new Date();
        strictInsertFill(metaObject, "createTime", Date.class, date);
        strictInsertFill(metaObject, "updateTime", Date.class, date);
        strictInsertFill(metaObject, "deleted", Integer.class, 0);

        // 自动填充creator和updater字段
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            strictInsertFill(metaObject, "creator", Long.class, currentUserId);
            strictInsertFill(metaObject, "updater", Long.class, currentUserId);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        strictUpdateFill(metaObject, "updateTime", Date.class, new Date());

        // 自动填充updater字段
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            strictUpdateFill(metaObject, "updater", Long.class, currentUserId);
        }
    }



    @Override
    public MetaObjectHandler strictFillStrategy(MetaObject metaObject, String fieldName, Supplier<?> fieldVal) {
        // 目标值是空，或者是需要覆盖填充的字段，此处修改的时候会覆盖自己设置的修改时间（主要是为了处理从数据库查询出来对象，修改个别字段后直接入库的编辑，编辑时间不修改的问题）
        if (metaObject.getValue(fieldName) == null || needOverlayFillField.contains(fieldName)) {
            Object obj = fieldVal.get();
            if (Objects.nonNull(obj)) {
                metaObject.setValue(fieldName, obj);
            }
        }
        return this;
    }

    /**
     * 获取当前登录用户ID
     * 通过反射调用BaseService的getLoginUserId方法
     */
    private Long getCurrentUserId() {
        if (applicationContext == null) {
            log.debug("ApplicationContext未初始化，无法获取BaseService");
            return null;
        }

        try {
            // 尝试获取BaseService bean
            Object baseService = null;
            try {
                baseService = applicationContext.getBean("baseServiceImpl");
            } catch (Exception e) {
                // 如果找不到baseServiceImpl，尝试通过类型查找
                try {
                    String[] beanNames = applicationContext.getBeanNamesForType(
                        Class.forName("com.siact.energy.cal.tool.service.BaseService"));
                    if (beanNames.length > 0) {
                        baseService = applicationContext.getBean(beanNames[0]);
                    }
                } catch (Exception ex) {
                    // 尝试server模块的BaseService
                    try {
                        String[] serverBeanNames = applicationContext.getBeanNamesForType(
                            Class.forName("com.siact.energy.cal.server.service.BaseService"));
                        if (serverBeanNames.length > 0) {
                            baseService = applicationContext.getBean(serverBeanNames[0]);
                        }
                    } catch (Exception serverEx) {
                        log.debug("无法找到BaseService实现: {}", serverEx.getMessage());
                    }
                }
            }

            if (baseService != null) {
                // 通过反射调用getLoginUserId方法
                Method getLoginUserIdMethod = baseService.getClass().getMethod("getLoginUserId");
                Object result = getLoginUserIdMethod.invoke(baseService);
                return (Long) result;
            }
        } catch (Exception e) {
            log.debug("获取当前登录用户失败: {}", e.getMessage());
        }

        return null;
    }

}
