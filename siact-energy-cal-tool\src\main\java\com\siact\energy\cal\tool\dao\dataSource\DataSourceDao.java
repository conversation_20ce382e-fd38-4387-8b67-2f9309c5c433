package com.siact.energy.cal.tool.dao.dataSource;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceQueryDTO;
import com.siact.energy.cal.common.pojo.vo.dataSource.DataSourceVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.siact.energy.cal.tool.entity.dataSource.DataSource;

/**
 * 数据源表(DataSource)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:12
 */
@Repository
public interface DataSourceDao extends BaseMapper<DataSource> {

    /**
     * 批量新增数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<DataSource> entities);

    /**
     * 批量新增或按主键更新数据
     *
     * @param entities 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<DataSource> entities);

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param dataSourceQueryDTO 查询参数对象
     * @return 分页列表数据
     */
    PageBean<DataSourceVO> listPage(PageBean<DataSourceVO> page,
                                    @Param("query") DataSourceQueryDTO dataSourceQueryDTO);

    List<com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo> getDataSource();
}

