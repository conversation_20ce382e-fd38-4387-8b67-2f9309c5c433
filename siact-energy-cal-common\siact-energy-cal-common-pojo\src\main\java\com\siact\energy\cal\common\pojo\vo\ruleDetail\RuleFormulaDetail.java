package com.siact.energy.cal.common.pojo.vo.ruleDetail;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 指标公式详情
 *
 * <AUTHOR>
 * @since 2024-05-21 10:07:00
 */
@ApiModel("指标详情表VO")
@Data
public class RuleFormulaDetail {

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", position = 2)
    private String ruleName;

    /**
     * 计算类型（1-基础指标，2-聚合指标,3-衍生指标）
     */
    @ApiModelProperty(value = "计算类型（1-基础指标，2-聚合指标,3-衍生指标）", position = 5)
    private Integer calType;

    /**
     * 节点（模型）编码
     */
    @ApiModelProperty(value = "节点（模型）编码", position = 6)
    private String devCode;

    /**
     * 节点（模型）名称
     */
    @ApiModelProperty(value = "节点（模型）名称", position = 7)
    private String devName;

    /**
     * 属性编码，模型编码
     */
    @ApiModelProperty(value = "属性编码，模型编码", position = 8)
    private String devProperty;

    /**
     * 属性（模型）名称
     */
    @ApiModelProperty(value = "属性（模型）名称", position = 9)
    private String propName;

    /**
     * 公式表达式
     */
    @ApiModelProperty(value = "公式表达式", position = 10)
    private String ruleFormula;

    /**
     * 所属规则集id
     */
    @ApiModelProperty(value = "所属规则集id", position = 13)
    private Long ruleColId;

}

