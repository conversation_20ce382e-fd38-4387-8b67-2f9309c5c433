package com.siact.energy.cal.common.pojo.dto.energycal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Package com.siact.energy.cal.common.pojo.dto.energycal
 * @description: 时间区间公式计算
 * <AUTHOR>
 * @create 2024/12/11 14:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "实时计算公式时间区间DTO")
public class CalculateFormulaIntervalTimeDTO {
    @ApiModelProperty(value = "列表",position = 1,required = true)
    private List<CalFormula> calFormulaList;
    @ApiModelProperty(value = "开始时间",position = 2,required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间",position = 3,required = true)
    private String endTime;
}
