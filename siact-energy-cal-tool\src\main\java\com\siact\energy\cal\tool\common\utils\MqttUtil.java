package com.siact.energy.cal.tool.common.utils;

import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import javax.validation.constraints.NotBlank;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MqttUtil
 *
 * <AUTHOR>
 * @since 2024-05-15 17:25:42
 */
public class MqttUtil {

    /**
     * 订阅ID与MqttClient对象映射集
     */
    private static Map<String, MqttClient> mqttClientMap = new ConcurrentHashMap<>();

    /**
     * 订阅ID与MqttTopicFilter映射集
     */
    private static Map<String, String> mqttTopicFilterMap = new ConcurrentHashMap<>();

    /**
     * 订阅ID与监听器映射集
     */
    private static Map<String, IMqttMessageListener> mqttMessageListenerMap = new ConcurrentHashMap<>();


    /**
     * 创建MQTT客户端并连接、缓存
     *
     * @param host 主机
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @param id 唯一ID
     * @return MQTT客户端
     * @throws MqttException 异常
     */
    public static MqttClient createAndConnectMqttClientCache(String host,
                                                             String port,
                                                             String username,
                                                             String password,
                                                             @NotBlank String id) throws MqttException {

        String serverURI = String.format("tcp://%s:%s", host, port);
        MqttClient mqttClient = new MqttClient(serverURI, String.join("-", "temp_energy_cal", UUID.randomUUID().toString()), new MemoryPersistence());
        mqttClient.connect(getDefaultMqttConnectOptions(username, password));
        mqttClient.setCallback(new CustomMqttCallbackExtended(id));
        mqttClientMap.put(id, mqttClient);
        return mqttClient;
    }

    /**
     * 订阅主题并缓存
     *
     * @param mqttClient MQTT客户端
     * @param topicFilter 订阅主题
     * @param mqttMessageListener 消息监听器
     * @param id 唯一ID
     * @throws MqttException 异常
     */
    public static void subscribeAndCache(MqttClient mqttClient,
                                         String topicFilter,
                                         IMqttMessageListener mqttMessageListener,
                                         @NotBlank String id) throws MqttException {
        mqttClient.subscribe(topicFilter, mqttMessageListener);
        mqttTopicFilterMap.put(id, topicFilter);
        mqttMessageListenerMap.put(id, mqttMessageListener);
    }

    /**
     * 取消订阅主题
     *
     * @param id 唯一ID
     * @throws MqttException 异常
     */
    public static void unsubscribeAndRemoveCache(@NotBlank String id) throws MqttException {
        if(mqttClientMap.containsKey(id)
                && mqttTopicFilterMap.containsKey(id)) {
            MqttClient mqttClient = mqttClientMap.get(id);
            String topicFilter = mqttTopicFilterMap.get(id);
            mqttClient.unsubscribe(new String[]{topicFilter});
            // 移除订阅关系缓存
            mqttTopicFilterMap.remove(id);
            // 移除监听器关系缓存
            mqttMessageListenerMap.remove(id);
        }
    }

    /**
     * 销毁Mqtt客户端
     *
     * @param id 唯一ID
     */
    public static void destroyMqttClient(String id) throws MqttException {
        if(mqttClientMap.containsKey(id)) {
            MqttClient mqttClient = mqttClientMap.get(id);
            // 断开连接
            mqttClient.disconnect();
            // 关闭客户端
            mqttClient.close();
            // 移除客户端缓存
            mqttClientMap.remove(id);
            // 移除订阅关系缓存
            mqttTopicFilterMap.remove(id);
            // 移除监听器关系缓存
            mqttMessageListenerMap.remove(id);
        }
    }

    /**
     * 判断是否有订阅
     *
     * @param id 唯一ID
     * @return 是否有订阅
     */
    public static boolean hasSubscribe(String id) {
        return mqttClientMap.containsKey(id) || mqttTopicFilterMap.containsKey(id);
    }

    /**
     * 重新订阅
     *
     * @param id 唯一ID
     */
    public static void reSubscribe(@NotBlank String id) throws MqttException {
        if(mqttClientMap.containsKey(id)
                && mqttTopicFilterMap.containsKey(id)
                && mqttMessageListenerMap.containsKey(id)) {
            MqttClient mqttClient = mqttClientMap.get(id);
            String topicFilter = mqttTopicFilterMap.get(id);
            IMqttMessageListener mqttMessageListener = mqttMessageListenerMap.get(id);
            mqttClient.subscribe(topicFilter, mqttMessageListener);
        }
    }

    /**
     * 获取默认配置
     *
     * @param userName 账号
     * @param passWord 密码
     * @return 默认配置
     */
    private static MqttConnectOptions getDefaultMqttConnectOptions(String userName, String passWord) {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setUserName(userName);
        options.setPassword(passWord.toCharArray());
        options.setConnectionTimeout(30);
        options.setAutomaticReconnect(true);
        options.setCleanSession(true);
        return options;
    }

}
