package com.siact.energy.cal.tool.service.dataProject;


import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.List;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.tool.entity.dataProject.DataProject;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataProject.DataProjectUpdateDTO;

/**
 * 项目表(DataProject)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-14 15:27:00
 */
public interface DataProjectService extends IService<DataProject> {

    /**
     * 注册列表
     *
     * @param page                分页对象
     * @param dataProjectQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<DataProjectVO> listPage(PageBean<DataProjectVO> page, DataProjectQueryDTO dataProjectQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    DataProjectVO getVoById(Serializable id);

    /**
     * 新增数据
     *
     * @param dataProjectInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(DataProjectInsertDTO dataProjectInsertDTO);

    /**
     * 修改数据
     *
     * @param dataProjectUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(DataProjectUpdateDTO dataProjectUpdateDTO);

    /**
     * 项目列表(全部)
     *
     * @return 查询结果
     */
    List<DataProjectVO> listAll();

    /**
     * 从数字孪生更新项目
     *
     * @return 更新结果
     */
    int updateProjectByDigitalTwin();
}

