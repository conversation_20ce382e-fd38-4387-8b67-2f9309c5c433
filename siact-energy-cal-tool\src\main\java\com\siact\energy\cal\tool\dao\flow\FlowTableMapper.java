package com.siact.energy.cal.tool.dao.flow;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.energy.cal.tool.entity.flow.FlowViewEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.Map;

@Mapper
public interface FlowTableMapper extends BaseMapper<FlowViewEntity> {
    int getTableCount(String tableName);
    Integer saveItemInput(String tableName, Map map);

}
