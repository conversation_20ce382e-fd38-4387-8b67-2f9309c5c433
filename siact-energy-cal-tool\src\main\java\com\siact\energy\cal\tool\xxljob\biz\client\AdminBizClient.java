package com.siact.energy.cal.tool.xxljob.biz.client;


import com.siact.energy.cal.tool.xxljob.biz.AdminBiz;
import com.siact.energy.cal.tool.xxljob.biz.model.HandleCallbackParam;
import com.siact.energy.cal.tool.xxljob.biz.model.RegistryParam;
import com.siact.energy.cal.tool.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.tool.xxljob.util.XxlJobRemotingUtil;

import java.util.List;

/**
 * admin api test
 *
 * <AUTHOR> 2017-07-28 22:14:52
 */
public class AdminBizClient implements AdminBiz {

    public AdminBizClient() {
    }
    public AdminBizClient(String addressUrl, String accessToken) {
        this.addressUrl = addressUrl;
        this.accessToken = accessToken;

        // valid
        if (!this.addressUrl.endsWith("/")) {
            this.addressUrl = this.addressUrl + "/";
        }
    }

    private String addressUrl ;
    private String accessToken;
    private int timeout = 3;


    @Override
    public ReturnT<String> callback(List<HandleCallbackParam> callbackParamList) {
        return XxlJobRemotingUtil.postBody1(addressUrl+"api/callback", accessToken, timeout, callbackParamList, String.class);
    }

    @Override
    public ReturnT<String> registry(RegistryParam registryParam) {
        return XxlJobRemotingUtil.postBody(addressUrl + "api/registry", accessToken, timeout, registryParam, String.class);
    }

    @Override
    public ReturnT<String> registryRemove(RegistryParam registryParam) {
        return XxlJobRemotingUtil.postBody(addressUrl + "api/registryRemove", accessToken, timeout, registryParam, String.class);
    }

}
