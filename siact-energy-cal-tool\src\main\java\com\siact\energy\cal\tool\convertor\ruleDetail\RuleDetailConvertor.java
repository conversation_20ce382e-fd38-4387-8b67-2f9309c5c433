package com.siact.energy.cal.tool.convertor.ruleDetail;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail;
import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailQueryDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailInsertDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailUpdateDTO;

/**
 * 指标详情表(RuleDetail) Convertor
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Mapper
public interface RuleDetailConvertor extends AbstractConvertor<RuleDetailVO, RuleDetail> {

    RuleDetailConvertor INSTANCE = Mappers.getMapper(RuleDetailConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<RuleDetailVO> entityPage2VoPageBean(Page<RuleDetail> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<RuleDetail> voPageBean2EntityPage(PageBean<RuleDetailVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleDetail queryDTO2Entity(RuleDetailQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleDetail insertDTO2Entity(RuleDetailInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    RuleDetail updateDTO2Entity(RuleDetailUpdateDTO dto);

}
