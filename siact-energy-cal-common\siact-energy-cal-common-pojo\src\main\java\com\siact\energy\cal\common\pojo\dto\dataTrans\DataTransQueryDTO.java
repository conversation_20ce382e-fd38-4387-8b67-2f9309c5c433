package com.siact.energy.cal.common.pojo.dto.dataTrans;


import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 数据传输表（mqtt）(DataTrans) 查询DTO
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:55
 */
@ApiModel("数据传输表（mqtt）查询DTO")
@Data
public class DataTransQueryDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 1)
    private Long projectId;

    /**
     * 主机名
     */
    @ApiModelProperty(value = "主机名", position = 2)
    private String host;

    /**
     * 端口
     */
    @ApiModelProperty(value = "端口", position = 3)
    private String port;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", position = 4)
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", position = 5)
    private String password;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题", position = 6)
    private String topic;

}

