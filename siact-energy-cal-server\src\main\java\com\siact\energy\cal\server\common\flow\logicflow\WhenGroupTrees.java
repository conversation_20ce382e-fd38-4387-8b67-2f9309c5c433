package com.siact.energy.cal.server.common.flow.logicflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 多个节点组合
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhenGroupTrees {
    /**
     * when Group 节点
     */
    String whenGroupNodeId;

    /**
     * when中有多棵树
     */
    List<ELNode> elNodeList;

}
