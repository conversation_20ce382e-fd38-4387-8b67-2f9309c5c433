package com.siact.energy.cal.server.common.flow.logicflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 上下文
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LfNode {
    private String id;
    private String type;
    private Integer x;
    private Integer y;
    private Map<String,Object> properties;
    private TextEntity text;
    private List<String> children;

    @Data
    public static class TextEntity {
        private Integer x;
        private Integer y;
        private String value;
    }
}