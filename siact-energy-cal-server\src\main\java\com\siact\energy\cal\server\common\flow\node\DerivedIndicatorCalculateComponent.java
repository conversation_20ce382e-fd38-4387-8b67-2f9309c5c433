package com.siact.energy.cal.server.common.flow.node;

import com.siact.energy.cal.common.pojo.dto.energycal.TimeQueryDTO;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-19
 * @Description: 衍生指标计算组件
 * @Version: 1.0
 */
@Component("DerivedIndicatorCalNode")
@Slf4j
public class DerivedIndicatorCalculateComponent extends NodeComponent  {

    @Resource
    EnergyCalService energyCalService;
    @Resource
    FlowViewServiceImpl flowViewService;
    @Resource
    BaseIndicatorCalculateComponent baseIndicatorCalculateComponent;
    @Resource
    DataSourceService dataSourceService;
    @Override
    public void process() {
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        //查询规则calculateContext
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = calculateContext.getTaskId();
        String resultTable = calculateContext.getResultTable();
        log.info("衍生指标计算组件开始执行，tag:{}",nodeId);
        //获取上下文中的项目计算类型属性列表
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = calculateContext.getResultMap();
        Map<String, Map<String, List<String>>> projectCalTypeMap = calculateContext.getProjectCalTypeMap();
        //从projectCalTypeMap中拿出所有衍生指标
        Set<String> derivedIndicators = new HashSet<>();
        projectCalTypeMap.entrySet().forEach(entry -> {
            String projectCode = entry.getKey();
            Map<String, List<String>> calTypePropMap = entry.getValue();
            //获取数据源配置信息
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (!ObjectUtils.isEmpty(dataSourceVo)) {
                TimeQueryDTO queryDataByIntervalTimeDTO = new TimeQueryDTO();
                queryDataByIntervalTimeDTO.setStartTime(calculateContext.getStartTime());
                queryDataByIntervalTimeDTO.setEndTime(calculateContext.getEndTime());
                queryDataByIntervalTimeDTO.setInterval(calculateContext.getInterval());
                queryDataByIntervalTimeDTO.setTsUnit(calculateContext.getTsUnit());
                //计算衍生指标
                List<String> derivedIndicatorByPropject = calTypePropMap.getOrDefault(ConstantBase.CAL_DER, null);
                if (!ObjectUtils.isEmpty(derivedIndicatorByPropject)) {
                    energyCalService.processDerivedIndicators(resultMap,calTypePropMap,queryDataByIntervalTimeDTO);
                    derivedIndicators.addAll(derivedIndicatorByPropject);
                }

            }
        });
        if(derivedIndicators.size()>0){
            //将resultMap放入开始组件创建的临时表中
            baseIndicatorCalculateComponent.updateResultMapToTempTable(resultTable,resultMap,derivedIndicators);
        }
        flowViewService.insertFlowTable(Integer.parseInt(flowId), resultTable, nodeId, taskId);
        calculateContext.setResultMap(resultMap);
        log.info("衍生指标计算组件执行完毕");
    }

}
