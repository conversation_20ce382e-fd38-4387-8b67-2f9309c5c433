package com.siact.energy.cal.server.xxljob.service.impl;


import com.siact.energy.cal.server.xxljob.biz.AdminBiz;
import com.siact.energy.cal.server.xxljob.biz.model.HandleCallbackParam;
import com.siact.energy.cal.server.xxljob.biz.model.RegistryParam;
import com.siact.energy.cal.server.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.server.xxljob.core.thread.JobCompleteHelper;
import com.siact.energy.cal.server.xxljob.core.thread.JobRegistryHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 2017-07-27 21:54:20
 */
@Service
public class AdminBizImpl implements AdminBiz {


    @Override
    public ReturnT<String> callback(List<HandleCallbackParam> callbackParamList) {
        return JobCompleteHelper.getInstance().callback(callbackParamList);
    }

    @Override
    public ReturnT<String> registry(RegistryParam registryParam) {
        return JobRegistryHelper.getInstance().registry(registryParam);
    }

    @Override
    public ReturnT<String> registryRemove(RegistryParam registryParam) {
        return JobRegistryHelper.getInstance().registryRemove(registryParam);
    }

}
