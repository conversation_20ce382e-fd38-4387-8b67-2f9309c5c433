package com.siact.energy.cal.server.convertor.flow;


import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ApiConfigDto;
import com.siact.energy.cal.common.pojo.vo.flow.ApiConfigVO;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-12
 * @Description:
 * @Version: 1.0
 */
@Mapper
public interface ApiConfigConvertor {
    ApiConfigConvertor INSTANCE = Mappers.getMapper(ApiConfigConvertor.class);


    ApiConfigEntity convertToEntity(ApiConfigDto apiConfigVO);

    List<ApiConfigVO> apiConfigEntityToVo(List<ApiConfigEntity> apiConfigEntityList);

    PageBean<ApiConfigEntity> apiConfigEntityToPageBean(PageBean<ApiConfigVO> pageBean);


    PageBean<ApiConfigVO> pageEntityToPageVo(PageBean<ApiConfigEntity> pageBean);


    ApiConfigVO convertEntityToVo(ApiConfigEntity apiConfigEntity);


}
