package com.siact.energy.cal.common.pojo.dto.energycal;
/**
 * @Package com.siact.energy.cal.common.pojo.dto.energycal
 * @description:
 * <AUTHOR>
 * @create 2024/10/8 11:09
 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "实时计算DTO")
public class CalFormula {
    @ApiModelProperty(value = "计算公式",position = 1,required = true)
    private String formula;
    @ApiModelProperty(value = "AVG:均值;MAX:最大值;MIN:最小值;LAST:最新值;FIRST:最早值;SUM:累加;DIFF:差值,如果是记录值,默认为空",position = 2,required = false)
    private String aggType;
    @ApiModelProperty(value = "目标属性编码",position = 3,required = true)
    private String targetProp;
}
