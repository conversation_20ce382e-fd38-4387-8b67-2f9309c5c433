// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbsynch.sybase;


import com.siact.energy.cal.dbsynch.mssql.SqlServerDatabaseSyncImpl;

import javax.sql.DataSource;

/**
 * Sybase数据库DML同步实现类
 *
 * <AUTHOR>
 */
public class SybaseDatabaseSyncImpl extends SqlServerDatabaseSyncImpl {

  public SybaseDatabaseSyncImpl(DataSource ds) {
    super(ds);
  }

}
