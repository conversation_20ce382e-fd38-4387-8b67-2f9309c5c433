package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.entity.IndicatorCal;
import com.siact.energy.cal.common.pojo.entity.IndicatorScreen;
import com.siact.energy.cal.common.pojo.vo.flow.CustomIndicatorVO;
import com.siact.energy.cal.common.util.utils.DBConnection;
import com.siact.energy.cal.common.util.utils.DBTools;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.CalculateContext;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.energycal.EnergyCalService;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-19
 * @Description: 自定义指标计算组件
 * @Version: 1.0
 */
@Component("CustomIndicatorNode")
@Slf4j
public class CustomIndicatorComponent extends NodeComponent {

    @Resource
    EnergyCalService energyCalService;
    @Resource
    BaseIndicatorCalculateComponent baseIndicatorCalculateComponent;
    @Resource
    IFlowRelationService flowRelationService;
    @Resource
    HikariDataSource hikariDataSource;
    @Resource
    FlowViewServiceImpl flowViewService;
    @Override
    public void process() {
        CalculateContext calculateContext = this.getContextBean("calculateContext");
        //查询规则calculateContext
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = calculateContext.getTaskId();
        String resultTable = calculateContext.getResultTable();

        log.info("自定义指标计算组件开始执行，tag:{}", nodeId);
        //获取计算组件的页面配置信息
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null) {
            throw new BizException("组件配置信息不存在");
        }
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        Object customIndicatorStr = collect.getOrDefault("customIndicator", null);
        JSONObject customIndicatorObj = JSONUtil.parseObj(customIndicatorStr);

        CustomIndicatorVO customIndicator = BeanUtil.toBean(customIndicatorObj, CustomIndicatorVO.class);
        //获取CustomCalDto中自定义指标，组装sql语句
        String sql = genneteSql(customIndicator, resultTable);
        //创建视图
        StringBuffer sb = new StringBuffer();
        String viewName = UUIDUtils.uuidViewName();
        sb.append("create view ").append(viewName).append(" as ").append(sql);
        //执行sql,返回结果存入resultMap中，并将指标存入视图中
        log.info("自定义指标sql语句为：{}", sb);
        DBTools.executeSql(sb.toString(), hikariDataSource);
        //将查询结果放入resultMap中
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = calculateContext.getResultMap();
        //查询视图
        putViewResultToMap(resultMap, viewName);
        flowViewService.insertFlowView(Integer.parseInt(flowId), viewName, nodeId, taskId);
        calculateContext.setResultMap(resultMap);
        log.info("自定义指标计算组件执行完毕");
    }

    private String genneteSql(CustomIndicatorVO customIndicator, String tableName) {
        String sql = "";
        //获取指标筛选条件
        List<IndicatorScreen> indicatorScreenList = customIndicator.getIndicatorScreenList();
        //获取窗口大小
        String windowSize = customIndicator.getWindowSize() + customIndicator.getTsUnit();
        StringBuffer sb = new StringBuffer();
        sb.append("select ");
        if (ObjectUtil.isNotEmpty(indicatorScreenList)) {
            for (IndicatorScreen indicatorScreen : indicatorScreenList) {
                String indicatorEnum = indicatorScreen.getIndicatorEnum();
                String dataCode = indicatorScreen.getDataCode();
                String alias = indicatorScreen.getAlias();
                String filterCondition = indicatorScreen.getFilterCondition();
                if (!StrUtil.isEmpty(filterCondition)) {
                    sb.append("ts,case when " + filterCondition + " then " + indicatorEnum + "(" + dataCode + ") else null end as " + alias + ",");
                } else {
                    if (StrUtil.isEmpty(indicatorEnum)) {
                        sb.append("ts," + dataCode + " as " + alias + ",");
                    } else {
                        sb.append("ts," + indicatorEnum + "(" + dataCode + ") as " + alias + ",");
                    }
                }
            }

        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(" from " + tableName + " group by " + getSQLFormatForKey(windowSize));
        String resultFilter = customIndicator.getResultFilter();
        if (!StrUtil.isEmpty(resultFilter)) {
            sb.append(" having " + resultFilter);
        }
        //添加指标计算
        List<IndicatorCal> indicatorDataList = customIndicator.getIndicatorDataList();
        if (ObjectUtil.isNotEmpty(indicatorDataList) && indicatorDataList.size() > 0) {
            StringBuffer buffer = new StringBuffer();
            buffer.append("select ");
            indicatorDataList.stream().filter(indicatorCal -> StrUtil.isNotEmpty(indicatorCal.getCalFormula()) && StrUtil.isNotEmpty(indicatorCal.getAliasNew()))
                    .map(indicatorCal -> buffer.append(indicatorCal.getCalFormula() + " as " + indicatorCal.getAliasNew() + ","));
            if (buffer.toString().contains("as")) {
                buffer.deleteCharAt(buffer.length() - 1);
                buffer.append("from ");
                buffer.append("(" + sb.toString() + ") temp");
                sql = buffer.toString();
            } else {
                sql = sb.toString();
            }
        } else {
            sql = sb.toString();
        }

        log.info("自定义指标sql:" + sql);
        return sql;
    }

    /**
     * 根据给定的窗口大小键获取对应的 SQL 格式化字符串。
     *
     * @param windowSize 窗口大小的键，如 "1分钟"、"15分钟" 等。
     * @return 对应的 SQL 格式化字符串。
     */
    private static String getSQLFormatForKey(String windowSize) {
        String sqlFormat;
        switch (windowSize) {
            case "1MIN":
                sqlFormat = "DATE_FORMAT(ts, '%Y-%m-%d %H:%i')";
                break;
            case "15MIN":
                sqlFormat = "DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(ts) / 900) * 900), '%Y-%m-%d %H:%i:00')";
                break;
            case "30MIN":
                sqlFormat = "DATE_FORMAT(FROM_UNIXTIME(FLOOR(UNIX_TIMESTAMP(ts) / 1800) * 1800), '%Y-%m-%d %H:%i:00')";
                break;
            case "60MIN":
                sqlFormat = "DATE_FORMAT(ts, '%Y-%m-%d %H:00:00')";
                break;
            case "1H":
                sqlFormat = "DATE_FORMAT(ts, '%Y-%m-%d %H:00:00')";
                break;
            case "1D":
                sqlFormat = "DATE(ts)";
                break;
            case "1M":
                sqlFormat = "DATE_FORMAT(ts, '%Y-%m')";
                break;
            default:
                sqlFormat = "ts";
        }
        return sqlFormat;
    }

    public void putViewResultToMap(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, String viewName) {

        String sql = "select * from " + viewName;
        Connection connection = null;
        ResultSet resultSet = null;
        try {
            connection = hikariDataSource.getConnection();
            resultSet = DBTools.executeQuerySql(connection, sql);
            while (resultSet.next()) {
                String ts = resultSet.getString("ts");
                for (int i = 2; i <= resultSet.getMetaData().getColumnCount(); i++) {
                    String columnName = resultSet.getMetaData().getColumnName(i);
                    BigDecimal value = resultSet.getBigDecimal(columnName);
                    if (ObjectUtil.isNotEmpty(value)) {
                        ConcurrentHashMap<String, BigDecimal> map = resultMap.get(columnName);
                        if (map == null) {
                            map = new ConcurrentHashMap<>();
                            map.put(ts, value);
                            resultMap.put(columnName, map);
                        }
                        map.put(ts, value);
                    }

                }
            }
        } catch (Exception e) {
            throw new BizException("查询自定义指标视图失败");
        } finally {
            DBConnection.close(connection);
        }
    }
}
