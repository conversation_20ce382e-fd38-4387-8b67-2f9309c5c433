package com.siact.energy.cal.tool.service.funLib;


import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;
import java.util.List;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibSelectQueryDTO;
import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;
import com.siact.energy.cal.tool.entity.funLib.FunLib;
import com.siact.energy.cal.common.pojo.vo.funLib.FunLibVO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibQueryDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibInsertDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibUpdateDTO;

/**
 * 常用函数库(FunLib)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-11 09:47:20
 */
public interface FunLibService extends IService<FunLib> {

    /**
     * 注册列表
     *
     * @param page           分页对象
     * @param funLibQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<FunLibVO> listPage(PageBean<FunLibVO> page, FunLibQueryDTO funLibQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    FunLibVO getVoById(Serializable id);

    /**
     * 新增数据
     *
     * @param funLibInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(FunLibInsertDTO funLibInsertDTO);

    /**
     * 修改数据
     *
     * @param funLibUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(FunLibUpdateDTO funLibUpdateDTO);

    /**
     * 函数库列表
     *
     * @param dto 参数对象
     * @return 查询结果
     */
    List<FunLibSelectOptionVO> selectList(FunLibSelectQueryDTO dto);

}

