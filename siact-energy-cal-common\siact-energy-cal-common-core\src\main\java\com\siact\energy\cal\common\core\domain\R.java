package com.siact.energy.cal.common.core.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * R
 *
 * <AUTHOR>
 * @since 2024-05-13 16:02:06
 */
@ApiModel("响应对象")
@Data
public class R<T> implements Serializable {

    private static final long serialVersionUID = -9000955239523866763L;

    /**
     * 响应状态码
     */
    @ApiModelProperty(value = "响应状态码", name = "code", example = "200", position = 1)
    private String code;

    /**
     * 响应消息
     */
    @ApiModelProperty(value = "响应消息", example = "操作成功", position = 2)
    private String msg;

    /**
     * 响应数据
     */
    @ApiModelProperty(value = "响应数据", position = 3)
    private T data;

    public static <T> R<T> OK() {
        return buildResult(RStatus.SUCCESS.getCode(), RStatus.SUCCESS.getMessage(), null);
    }

    public static <T> R<T> OK(T data) {
        return buildResult(RStatus.SUCCESS.getCode(), RStatus.SUCCESS.getMessage(), data);
    }

    public static <T> R<T> OK(T data, String msg) {
        return buildResult(RStatus.SUCCESS.getCode(), msg, data);
    }

    public static <T> R<T> ERROR() {
        return buildResult(RStatus.ERROR.getCode(), RStatus.ERROR.getMessage(), null);
    }

    public static <T> R<T> ERROR(String msg) {
        return buildResult(RStatus.ERROR.getCode(), msg, null);
    }

    public static <T> R<T> ERROR(T data) {
        return buildResult(RStatus.ERROR.getCode(), RStatus.ERROR.getMessage(), data);
    }

    public static <T> R<T> ERROR(T data, String msg) {
        return buildResult(RStatus.ERROR.getCode(), msg, data);
    }

    /**
     * 其他状态
     *
     * @param status:
     * @param data:
     * @return {@link R <T> }
     * <AUTHOR>
     * @date 2022年11月13日 16:00
     */
    public static <T> R<T> FILL(RStatus status, T data) {
        return buildResult(status.getCode(), status.getMessage(), data);
    }

    /**
     * 其他状态
     *
     * @param status:
     * @return {@link R<T> }
     * <AUTHOR>
     * @date 2022年11月13日 16:00
     */
    public static <T> R<T> FILL(RStatus status) {
        return buildResult(status.getCode(), status.getMessage(), null);
    }

    /**
     * 其他状态
     *
     * @param code:
     * @param msg:
     * @return {@link R <T> }
     * <AUTHOR>
     * @date 2022年11月13日 16:00
     */
    public static <T> R<T> FILL(String code, String msg) {
        return buildResult(code, msg, null);
    }

    /**
     * 其他状态
     *
     * @param code:
     * @param data:
     * @param msg:
     * @return {@link R <T> }
     * <AUTHOR>
     * @date 2022年11月13日 16:00
     */
    public static <T> R<T> FILL(String code, T data, String msg) {
        return buildResult(code, msg, data);
    }

    /**
     * 封装
     *
     * @param code:
     * @param msg:
     * @param data:
     * @return {@link R <T> }
     * <AUTHOR>
     * @date 2022年11月13日 16:00
     */
    private static <T> R<T> buildResult(String code, String msg, T data) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setMsg(msg);
        apiResult.setData(data);
        return apiResult;
    }

}