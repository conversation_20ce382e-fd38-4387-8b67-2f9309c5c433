// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbwriter.sybase;


import lombok.extern.slf4j.Slf4j;
import com.siact.energy.cal.dbwriter.mssql.SqlServerWriterImpl;

import javax.sql.DataSource;

/**
 * Sybase批量写入实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class SybaseWriterImpl extends SqlServerWriterImpl {

  public SybaseWriterImpl(DataSource dataSource) {
    super(dataSource);
  }

  @Override
  protected String getDatabaseProductName() {
    return "Sybase";
  }

}
