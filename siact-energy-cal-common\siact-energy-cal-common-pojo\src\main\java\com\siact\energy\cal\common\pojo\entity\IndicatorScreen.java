package com.siact.energy.cal.common.pojo.entity;/**
 * @Package com.siact.energy.cal.common.pojo.entity
 * @description: 指标筛选实体类
 * <AUTHOR>
 * @create 2024/9/3 14:16
 */

import com.siact.energy.cal.common.pojo.enums.IndicatorFuncEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName IndicatorScreen
 * @Description
 * <AUTHOR>
 * @Date 2024/9/3 14:16
 * @Version 1.0
 **/
@Data
@ApiModel("指标筛选")
public class IndicatorScreen {
    /**
     * 指标函数
     */
    @ApiModelProperty(value = "项目id", position = 1, required = true, example = "记录数/和值")
    private String indicatorEnum;
    /**
     * 属性编码
     */
    @ApiModelProperty(value = "属性编码", position = 2, required = true, example = "设备属性长码")
    private String dataCode;
    /**
     * 别名
     */
    @ApiModelProperty(value = "别名", position = 3, required = true, example = "别名")
    private String alias;
    /**
     * 过滤条件
     */
    @ApiModelProperty(value = "过滤条件", position = 4, required = true)
    private String filterCondition;
}
