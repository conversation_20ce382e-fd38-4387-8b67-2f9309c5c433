package com.siact.energy.cal.server.common.flow.context;

import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.yomahub.liteflow.context.ContextBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 上下文
 * @Version: 1.0
 */
@ContextBean("resultContext")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ResultContext {

    private String name;

    @ApiModelProperty("sql")
    private String sql;

    private String fileName;

    @ApiModelProperty("结果表id")
    private String resultTable;

    private Map<String, Object> nodeMap;

    @ApiModelProperty("公式列表")
    private List<String> formulaList;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("数据源id")
    private String dataSourceId;

    @ApiModelProperty("api接口id")
    private Long apiId;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty(value = "参数列表")
    private List<ParamDto> paramList;

}
