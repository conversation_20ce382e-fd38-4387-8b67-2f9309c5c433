package com.siact.energy.cal.core.database.impl;

import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.core.database.AbstractDatabase;
import com.siact.energy.cal.core.database.IDatabaseInterface;
import com.siact.energy.cal.core.model.ColumnDescription;
import com.siact.energy.cal.core.model.TableDescription;

import java.sql.*;
import java.util.*;

/**
 * <AUTHOR>
 * @Package com.siact.dwenergy.datadbswitch.core.database.impl
 * @description:
 * @create 2023/9/4 11:51
 */
public class DatabaseTdengineImpl extends AbstractDatabase implements IDatabaseInterface {
    private static final String SHOW_CREATE_TABLE_SQL = "SHOW CREATE STABLE `%s`.`%s`";
/*    private static final String SHOW_CREATE_VIEW_SQL =
            "SELECT \"DEFINITION\" FROM \"V_SYS_VIEWS\" WHERE \"SCHEMANAME\"= ? AND \"VIEWNAME\"= ?";*/

    public DatabaseTdengineImpl() {
        super("com.taosdata.jdbc.rs.RestfulDriver");
    }

    public DatabaseTdengineImpl(String driverClassName) {
        super(driverClassName);
    }

    @Override
    protected String getDefaultValueSql(String schemaName, String tableName) {
        return null;
    }

    @Override
    protected String getTableFieldsQuerySQL(String schemaName, String tableName) {
         return String.format("DESCRIBE %s.%s",schemaName,tableName);

    }

    @Override
    protected String getTestQuerySQL(String sql) {
        return String.format(sql, sql.replace(";", ""));
    }

    @Override
    public ProductTypeEnum getDatabaseType() {
        return ProductTypeEnum.TDENGINE;
    }

    @Override
    public String getTableDDL(Connection connection, String schemaName, String tableName) {
        return null;
    }

    @Override
    public String getViewDDL(Connection connection, String schemaName, String tableName) {
        return null;
    }

    @Override
    public List<ColumnDescription> querySelectSqlColumnMeta(Connection connection, String sql) {
        return this.getSelectSqlColumnMeta(connection, sql);
    }

    protected List<ColumnDescription> getSelectSqlColumnMeta(Connection connection, String querySQL) {
        List<ColumnDescription> ret = new ArrayList<>();
        try (Statement st = connection.createStatement()) {

            try (ResultSet rs = st.executeQuery(querySQL)) {
                while (rs.next()) {
                    String columnName = rs.getString("Field");
                    String dataType = rs.getString("Type");
                    int length = rs.getInt("Length");
                    String note = rs.getString("Note");
                    ColumnDescription cd = new ColumnDescription();
                    cd.setFieldName(columnName);
                    cd.setLabelName(note);
                    cd.setFieldTypeName(dataType);
                    cd.setFiledTypeClassName(dataType);
                    cd.setDisplaySize(length);
                    cd.setPrecisionSize(0);
                    cd.setScaleSize(0);
                    cd.setAutoIncrement(false);
                    cd.setNullable(0 != ResultSetMetaData.columnNoNulls);

                    boolean signed = false;
                    try {
                        signed = false;
                    } catch (Exception ignored) {
                        // This JDBC Driver doesn't support the isSigned method
                        // nothing more we can do here by catch the exception.
                    }
                    cd.setSigned(signed);
                    cd.setDbType(getDatabaseType());
                    ret.add(cd);
                }
                return ret;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<String> queryTablePrimaryKeys(Connection connection, String schemaName,
                                              String tableName) {
        return Collections.emptyList();
    }

    @Override
    public List<TableDescription> queryTableList(Connection connection, String schemaName) {
        List<TableDescription> ret = new ArrayList<>();
        String sql = String.format("show stables", schemaName);
        try (PreparedStatement ps = connection.prepareStatement(sql);
             ResultSet rs = ps.executeQuery();) {
            while (rs.next()) {
                TableDescription td = new TableDescription();
                td.setSchemaName(schemaName);
                td.setTableName(rs.getString("name"));
			/*	td.setRemarks(rs.getString("TABLE_COMMENT"));
				String tableType = rs.getString("TABLE_TYPE");
				if (tableType.equalsIgnoreCase("VIEW")) {
					td.setTableType("VIEW");
				} else {
					td.setTableType("TABLE");
				}*/

                ret.add(td);
            }

            return ret;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<ColumnDescription> queryTableColumnMeta(Connection connection, String schemaName,
                                                        String tableName) {
        String querySQL = this.getTableFieldsQuerySQL(schemaName, tableName);
        return getSelectSqlColumnMeta(connection, querySQL);
    }
    @Override
    public List<String> getTableColumnCommentDefinition(TableDescription td,
                                                        List<ColumnDescription> cds) {
        return Collections.emptyList();
    }

}
