// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.sql.service.impl;

import com.siact.energy.cal.common.type.ProductTypeEnum;
import com.siact.energy.cal.sql.ddl.AbstractDatabaseDialect;
import com.siact.energy.cal.sql.ddl.AbstractSqlDdlOperator;
import com.siact.energy.cal.sql.ddl.pojo.TableDefinition;
import com.siact.energy.cal.sql.ddl.sql.DdlSqlAlterTable;
import com.siact.energy.cal.sql.ddl.sql.DdlSqlCreateTable;
import com.siact.energy.cal.sql.ddl.sql.DdlSqlDropTable;
import com.siact.energy.cal.sql.ddl.sql.DdlSqlTruncateTable;
import com.siact.energy.cal.sql.ddl.sql.impl.MySqlDialectImpl;
import com.siact.energy.cal.sql.ddl.sql.impl.OracleDialectImpl;
import com.siact.energy.cal.sql.ddl.sql.impl.PostgresDialectImpl;
import com.siact.energy.cal.sql.service.ISqlGeneratorService;

import java.util.HashMap;
import java.util.Map;

/**
 * 拼接生成SQL实现类
 *
 * <AUTHOR>
 *
 */
public class MyselfSqlGeneratorServiceImpl implements ISqlGeneratorService {

	private static final Map<ProductTypeEnum, String> DATABASE_MAPPER = new HashMap<ProductTypeEnum, String>();

	static {
		DATABASE_MAPPER.put(ProductTypeEnum.MYSQL, MySqlDialectImpl.class.getName());
		DATABASE_MAPPER.put(ProductTypeEnum.ORACLE, OracleDialectImpl.class.getName());
		DATABASE_MAPPER.put(ProductTypeEnum.POSTGRESQL, PostgresDialectImpl.class.getName());

	}

	public static AbstractDatabaseDialect getDatabaseInstance(ProductTypeEnum type) {
		if (DATABASE_MAPPER.containsKey(type)) {
			String className = DATABASE_MAPPER.get(type);
			try {
				return (AbstractDatabaseDialect) Class.forName(className).newInstance();
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}

		throw new RuntimeException(String.format("Unkown database type (%s)", type.name()));
	}

	@Override
	public String createTable(String dbType, TableDefinition t) {
		ProductTypeEnum type = ProductTypeEnum.valueOf(dbType.toUpperCase());
		AbstractDatabaseDialect dialect = getDatabaseInstance(type);
		AbstractSqlDdlOperator operator = new DdlSqlCreateTable(t);
		return operator.toSqlString(dialect);
	}

	@Override
	public String alterTable(String dbType, String handle, TableDefinition t){
		ProductTypeEnum type = ProductTypeEnum.valueOf(dbType.toUpperCase());
		AbstractDatabaseDialect dialect = getDatabaseInstance(type);
		AbstractSqlDdlOperator operator = new DdlSqlAlterTable(t,handle);
		return operator.toSqlString(dialect);
	}

	@Override
	public String dropTable(String dbType, TableDefinition t) {
		ProductTypeEnum type = ProductTypeEnum.valueOf(dbType.toUpperCase());
		AbstractDatabaseDialect dialect = getDatabaseInstance(type);
		AbstractSqlDdlOperator operator = new DdlSqlDropTable(t);
		return operator.toSqlString(dialect);
	}

	@Override
	public String truncateTable(String dbType, TableDefinition t) {
		ProductTypeEnum type = ProductTypeEnum.valueOf(dbType.toUpperCase());
		AbstractDatabaseDialect dialect = getDatabaseInstance(type);
		AbstractSqlDdlOperator operator = new DdlSqlTruncateTable(t);
		return operator.toSqlString(dialect);
	}

}
