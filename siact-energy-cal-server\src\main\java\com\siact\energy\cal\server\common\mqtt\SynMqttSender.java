package com.siact.energy.cal.server.common.mqtt;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class SynMqttSender {
    @Autowired
    IMqttSender iMqttSender;
    public DefaultFuture sendMessage(String topic, JSONObject msg, String id)  {
        iMqttSender.sendToMqtt(topic,1, JSON.toJSONString(msg));
        log.info("消息topic:{},消息id：{}, 消息:{}", topic, id, msg);
        //设置超时间为20s
        return new DefaultFuture(id,20);
    }
}
