package com.siact.energy.cal.tool.task;

import com.siact.energy.cal.tool.service.dataProject.DataProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * UpdateProjectByDigitalTwinTask
 *
 * <AUTHOR>
 * @since 2024-05-14 17:20:31
 */
@Slf4j
@RefreshScope
@EnableScheduling
@Component
public class UpdateProjectByDigitalTwinTask implements ApplicationListener<RefreshScopeRefreshedEvent> {

    @Autowired
    private DataProjectService dataProjectService;

    /**
     * 任务开关
     */
    @Value("${update-project.task.enable:true}")
    private boolean taskEnable;

    @Scheduled(cron = "${update-project.task.cron:0 0/5 * * * ?}")
    public void exec() {
        log.debug("定时更新数字孪生项目开始==>");

        if(!taskEnable) {
            return;
        }

        int result = dataProjectService.updateProjectByDigitalTwin();

        log.debug("<==定时更新数字孪生项目结束, result: {}", result);
    }

    @Override
    public void onApplicationEvent(RefreshScopeRefreshedEvent event) {

    }
}
