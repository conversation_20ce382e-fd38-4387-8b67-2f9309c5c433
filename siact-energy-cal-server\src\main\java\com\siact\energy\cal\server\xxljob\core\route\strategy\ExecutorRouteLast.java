package com.siact.energy.cal.server.xxljob.core.route.strategy;

import com.siact.energy.cal.server.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.server.xxljob.biz.model.TriggerParam;
import com.siact.energy.cal.server.xxljob.core.route.ExecutorRouter;


import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/3/10.
 */
public class ExecutorRouteLast extends ExecutorRouter {

    @Override
    public ReturnT<String> route(TriggerParam triggerParam, List<String> addressList) {
        return new ReturnT<String>(addressList.get(addressList.size()-1));
    }

}
