package com.siact.energy.cal.server.common.flow.node;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.flow.CommonNodeDto;
import com.siact.energy.cal.common.pojo.dto.flow.DataSourceDto;
import com.siact.energy.cal.common.util.utils.MapToDbUtils;
import com.siact.energy.cal.common.util.utils.UUIDUtils;
import com.siact.energy.cal.server.common.flow.context.ResultContext;
import com.siact.energy.cal.server.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.server.service.flow.IFlowRelationService;
import com.siact.energy.cal.server.service.flow.impl.FlowViewServiceImpl;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-09
 * @Description: 数据源组件
 * @Version: 1.0
 */
@Component("DataSourceMQTTNode")
@Slf4j
public class DataSourceMQTTComponent extends NodeComponent {

    @Resource
    IFlowRelationService flowRelationService;

    @Resource
    FlowViewServiceImpl flowViewService;

    @Override
    public void process() {
        //获取上下文
        ResultContext resultContext = this.getContextBean("resultContext");
        String flowId = this.getChainId();
        String nodeId = this.getTag();
        String taskId = resultContext.getTaskId();
        SiComRelationEntity siComRelationEntity = flowRelationService.getSiComRelationEntity(flowId, nodeId);
        if (siComRelationEntity == null){
            throw new BizException("组件配置查询失败");
        }
        //获取数据源id
        List<CommonNodeDto> list = JSONUtil.toList(siComRelationEntity.getText(), CommonNodeDto.class);
        Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
        DataSourceDto mqttCon = BeanUtil.toBean(collect, DataSourceDto.class);
        //创建表
        String tableId = UUIDUtils.uuidStdTableName();
        mqtt(mqttCon, tableId);
        try {
            Thread.sleep(10000); // 等待5秒钟
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        resultContext.setResultTable(tableId);
        flowViewService.insertFlowTable(Integer.parseInt(flowId), tableId, nodeId, taskId);
    }

    private void mqtt(DataSourceDto mqttCon, String tableId){
        String broker = mqttCon.getBroker(); // MQTT代理的地址
        int random = RandomUtil.randomInt(10, 10000);;
        String clientId = "mqtt_java"+random; // 客户端ID
        MemoryPersistence persistence = new MemoryPersistence(); // 设置持久化
        try {
            MqttClient sampleClient = new MqttClient(broker, clientId, persistence);
            MqttConnectOptions connOpts = new MqttConnectOptions(); // 设置连接选项
            connOpts.setCleanSession(true); // 设置会话清除标志
            connOpts.setUserName(mqttCon.getUserName());
            connOpts.setPassword(mqttCon.getPassword().toCharArray());
            sampleClient.connect(connOpts); // 连接到MQTT代理

            // 订阅MQTT FX的Topic
            String topic = mqttCon.getTopic(); // 订阅的主题
            int qos = 1;
            sampleClient.subscribe(topic, qos);
            log.info("Subscribed to topic: {} ", topic);

            // 接收来自MQTT FX的消息
            sampleClient.setCallback(new MqttCallback() {
                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    log.info("Received message: {}",  new String(message.getPayload()));
                    String msg = new String(message.getPayload());
                    JSONObject jsonObject = JSONUtil.parseObj(msg);
                    Set<String> keys = jsonObject.keySet();
                    MapToDbUtils.createTaleByFields(tableId, keys);
                    MapToDbUtils.insertByMap(tableId, jsonObject);
                }

                @Override
                public void connectionLost(Throwable cause) {
                    log.info("Connection lost:{} ", cause.getMessage());
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {}
            });

            // 断开连接
            //sampleClient.disconnect();

        } catch (MqttException me) {
            log.info("reason：{}", me.getReasonCode());
            log.info("msg " + me.getMessage());
            log.info("loc " + me.getLocalizedMessage());
            log.info("cause " + me.getCause());
            log.info("excep " + me);
        } catch (Exception e) {
            log.error("Exception: {}", e.getMessage());
            throw new BizException("MQTT连接失败");
        }
    }

}
