package com.siact.energy.cal.tool.service.flow;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siact.energy.cal.tool.entity.flow.FlowViewEntity;

import java.util.List;
import java.util.Map;

public interface IFlowViewService extends IService<FlowViewEntity> {
    /**
     * 插入独享
     * @param flowId
     * @param viewName
     * @return
     */
    int insertFlowView(int flowId, String viewName, String comId, String taskId);

    int insertFlowTable(int flowId, String viewName, String comId, String taskId);

    /**
     * 查询组件流对应的所有视图
     * @param flowId
     * @return
     */
    List<String> selectViewNames(int flowId);

    List<String> selectViewNames();


    List<String> selectTableNames();

    /**
     * 删除计算过程中产生的视图
     * @param ViewNames
     */
    void deleteViews(List<String> ViewNames);

    void deleteTables(List<String> ViewNames);

    void deleteViewNameByFlowId(int flowId);

    void deleteViewName(List<String> viewNames);

    void deleteTableName(List<String> tableNames);

    List<Map<String, Object>> executeSql(String sql);

    Integer executeSqlCount(String sql);

    List<String> getAllFields(String tableName);
}
