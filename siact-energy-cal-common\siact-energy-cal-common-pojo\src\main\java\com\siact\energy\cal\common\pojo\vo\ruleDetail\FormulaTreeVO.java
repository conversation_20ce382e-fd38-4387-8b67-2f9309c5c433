package com.siact.energy.cal.common.pojo.vo.ruleDetail;/**
 * @Package com.siact.energy.cal.common.pojo.vo.ruleDetail
 * @description: 计算公式层级树VO
 * <AUTHOR>
 * @create 2024/12/18 15:25
 */

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * @ClassName FormulaTreeVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/18 15:25
 * @Version 1.0
 **/
@Data
@ApiOperation("计算公式层级树VO")
public class FormulaTreeVO {
    /**
     * 属性编码，模型编码
     */
    @ApiModelProperty(value = "属性编码",required = true, position = 1)
    private String devProperty;

    /**
     * 所属规则集id
     */
    @ApiModelProperty(value = "所属规则集id,默认为空",required = false, position = 2)
    private Long ruleColId;
}
