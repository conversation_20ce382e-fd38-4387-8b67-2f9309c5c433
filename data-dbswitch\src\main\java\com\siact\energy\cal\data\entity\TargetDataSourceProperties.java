// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.data.entity;

import lombok.Data;

import java.util.concurrent.TimeUnit;

@Data
public class TargetDataSourceProperties {

	private String url;
	private String driverClassName;
	private String username;
	private String password;
	private Long connectionTimeout = TimeUnit.SECONDS.toMillis(60);
	private Long maxLifeTime = TimeUnit.MINUTES.toMillis(60);

	private String targetSchema = "";
	private Boolean targetDrop = Boolean.TRUE;
	private Boolean syncExist = Boolean.TRUE;
	private Boolean onlyCreate = Boolean.FALSE;
	/**
	 * 是否同步索引信息，只有targetDrop为TRUE时生效
	 */
	private Boolean indexCreate = Boolean.FALSE;
	/**
	 * 表明前缀
	 */
	private String tablePrefix;
	/**
	 * 是否自动转为小写
	 */
	private Boolean lowercase = Boolean.FALSE;
	private Boolean uppercase = Boolean.FALSE;
	private Boolean createTableAutoIncrement = Boolean.FALSE;
	private Boolean writerEngineInsert = Boolean.FALSE;
	private Boolean changeDataSync = Boolean.FALSE;
}
