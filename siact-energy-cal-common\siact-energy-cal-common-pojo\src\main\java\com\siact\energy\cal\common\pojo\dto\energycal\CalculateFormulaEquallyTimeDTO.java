package com.siact.energy.cal.common.pojo.dto.energycal;/**
 * @Package com.siact.energy.cal.common.pojo.dto.energycal
 * @description:
 * <AUTHOR>
 * @create 2024/10/8 10:15
 */

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CalculateFormulaDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/10/8 10:15
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "实时计算公式等时间间隔DTO")
public class CalculateFormulaEquallyTimeDTO {
    @ApiModelProperty(value = "列表",position = 1,required = true)
    private List<CalFormula> calFormulaList;
    @ApiModelProperty(value = "开始时间",position = 2,required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间",position = 3,required = true)
    private String endTime;
    @ApiModelProperty(value = "步长",position = 4,required = false)
    public Integer interval;
    @ApiModelProperty(value = "步长单位:(Y:年;M:月;D:日;H:小时;MIN:分),示例值(D)",position = 5,required = false)
    public String tsUnit;
}
