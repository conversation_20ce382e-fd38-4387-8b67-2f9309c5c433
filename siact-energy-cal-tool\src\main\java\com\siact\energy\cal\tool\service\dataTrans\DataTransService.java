package com.siact.energy.cal.tool.service.dataTrans;


import com.baomidou.mybatisplus.extension.service.IService;

import java.io.Serializable;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransSubscribeDTO;
import com.siact.energy.cal.tool.entity.dataTrans.DataTrans;
import com.siact.energy.cal.common.pojo.vo.dataTrans.DataTransVO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransQueryDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransUpdateDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.constraints.NotBlank;

/**
 * 数据传输表（mqtt）(DataTrans)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-15 11:25:35
 */
public interface DataTransService extends IService<DataTrans> {

    /**
     * 注册列表
     *
     * @param page              分页对象
     * @param dataTransQueryDTO 查询实体
     * @return 查询结果
     */
    PageBean<DataTransVO> listPage(PageBean<DataTransVO> page, DataTransQueryDTO dataTransQueryDTO);

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 返回数据
     */
    DataTransVO getVoById(Serializable id);

    /**
     * 新增数据
     *
     * @param dataTransInsertDTO 实体对象
     * @return 新增结果
     */
    Boolean save(DataTransInsertDTO dataTransInsertDTO);

    /**
     * 修改数据
     *
     * @param dataTransUpdateDTO 实体对象
     * @return 修改结果
     */
    Boolean updateVoById(DataTransUpdateDTO dataTransUpdateDTO);

    /**
     * 通过项目ID获取实时数据消费配置
     *
     * @param projectId 主键
     * @return 返回数据
     */
    DataTransVO getByProjectId(Long projectId);

    /**
     * 新增或编辑信息
     *
     * @param dataTransUpdateDTO 实体对象
     * @return 新增或编辑结果
     */
    Boolean saveOrUpdate(DataTransUpdateDTO dataTransUpdateDTO);

    /**
     * 订阅主题
     *
     * @param dto 实体对象
     * @return 订阅结果
     */
    SseEmitter subscribe(DataTransSubscribeDTO dto);

    /**
     * 取消订阅主题
     *
     * @param subscribeId 订阅ID
     * @return 取消订阅结果
     */
    Boolean unsubscribe(@NotBlank String subscribeId);
}

