package com.siact.energy.cal.common.pojo.dto.energycal;

import lombok.Data;

import java.util.Objects;
@Data
public class DevPropertyKey {
        String devCode;
        String devProperty;

        public DevPropertyKey(String devCode, String devProperty) {
            this.devCode = devCode;
            this.devProperty = devProperty;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            DevPropertyKey that = (DevPropertyKey) o;
            return Objects.equals(devCode, that.devCode) && Objects.equals(devProperty, that.devProperty);
        }

        @Override
        public int hashCode() {
            return Objects.hash(devCode, devProperty);
        }
    }