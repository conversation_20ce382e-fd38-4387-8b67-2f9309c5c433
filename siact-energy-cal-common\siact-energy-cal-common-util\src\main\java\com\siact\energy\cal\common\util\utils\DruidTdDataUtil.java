package com.siact.energy.cal.common.util.utils;


import com.siact.energy.cal.common.core.exception.BizException;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;

/**
 * @program:
 * @description:
 * @author: <PERSON>
 * @create: 2021-07-12 15:50
 **/

@Slf4j
public class DruidTdDataUtil {

    private static final Map<String, HikariDataSource> PROJECT_DATA_SOURCES = new ConcurrentHashMap<>();

    // 调整连接池参数
    private static final int MAX_POOL_SIZE = 50;  // 增加最大连接数
    private static final int MIN_IDLE = 10;
    private static final long MAX_LIFETIME = 1800000; // 30分钟
    private static final long CONNECTION_TIMEOUT = 60000; // 增加到60秒
    private static final long IDLE_TIMEOUT = 600000; // 10分钟
    private static final long VALIDATION_TIMEOUT = 5000; // 5秒
    private static final long SOCKET_TIMEOUT = 300000; // 5分钟
    private static final long QUERY_TIMEOUT = 300000; // 5分钟

    public static synchronized void initializeDataSource(String projectCode, String url,
                                                         String username, String password) {
        if (PROJECT_DATA_SOURCES.containsKey(projectCode)) {
            return;
        }

        try {
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl(url);
            config.setUsername(username);
            config.setPassword(password);

            // 连接池基础配置
            config.setMaximumPoolSize(MAX_POOL_SIZE);
            config.setMinimumIdle(MIN_IDLE);
            config.setMaxLifetime(MAX_LIFETIME);
            config.setConnectionTimeout(CONNECTION_TIMEOUT);
            config.setIdleTimeout(IDLE_TIMEOUT);
            config.setValidationTimeout(VALIDATION_TIMEOUT);

            // 关键配置：设置连接测试查询
            config.setConnectionTestQuery("select server_status()");

            // 性能优化配置
            config.setAutoCommit(true);
            config.addDataSourceProperty("cachePrepStmts", "true");
            config.addDataSourceProperty("prepStmtCacheSize", "250");
            config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

            // 设置连接池名称，便于监控
            config.setPoolName("TDEngine-Pool-" + projectCode);

            HikariDataSource dataSource = new HikariDataSource(config);
            PROJECT_DATA_SOURCES.put(projectCode, dataSource);
            log.info("Initialized TDEngine data source for project: {}", projectCode);
        } catch (Exception e) {
            log.error("Failed to initialize TDEngine data source for project: " + projectCode, e);
            throw new BizException("数据源初始化失败: " + e.getMessage());
        }
    }

    public static Connection getConnection(String projectCode) {
        HikariDataSource dataSource = PROJECT_DATA_SOURCES.get(projectCode);
        if (dataSource == null) {
            throw new BizException("Project " + projectCode + " data source not initialized");
        }

        try {
            Connection conn = dataSource.getConnection();
            // 设置查询超时
            conn.setNetworkTimeout(Executors.newSingleThreadExecutor(), (int) QUERY_TIMEOUT);
            return conn;
        } catch (SQLException e) {
            log.error("Failed to get connection for project: " + projectCode, e);
            throw new BizException("获取数据库连接失败: " + e.getMessage());
        }
    }

    /**
     * 关闭指定项目的数据源
     * @param projectCode 项目编码
     */
    public static synchronized void closeDataSource(String projectCode) {
        DataSource dataSource = PROJECT_DATA_SOURCES.remove(projectCode);
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            log.info("Closed data source for project: {}", projectCode);
        }
    }

    /**
     * 关闭所有数据源
     */
    public static synchronized void closeAllDataSources() {
        PROJECT_DATA_SOURCES.forEach((projectCode, dataSource) -> {
            if (dataSource instanceof HikariDataSource) {
                ((HikariDataSource) dataSource).close();
                log.info("Closed data source for project: {}", projectCode);
            }
        });
        PROJECT_DATA_SOURCES.clear();
    }

    /**
     * 安全关闭数据库资源
     */
    public static void closeResources(AutoCloseable... resources) {
        for (AutoCloseable resource : resources) {
            if (resource != null) {
                try {
                    resource.close();
                } catch (Exception e) {
                    log.error("Error closing resource", e);
                }
            }
        }
    }
}
