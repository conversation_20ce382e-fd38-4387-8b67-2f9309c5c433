package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: Zhang<PERSON>anWu
 * @CreateTime: 2024-09-03
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel("开始定时组件参数Dto")
public class StartCronNodeDataCodeDto {

  /*  @ApiModelProperty(value = "组件名称", example = "开始组件(指标列表)", position = 1)
    private String name;*/

    @ApiModelProperty(value = "属性编码列表",position = 1,required = true)
    public List<String> indicatorList;

    @ApiModelProperty(value = "步长",position = 4,required = true)
    public Integer interval;

}
