<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.ruleCol.RuleColDao">

    <resultMap type="com.siact.energy.cal.server.entity.ruleCol.RuleCol" id="RuleColMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ruleColName" column="rule_col_name" jdbcType="VARCHAR"/>
        <result property="ruleColDes" column="rule_col_des" jdbcType="VARCHAR"/>
        <result property="attrList" column="attr_list" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="activeState" column="active_state" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into rule_col(rule_col_name, rule_col_des,attr_list, active_state, creator, create_time, updater, update_time,
        deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.ruleColName}, #{entity.ruleColDes},#{entity.attrList}, #{entity.activeState}, #{entity.creator},
            #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 - 支持多数据库 -->
    <insert id="insertOrUpdateBatch">
        <choose>
            <!-- MySQL数据库 -->
            <when test="_databaseId == 'mysql'">
                insert into rule_col(rule_col_name, rule_col_des, attr_list,active_state, creator, create_time, updater, update_time,
                deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleColName}, #{entity.ruleColDes},#{entity.attrList}, #{entity.activeState}, #{entity.creator},
                    #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on duplicate key update
                rule_col_name = values(rule_col_name), rule_col_des = values(rule_col_des), attr_list = values(attr_list), active_state = values(active_state),
                creator = values(creator), create_time = values(create_time), updater = values(updater), update_time = values(update_time), deleted = values(deleted)
            </when>
            <!-- PostgreSQL数据库（包括南大通用） -->
            <when test="_databaseId == 'postgresql'">
                insert into rule_col(rule_col_name, rule_col_des, attr_list,active_state, creator, create_time, updater, update_time,
                deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleColName}, #{entity.ruleColDes},#{entity.attrList}, #{entity.activeState}, #{entity.creator},
                    #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on conflict (id) do update set
                rule_col_name = excluded.rule_col_name, rule_col_des = excluded.rule_col_des, attr_list = excluded.attr_list, active_state = excluded.active_state,
                creator = excluded.creator, create_time = excluded.create_time, updater = excluded.updater, update_time = excluded.update_time, deleted = excluded.deleted
            </when>
            <!-- 默认使用MySQL语法 -->
            <otherwise>
                insert into rule_col(rule_col_name, rule_col_des, attr_list,active_state, creator, create_time, updater, update_time,
                deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleColName}, #{entity.ruleColDes},#{entity.attrList}, #{entity.activeState}, #{entity.creator},
                    #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on duplicate key update
                rule_col_name = values(rule_col_name), rule_col_des = values(rule_col_des), attr_list = values(attr_list), active_state = values(active_state),
                creator = values(creator), create_time = values(create_time), updater = values(updater), update_time = values(update_time), deleted = values(deleted)
            </otherwise>
        </choose>
    </insert>
</mapper>

