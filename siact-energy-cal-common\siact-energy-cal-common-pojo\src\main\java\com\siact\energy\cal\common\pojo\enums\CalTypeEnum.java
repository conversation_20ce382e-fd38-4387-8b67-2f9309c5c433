package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.List;

/**
 * CalTypeEnum
 *
 * <AUTHOR>
 * @since 2024-05-21 10:12:27
 */
public enum CalTypeEnum {


    /**
     * 基础指标
     */
    BASE(1, "基础指标"),

    /**
     * 聚合指标
     */
    AGGREGATE(2, "聚合指标"),
    /**
     * 衍生指标
     */
    DERIVED(3, "衍生指标");


    public static final String TIPS = "计算类型，1:基础指标,2:聚合指标,3:衍生指标";

    private final Integer value;

    private final String description;

    CalTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static String getDescriptionByValue(Integer calType){
        for(CalTypeEnum calTypeEnum : values()){
            if(calTypeEnum.getValue().equals(calType)){
                return calTypeEnum.getDescription();
            }
        }
        return null;
    }

    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(CalTypeEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDescription()).value(enumObj.getValue()).build());
        }

        return list;
    }

}
