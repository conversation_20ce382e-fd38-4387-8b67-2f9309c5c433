package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-03
 * @Description: 组件流执行参数
 * @Version: 1.0
 */
@Data
@ApiModel("组件流执行参数")
public class ParamDto {
    @ApiModelProperty(value = "参数key",position = 1)
    String paramKey;
    @ApiModelProperty(value = "参数value",position = 2)
    Object paramValue;
}
