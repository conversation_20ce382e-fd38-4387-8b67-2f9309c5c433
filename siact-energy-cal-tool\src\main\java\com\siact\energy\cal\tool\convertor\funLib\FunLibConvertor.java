package com.siact.energy.cal.tool.convertor.funLib;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siact.energy.cal.common.pojo.vo.common.FunLibSelectOptionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.siact.energy.cal.tool.entity.funLib.FunLib;
import com.siact.energy.cal.common.core.convertor.AbstractConvertor;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.vo.funLib.FunLibVO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibQueryDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibInsertDTO;
import com.siact.energy.cal.common.pojo.dto.funLib.FunLibUpdateDTO;

import java.util.List;

/**
 * 常用函数库(FunLib) Convertor
 *
 * <AUTHOR>
 * @since 2024-06-11 09:47:21
 */
@Mapper(builder = @org.mapstruct.Builder(disableBuilder = true))
public interface FunLibConvertor extends AbstractConvertor<FunLibVO, FunLib> {

    FunLibConvertor INSTANCE = Mappers.getMapper(FunLibConvertor.class);

    /**
     * 实体分页转VO分页
     *
     * @param entityPage 实体分页
     * @return VO 分页
     */
    PageBean<FunLibVO> entityPage2VoPageBean(Page<FunLib> entityPage);

    /**
     * VO分页转实体分页
     *
     * @param voPageBean 实体分页
     * @return VO分页
     */
    Page<FunLib> voPageBean2EntityPage(PageBean<FunLibVO> voPageBean);

    /**
     * 查询DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    FunLib queryDTO2Entity(FunLibQueryDTO dto);

    /**
     * 新增DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    FunLib insertDTO2Entity(FunLibInsertDTO dto);

    /**
     * 更新DTO转实体
     *
     * @param dto DTO
     * @return 实体
     */
    FunLib updateDTO2Entity(FunLibUpdateDTO dto);

    /**
     * 批量实体转选择对象
     *
     * @param entities 实体
     * @return 选择对象
     */
    List<FunLibSelectOptionVO> entity2SelectVO(List<FunLib> entities);

    /**
     * 实体转选择对象
     *
     * @param entity 实体
     * @return 选择对象
     */
    FunLibSelectOptionVO entity2SelectVO(FunLib entity);

}
