package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-29
 * @Description: 组件流定义
 * @Version: 1.0
 */
@Data
@TableName("si_component_flow")
@ApiModel("组件流")
public class ComponentFlowEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id", position = 1)
    private Integer id;
    @ApiModelProperty(value = "名称", position = 2)
    @TableField("flow_name")
    private String flowName;
    @ApiModelProperty(value = "组件流结构", position = 3)
    private String context;
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
    @ApiModelProperty(value = "描述", position = 4)
    @TableField("flow_dec")
    private String flowDec;
    @ApiModelProperty(value = "组件流状态:0:未运行,1:运行中,2:成功,3:失败", position = 4)
    private Integer status;
}
