package com.siact.energy.cal.tool.common.flow.context;

import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.yomahub.liteflow.context.ContextBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Zhangzanwu
 * @CreateTime: 2024-08-20
 * @Description: 上下文
 * @Version: 1.0
 */
@ContextBean("calculateContext")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CalculateContext {

    private String name;

    @ApiModelProperty("结果表id")
    private String resultTable;

    private Map<String, Object> nodeMap;

    @ApiModelProperty("api接口id")
    private Long apiId;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("属性编码列表")
    private List<String> dataCodeList;
    @ApiModelProperty("指标集")
    private Long indicatorsColId;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("时间间隔")
    private Integer interval;
    @ApiModelProperty("时间单位")
    private String tsUnit;

    @ApiModelProperty("项目计算类型属性列表")
    private Map<String, Map<String, List<String>>> projectCalTypeMap;

    @ApiModelProperty(value = "参数列表")
    private Map<String,String> paramList;

    @ApiModelProperty(value = "结果列表")
    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap;

}
