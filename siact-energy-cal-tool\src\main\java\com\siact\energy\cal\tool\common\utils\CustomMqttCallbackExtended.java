package com.siact.energy.cal.tool.common.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

/**
 * CustomMqttCallbackExtended
 *
 * <AUTHOR>
 * @since 2024-05-16 11:08:40
 */
@Slf4j
@RequiredArgsConstructor
public class CustomMqttCallbackExtended implements MqttCallbackExtended {

    /**
     * 唯一ID
     */
    private final String id;

    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        // 重连成功 重新订阅主题
        log.info("连接完成，reconnect: {}, serverURI: {}", reconnect, serverURI);
        if(reconnect) {
            try {
                MqttUtil.reSubscribe(id);
            } catch (MqttException e) {
                log.error("重新订阅失败，", e);
            }
        }
    }

    @Override
    public void connectionLost(Throwable cause) {
        // 连接丢失
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        // 使用了Listener订阅方式后，该处不会收到消息
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        // 分发完成
    }

}
