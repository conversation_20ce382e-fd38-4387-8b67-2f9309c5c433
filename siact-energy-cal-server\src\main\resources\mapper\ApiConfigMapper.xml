<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.flow.ApiConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siact.energy.cal.server.entity.flow.ApiConfigEntity">
        <id column="id" property="id" />
        <result column="api_name" property="apiName" />
        <result column="api_url" property="apiUrl" />
        <result column="request_type" property="requestType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_id" property="createId" />
        <result column="create_name" property="createName" />
        <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
        <result column="flow_id" property="flowId" />
        <result column="table_id" property="tableId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, api_name, api_url, request_type, creat_time, update_time, create_id, create_name, deleted, remark, flow_id, table_id
    </sql>

</mapper>
