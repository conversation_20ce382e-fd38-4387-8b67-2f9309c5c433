package com.siact.energy.cal.server.controller.dataSource;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.common.core.exception.BizException;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.dataSource.DataSourceTestDTO;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.Objects;

/**
 * 数据源表(DataSource)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-15 09:10:09
 */
@Api(tags = {"数据源表"})
@ApiSort(20)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/data/source")
public class DataSourceController {

    private final DataSourceService dataSourceService;

    public static final String TAOS_JDBC_FORMAT = "jdbc:TAOS-RS://%s:%s/%s";

    /**
     * 数据源测试
     *
     * @param dataSourceTestDTO 实体对象
     * @return 测试结果
     */
    @ApiOperation(value = "数据源测试")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSourceTestDTO", value = "实体对象", dataType = "数据源测试DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 60)
    @PostMapping("/dbTest")
    public R<Object> dbTest(@RequestBody @Validated DataSourceTestDTO dataSourceTestDTO) {
        try {
            if(Objects.nonNull(dataSourceTestDTO) && StringUtils.isBlank(dataSourceTestDTO.getJdbcUrl())) {
                dataSourceTestDTO.setJdbcUrl(String.format(TAOS_JDBC_FORMAT, dataSourceTestDTO.getDatabaseIp(),
                        dataSourceTestDTO.getDatabasePort(), dataSourceTestDTO.getDb()));
            }
            dataSourceService.dbTest(dataSourceTestDTO);
            return R.OK("数据源连接测试成功");
        } catch (BizException e) {
            // 捕获业务异常，返回4xx状态码而非5xx
            return R.ERROR(e.getRStatus().getCode(), e.getRStatus().getMessage());
        } catch (Exception e) {
            return R.ERROR( "数据源测试失败");
        }
    }
    /**
     * 数据源测试(使用ID)
     *
     * @param id 主键
     * @return 测试结果
     */
    @ApiOperation(value = "数据源测试(使用ID)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 70)
    @GetMapping("/dbTestById")
    public R<Object> dbTestById(@RequestParam Serializable id) {

        try {
            dataSourceService.dbTestById(id);
            return R.OK();
        } catch (BizException e) {
            // 捕获业务异常，返回4xx状态码而非5xx
            return R.ERROR(e.getRStatus().getCode(), e.getRStatus().getMessage());
        } catch (Exception e) {
            return R.ERROR( "数据源测试失败");
        }
    }

}

