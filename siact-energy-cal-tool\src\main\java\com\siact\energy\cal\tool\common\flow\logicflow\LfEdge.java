package com.siact.energy.cal.tool.common.flow.logicflow;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-28
 * @Description: 上下文
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LfEdge {
    private String id;
    private String type;
    private String sourceNodeId;
    private String targetNodeId;
    private LfPoint startPoint;
    private LfPoint endPoint;
    private List<LfPoint> pointsList;
    private Map<String,Object> properties;
    private TextEntity text;
    private String sourceAnchorId;
    private String targetAnchorId;

    @Data
    public static class TextEntity {
        private String value;
        private Integer x;
        private Integer y;
    }
}