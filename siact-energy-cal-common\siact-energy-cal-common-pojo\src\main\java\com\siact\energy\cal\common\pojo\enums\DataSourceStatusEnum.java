package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.List;

/**
 * DataSourceStatusEnum
 *
 * <AUTHOR>
 * @since 2024-05-20 20:20:29
 */
public enum DataSourceStatusEnum {

    /**
     * 激活
     */
    NORMAL(0, "正常"),

    /**
     * 未激活
     */
    INTERRUPT(1, "中断");

    private final Integer value;

    private final String description;

    DataSourceStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(DataSourceStatusEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDescription()).value(enumObj.getValue()).build());
        }

        return list;
    }

}
