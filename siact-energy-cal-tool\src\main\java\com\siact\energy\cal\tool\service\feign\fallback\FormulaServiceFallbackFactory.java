package com.siact.energy.cal.tool.service.feign.fallback;


import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.FormulaTreeDTO;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.RuleDetailViewDTO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.FormulaTreeVO;
import com.siact.energy.cal.tool.service.feign.FormulaServiceFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@Component
public class FormulaServiceFallbackFactory implements FallbackFactory<FormulaServiceFeignClient> {
    
    @Override
    public FormulaServiceFeignClient create(Throwable cause) {
        log.error("调用服务后端公式接口失败", cause);
        
        return new FormulaServiceFeignClient() {
            @Override
            public R<PageBean<RuleDetailViewDTO>> viewFormula(Long id,
                                                              PageBean<RuleDetailViewDTO> page) {
                String errorMsg = "无法连接到服务后端查看公式详情";
                if (cause != null) {
                    // 根据异常类型提供更具体的错误信息
                    if (cause instanceof feign.RetryableException) {
                        errorMsg += "，服务连接超时或不可达，请检查网络连接";
                    } else {
                        errorMsg += "，错误: " + cause.getMessage();
                    }
                }
                return R.ERROR(errorMsg);

            }

            @Override
            public R<FormulaTreeDTO> viewFormulaTree(FormulaTreeVO formulaTreeVO) {
                String errorMsg = "无法连接到服务后端查看公式树结构";
                if (cause != null) {
                    // 根据异常类型提供更具体的错误信息
                    if (cause instanceof feign.RetryableException) {
                        errorMsg += "，服务连接超时或不可达，请检查网络连接";
                    } else {
                        errorMsg += "，错误: " + cause.getMessage();
                    }
                }
                return R.ERROR(errorMsg);
            }
        };
    }
}