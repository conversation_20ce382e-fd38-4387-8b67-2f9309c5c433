package com.siact.energy.cal.common.pojo.validator;


import lombok.extern.slf4j.Slf4j;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * EnumValidator
 *
 * <AUTHOR>
 * @since 2024-03-12 13:17:59
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Constraint(validatedBy = EnumValidator.EnumValidatorHandler.class)
public @interface EnumValidator {

    /**
     * 枚举类
     *
     * @return 枚举类
     */
    Class<? extends Enum<?>> enumClass();

    /**
     * 存在性判断取值方法名称
     *
     * @return 方法名称
     */
    String existGetMethod() default "name";

    /***
     * 自定义校验方法名称
     *
     * @return 方法名称: 必须是public static boolean方法
     */
    String customValidationMethod() default "";

    /**
     * 错误提示语
     *
     * @return 提示语
     */
    String message() default "参数在枚举中不存在！";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 枚举校验处理类
     *
     * <AUTHOR>
     * @since 2023-02-14 10:50:53
     */
    @Slf4j
    class EnumValidatorHandler implements ConstraintValidator<EnumValidator, Object> {

        /**
         * 收集枚举对应值集合
         */
        private List<Object> values = new ArrayList<>();

        /**
         * 枚举class
         */
        private Class<? extends Enum> enumClass;

        /**
         * 枚举类名称
         */
        private String className;

        /**
         * 自定义校验方式参数类型
         */
        private Class<?> valueClass;

        /**
         * 自定义校验方法名称
         */
        private String customMethodName;

        /**
         * 是否有自定义校验器
         */
        private boolean isHasCustom;

        @Override
        public void initialize(EnumValidator enumValidator) {

            enumClass = enumValidator.enumClass();
            className = enumClass.getName();

            // 校验注解参数
            checkEnumValidatorParam(enumValidator);

        }

        /**
         * 校验注解参数
         *
         * @param enumValidator 注解参数
         */
        private void checkEnumValidatorParam(EnumValidator enumValidator) {

            String existGetMethodName = enumValidator.existGetMethod();
            String customValidationMethodName = enumValidator.customValidationMethod();
            // 校验existGetMethod、customValidationMethod是否同时为空
            if(isBlank(existGetMethodName) && isBlank(customValidationMethodName)) {
                throw new IllegalArgumentException(String.format("枚举校验器，枚举 [%s] existGetMethod 与 customValidationMethod 不能同时为空", className));
            }

            // 自定义校验规则存在，直接使用自定义校验
            if(isNotBlank(customValidationMethodName)) {
                customMethodName = customValidationMethodName;
                isHasCustom = Boolean.TRUE;
            } else {
                try {
                    // 存在性校验参数
                    Method existGetMethod = enumClass.getMethod(existGetMethodName);
                    Object[] objects = enumClass.getEnumConstants();
                    Object value;
                    for (Object obj : objects) {
                        value = existGetMethod.invoke(obj);
                        values.add(value);
                    }
                } catch (NoSuchMethodException e) {
                    throw new IllegalArgumentException(String.format("枚举校验器，枚举对象 [%s] 缺少名为 [%s] 的无参方法", className, existGetMethodName));
                } catch (InvocationTargetException | IllegalAccessException e) {
                    log.error("枚举校验器，初始化枚举 [{}] [existGetMethod: {}] [customValidationMethod: {}] 异常: {}", className, existGetMethodName, customValidationMethodName, e);
                    throw new IllegalArgumentException(String.format("枚举校验器，初始化枚举 [%s] [existGetMethod: %s] [customValidationMethod: %s] 失败", className, existGetMethodName, customValidationMethodName));
                }
            }
        }

        @Override
        public boolean isValid(Object value, ConstraintValidatorContext constraintValidatorContext) {

            if (Objects.isNull(value)) {
                return Boolean.TRUE;
            }

            valueClass = value.getClass();

            // 自定义校验器校验
            if (isHasCustom) {
                return customValidMethod(value);
            }

            return defaultValidMethod(value);
        }

        /**
         * 自定义校验方法
         *
         * @param value 待验证值
         * @return 校验结果
         */
        private boolean customValidMethod(Object value) {

            /**
             * 校验自定义参数
             */
            checkCustomMethod();

            try {
                Method customValidationMethod = enumClass.getMethod(customMethodName, valueClass);
                Boolean result = (Boolean)customValidationMethod.invoke(null, value);
                return Objects.isNull(result) ? Boolean.FALSE : result;
            } catch (NoSuchMethodException e) {
                throw new IllegalArgumentException(String.format("枚举校验器，枚举对象 [%s] 缺少名为 [%s] 参数类型为 [%s] 的自定义校验方法", className, customMethodName, valueClass));
            } catch ( IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                throw new IllegalArgumentException(String.format("枚举校验器，调用枚举 [%s] 的自定义校验方法 [%s](%s) 异常，[%s]", className, customMethodName, valueClass, e.getMessage()));
            }

        }

        /**
         * 检查自定义校验方法
         */
        private void checkCustomMethod() {

            Method customValidationMethod;
            // 检查自定义校验方法是否存在
            try {
                customValidationMethod = enumClass.getMethod(customMethodName, valueClass);
            } catch (NoSuchMethodException e) {
                throw new IllegalArgumentException(String.format("枚举校验器，枚举对象 [%s] 缺少名为 [%s](%s) 的自定义校验方法", className, customMethodName, valueClass));
            }

            // 检查自定义校验方法的返回值是不是Boolean
            if (!Boolean.TYPE.equals(customValidationMethod.getReturnType()) && !Boolean.class.equals(customValidationMethod.getReturnType())) {
                throw new IllegalArgumentException(String.format("枚举校验器，枚举对象 [%s] 的自定义校验方法 [%s](%s) 的返回类型不是 Boolean", className, valueClass, customMethodName));
            }

            // 检查自定义校验方法是不是static
            if(!Modifier.isStatic(customValidationMethod.getModifiers())) {
                throw new IllegalArgumentException(String.format("枚举校验器，枚举对象 [%s] 的自定义校验方法 [%s](%s) 不是static", className, valueClass, customMethodName));
            }

        }

        /**
         * 默认校验方法
         *
         * 使用提供的method方法获取值，看是否存在
         *
         * @param value
         * @return
         */
        private boolean defaultValidMethod(Object value) {
            if (value instanceof String) {
                String valueStr = (String) value;
                return isBlank(valueStr) || values.contains(value);
            }
            return Objects.isNull(value) || values.contains(value);
        }

        /**
         * 判断字符串不是空
         * org.apache.commons.lang3.StringUtils#isNotBlank
         *
         * @param cs 字符串
         * @return true：不是空，false：是空
         */
        public static boolean isNotBlank(final CharSequence cs) {
            return !isBlank(cs);
        }

        /**
         * 判断字符串是空
         * org.apache.commons.lang3.StringUtils#isBlank
         *
         * @param cs 字符串
         * @return true：是空，false：不是空
         */
        private static boolean isBlank(final CharSequence cs) {
            final int strLen = length(cs);
            if (strLen == 0) {
                return true;
            }
            for (int i = 0; i < strLen; i++) {
                if (!Character.isWhitespace(cs.charAt(i))) {
                    return false;
                }
            }
            return true;
        }

        /**
         * 获取字符串长度
         * org.apache.commons.lang3.StringUtils#length
         *
         * @param cs 字符串
         * @return 长度
         */
        private static int length(final CharSequence cs) {
            return cs == null ? 0 : cs.length();
        }
    }

}