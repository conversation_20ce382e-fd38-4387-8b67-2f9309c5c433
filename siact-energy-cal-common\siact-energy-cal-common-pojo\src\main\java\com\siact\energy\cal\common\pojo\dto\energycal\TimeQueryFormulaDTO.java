package com.siact.energy.cal.common.pojo.dto.energycal;/**
 * @Package com.siact.energy.cal.common.pojo.dto.energycal
 * @description:
 * <AUTHOR>
 * @create 2024/12/11 14:46
 */


import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName TimeQueryFormulaDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 14:46
 * @Version 1.0
 **/
@Data
public class TimeQueryFormulaDTO {
    @ApiModelProperty(value = "列表",position = 1,required = true)
    private List<CalFormula> calFormulaList;
    @ApiModelProperty(value = "开始时间",position = 2,required = true)
    private String startTime;
    @ApiModelProperty(value = "结束时间",position = 3,required = true)
    private String endTime;
    @ApiModelProperty(value = "步长",position = 4,required = false)
    public Integer interval;
    @ApiModelProperty(value = "步长单位:(Y:年;M:月;D:日;H:小时;MIN:分),示例值(D)",position = 5,required = false)
    public String tsUnit;

    // 查询类型
    private TimeQueryFormulaDTO.TimeQueryType queryType;

    public boolean isEquallySpacedQuery() {
        return interval != null && StrUtil.isNotBlank(tsUnit);
    }

    public enum TimeQueryType {
        EQUALLY_SPACED,    // 等时间间隔
        INTERVAL          // 时间区间;
    }
}
