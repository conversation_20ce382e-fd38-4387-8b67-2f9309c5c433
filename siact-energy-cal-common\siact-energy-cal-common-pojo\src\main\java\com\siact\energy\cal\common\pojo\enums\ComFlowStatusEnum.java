package com.siact.energy.cal.common.pojo.enums;

import lombok.Getter;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-12
 * @Description:
 * @Version: 1.0
 */
@Getter
public enum ComFlowStatusEnum {
    /**
     * 未运行
     */
    NOT_RUNNING(0),
    /**
     * 运行中
     */
    RUNNING(1),
    /**
     * 成功
     */
    SUCCESS(2),

    /**
     * 失败
     */
    FAIL(3);

    private final int value;

     ComFlowStatusEnum(Integer value) {
         this.value = value;
     }
}
