package com.siact.energy.cal.server.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "auth")
public class AuthProperties {
    
    /**
     * 是否启用权限验证
     */
    private boolean enabled = false;

    /**
     * sa-token配置
     */
    private SaTokenConfig saToken = new SaTokenConfig();

    @Data
    public static class SaTokenConfig {
        private String tokenName = "Siact-Token";
        private String tenantUserName;
        private String tenantPassword;
        private String serverUrl;
        private String gatewayUrl;
    }
} 