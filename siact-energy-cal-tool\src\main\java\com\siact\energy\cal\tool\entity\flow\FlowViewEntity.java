package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-30
 * @Description: 组件流与试图的关系
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("si_flow_tab_view_t")
public class FlowViewEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField("flow_id")
    private Integer flowId;
    @TableField("view_name")
    private String viewName;
    private Integer type;
    @TableField("table_name")
    private String tableName;
    @TableField("com_id")
    private String comId;
    @TableField("task_id")
    private String taskId;

}
