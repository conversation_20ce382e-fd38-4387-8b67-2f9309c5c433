// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.dbsynch.pgsql;

import com.siact.energy.cal.dbsynch.IDatabaseSynchronize;

import javax.sql.DataSource;

/**
 * Greenplum数据库DML同步实现类
 *
 * <AUTHOR>
 */
public class GreenplumDatabaseSyncImpl extends PostgresqlDatabaseSyncImpl implements
        IDatabaseSynchronize {

  public GreenplumDatabaseSyncImpl(DataSource ds) {
    super(ds);
  }

}
