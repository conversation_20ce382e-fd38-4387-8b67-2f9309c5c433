package com.siact.energy.cal.server.common.datasource;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * DataSourceTypeEnum
 *
 * <AUTHOR>
 * @since 2024-05-23 15:24:38
 */
@Getter
@RequiredArgsConstructor
public enum DataSourceTypeEnum {

    /** 涛思(时序数据库：https://docs.taosdata.com/) */
    TDENGINE("TDengine", "com.taosdata.jdbc.rs.RestfulDriver", null, "jdbc:TAOS-RS://localhost:6041/test");

    /**
     * 名称
     */
    private final String name;

    /**
     * 驱动
     */
    private final String driver;

    /**
     * 图标
     */
    private final String icon;

    /**
     * 格式
     */
    private final String format;

}
