package com.siact.energy.cal.server.xxljob.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.flow.JobInfoDto;
import com.siact.energy.cal.common.pojo.vo.flow.ComFlowNameVo;
import com.siact.energy.cal.server.dao.xxlJob.XxlJobGroupDao;
import com.siact.energy.cal.server.service.flow.impl.ProcessServiceImpl;
import com.siact.energy.cal.server.xxljob.biz.model.ReturnT;
import com.siact.energy.cal.server.xxljob.core.exception.XxlJobException;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobGroup;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobInfo;
import com.siact.energy.cal.server.xxljob.core.model.XxlJobUser;
import com.siact.energy.cal.server.xxljob.core.route.ExecutorRouteStrategyEnum;
import com.siact.energy.cal.server.xxljob.core.scheduler.MisfireStrategyEnum;
import com.siact.energy.cal.server.xxljob.core.scheduler.ScheduleTypeEnum;
import com.siact.energy.cal.server.xxljob.core.thread.JobScheduleHelper;
import com.siact.energy.cal.server.xxljob.core.util.I18nUtil;
import com.siact.energy.cal.server.xxljob.enums.ExecutorBlockStrategyEnum;
import com.siact.energy.cal.server.xxljob.glue.GlueTypeEnum;
import com.siact.energy.cal.server.xxljob.service.LoginService;
import com.siact.energy.cal.server.xxljob.service.XxlJobService;

import com.siact.energy.cal.server.xxljob.util.DateUtil;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-01
 * @Description: 任务调度中心
 * @Version: 1.0
 */
@Controller
@RequestMapping("/jobinfo")
@ApiSort(110)
@Api(tags = {"任务调度中心"})
public class JobInfoController {
	private static Logger logger = LoggerFactory.getLogger(JobInfoController.class);

	@Resource
	private XxlJobGroupDao xxlJobGroupDao;
	@Resource
	private XxlJobService xxlJobService;
	
//	@RequestMapping
	public String index(HttpServletRequest request, Model model, @RequestParam(required = false, defaultValue = "-1") int jobGroup) {

		// 枚举-字典
		model.addAttribute("ExecutorRouteStrategyEnum", ExecutorRouteStrategyEnum.values());	    // 路由策略-列表
		model.addAttribute("GlueTypeEnum", GlueTypeEnum.values());								// Glue类型-字典
		model.addAttribute("ExecutorBlockStrategyEnum", ExecutorBlockStrategyEnum.values());	    // 阻塞处理策略-字典
		model.addAttribute("ScheduleTypeEnum", ScheduleTypeEnum.values());	    				// 调度类型
		model.addAttribute("MisfireStrategyEnum", MisfireStrategyEnum.values());	    			// 调度过期策略

		// 执行器列表
		List<XxlJobGroup> jobGroupList_all =  xxlJobGroupDao.findAll();

		// filter group
		List<XxlJobGroup> jobGroupList = filterJobGroupByRole(request, jobGroupList_all);
		if (jobGroupList==null || jobGroupList.size()==0) {
			throw new XxlJobException(I18nUtil.getString("jobgroup_empty"));
		}

		model.addAttribute("JobGroupList", jobGroupList);
		model.addAttribute("jobGroup", jobGroup);

		return "jobinfo/jobinfo.index";
	}

	public static List<XxlJobGroup> filterJobGroupByRole(HttpServletRequest request, List<XxlJobGroup> jobGroupList_all){
		List<XxlJobGroup> jobGroupList = new ArrayList<>();
		if (jobGroupList_all!=null && jobGroupList_all.size()>0) {
			XxlJobUser loginUser = (XxlJobUser) request.getAttribute(LoginService.LOGIN_IDENTITY_KEY);
			if (loginUser.getRole() == 1) {
				jobGroupList = jobGroupList_all;
			} else {
				List<String> groupIdStrs = new ArrayList<>();
				if (loginUser.getPermission()!=null && loginUser.getPermission().trim().length()>0) {
					groupIdStrs = Arrays.asList(loginUser.getPermission().trim().split(","));
				}
				for (XxlJobGroup groupItem:jobGroupList_all) {
					if (groupIdStrs.contains(String.valueOf(groupItem.getId()))) {
						jobGroupList.add(groupItem);
					}
				}
			}
		}
		return jobGroupList;
	}
	public static void validPermission(HttpServletRequest request, int jobGroup) {
		XxlJobUser loginUser = (XxlJobUser) request.getAttribute(LoginService.LOGIN_IDENTITY_KEY);
		if (!loginUser.validPermission(jobGroup)) {
			throw new RuntimeException(I18nUtil.getString("system_permission_limit") + "[username="+ loginUser.getUsername() +"]");
		}
	}
	
	@RequestMapping(value = "/pageList", method = RequestMethod.GET)
	@ApiOperation("分页查询")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "start", value = "起始页码", dataType = "int"),
			@ApiImplicitParam(name = "length", value = "每页长度", dataType = "int"),
			@ApiImplicitParam(name = "jobGroup", value = "执行器id", dataType = "int"),
			@ApiImplicitParam(name = "triggerStatus", value = "任务状态:-1:所有,0:停止;1;启动", dataType = "int"),
			@ApiImplicitParam(name = "jobDesc", value = "任务名称", dataType = "String"),
			@ApiImplicitParam(name = "executorHandler", value = "执行jobhandler", dataType = "String"),
			@ApiImplicitParam(name = "author", value = "作者", dataType = "String")
	})
	@ApiOperationSupport(order = 10)
	@ResponseBody
	public R<Map<String, Object>> pageList(@RequestParam(required = false, defaultValue = "0") int start,
			@RequestParam(required = false, defaultValue = "10") int length,
			@RequestParam(required = false, defaultValue = "7")int jobGroup,
			@RequestParam(required = false, defaultValue = "-1")int triggerStatus,
			String jobDesc, String executorHandler, String author) {
		return R.OK(xxlJobService.pageList(start, length, jobGroup, triggerStatus, jobDesc, executorHandler, author));
	}
	
	@RequestMapping(value = "/add", method= RequestMethod.POST)
	@ApiOperation("新增任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "jobInfo", value = "实体对象", dataType = "任务实例")
	})
	@ApiOperationSupport(order = 20)
	@ResponseBody
	public ReturnT<String> add(@RequestBody XxlJobInfo jobInfo) {
		return xxlJobService.add(jobInfo);
	}
	
	@RequestMapping(value ="/update", method= RequestMethod.POST)
	@ApiOperation("更新任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "jobInfo", value = "实体对象", dataType = "任务实例")
	})
	@ApiOperationSupport(order = 30)
	@ResponseBody
	public ReturnT<String> update(@RequestBody XxlJobInfo jobInfo) {
		return xxlJobService.update(jobInfo);
	}
	@RequestMapping(value = "/start", method= RequestMethod.POST)
	@ApiOperation("激活任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "任务id", dataType = "int",required = true)
	})
	@ApiOperationSupport(order = 40)
	@ResponseBody
	public ReturnT<String> start(int id) {
		return xxlJobService.start(id);
	}
	@RequestMapping(value = "/stop", method= RequestMethod.POST)
	@ApiOperation("停止任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "id", value = "任务id", dataType = "int", required = true)
	})
	@ApiOperationSupport(order = 50)
	@ResponseBody
	public ReturnT<String> pause(int id) {
		return xxlJobService.stop(id);
	}


	@RequestMapping(value ="/trigger", method= RequestMethod.POST)
	@ApiOperation("执行一次")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "jobInfoDto", value = "任务", dataType = "JobInfoDto", required = true)
	})
	@ApiOperationSupport(order = 60)
	@ResponseBody
	public ReturnT<String> triggerJob(HttpServletRequest request, @RequestBody JobInfoDto jobInfoDto) {
		// login user
		XxlJobUser loginUser = (XxlJobUser) request.getAttribute(LoginService.LOGIN_IDENTITY_KEY);
		// trigger
		return xxlJobService.trigger(loginUser, jobInfoDto.getId(), jobInfoDto.getExecutorParam(), jobInfoDto.getAddressList());
	}

	@RequestMapping(value = "/nextTriggerTime", method= RequestMethod.GET)
	@ApiOperation("获取下次执行时间")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "scheduleType", value = "调度类型,默认值:CRON", dataType = "String", required = true),
			@ApiImplicitParam(name = "scheduleConf", value = "执行频率:例如0/55 * * * * ?", dataType = "String", required = true)
	})
	@ApiOperationSupport(order = 70)
	@ResponseBody
	public ReturnT<List<String>> nextTriggerTime(@RequestParam(defaultValue = "CRON")String scheduleType,
												 @RequestParam(defaultValue = "0/55 * * * * ?")String scheduleConf) {

		XxlJobInfo paramXxlJobInfo = new XxlJobInfo();
		paramXxlJobInfo.setScheduleType(scheduleType);
		paramXxlJobInfo.setScheduleConf(scheduleConf);

		List<String> result = new ArrayList<>();
		try {
			Date lastTime = new Date();
			for (int i = 0; i < 5; i++) {
				lastTime = JobScheduleHelper.generateNextValidTime(paramXxlJobInfo, lastTime);
				if (lastTime != null) {
					result.add(DateUtil.formatDateTime(lastTime));
				} else {
					break;
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return new ReturnT<List<String>>(ReturnT.FAIL_CODE, (I18nUtil.getString("schedule_type")+I18nUtil.getString("system_unvalid")) + e.getMessage());
		}
		return new ReturnT<List<String>>(result);

	}

	@RequestMapping(value = "/remove", method= RequestMethod.POST)
	@ApiOperation("删除任务")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "ids", value = "主键集合", dataType = "List<Long>", required = true)
	})
	@ApiOperationSupport(order = 80)
	@ResponseBody
	public ReturnT<String> remove(@RequestBody List<Integer> ids) {
		return xxlJobService.remove(ids);
	}


	@Resource
	ProcessServiceImpl processService;
	@RequestMapping(value = "/flow/list", method= RequestMethod.GET)
	@ApiOperation("获取组件流列表")
	@ApiOperationSupport(order = 90)
	@ResponseBody
	public R<List<ComFlowNameVo>> getComFlowList() {
		List<ComFlowNameVo>  comFlowNameVos= processService.getComFlowList();
		return R.OK(comFlowNameVos);
	}
	
}
