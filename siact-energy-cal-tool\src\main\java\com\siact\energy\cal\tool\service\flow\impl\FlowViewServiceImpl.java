package com.siact.energy.cal.tool.service.flow.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.tool.dao.flow.FlowViewMapper;
import com.siact.energy.cal.tool.entity.flow.FlowViewEntity;
import com.siact.energy.cal.tool.service.flow.IFlowViewService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-30
 * @Description: 组件流与视图关系
 * @Version: 1.0
 */
@Service
public class FlowViewServiceImpl extends ServiceImpl<FlowViewMapper, FlowViewEntity> implements IFlowViewService {
    @Resource
    FlowViewMapper flowViewMapper;
    @Override
    public int insertFlowView(int flowId, String viewName, String comId, String taskId) {
        FlowViewEntity flowViewEntity = new FlowViewEntity();
        flowViewEntity.setFlowId(flowId);
        flowViewEntity.setViewName(viewName);
        flowViewEntity.setType(1);
        flowViewEntity.setComId(comId);
        flowViewEntity.setTaskId(taskId);
        flowViewMapper.insert(flowViewEntity);
        return flowViewEntity.getId();
    }

    @Override
    public int insertFlowTable(int flowId, String tableId, String comId, String taskId) {
        FlowViewEntity flowViewEntity = new FlowViewEntity();
        flowViewEntity.setFlowId(flowId);
        flowViewEntity.setTableName(tableId);
        flowViewEntity.setType(2);
        flowViewEntity.setComId(comId);
        flowViewEntity.setTaskId(taskId);
        flowViewMapper.insert(flowViewEntity);
        return flowViewEntity.getId();
    }

    @Override
    public List<String> selectViewNames(int flowId) {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FlowViewEntity::getFlowId, flowId);
        wrapper.eq(FlowViewEntity::getType, 1);
        List<FlowViewEntity> flowViewEntities = flowViewMapper.selectList(wrapper);
        return flowViewEntities.stream().map(FlowViewEntity::getViewName).collect(Collectors.toList());
    }

    @Override
    public List<String> selectViewNames() {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(FlowViewEntity::getCreateTime, new Date());
        wrapper.eq(FlowViewEntity::getType, 1);
        List<FlowViewEntity> flowViewEntities = flowViewMapper.selectList(wrapper);
        return flowViewEntities.stream().map(FlowViewEntity::getViewName).collect(Collectors.toList());
    }

    @Override
    public List<String> selectTableNames() {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(FlowViewEntity::getCreateTime, new Date());
        wrapper.eq(FlowViewEntity::getType, 2);
        List<FlowViewEntity> flowTablesEntities = flowViewMapper.selectList(wrapper);
        return flowTablesEntities.stream().map(FlowViewEntity::getTableName).distinct().collect(Collectors.toList());
    }

    @Override
    public void deleteViews(List<String> viewNames) {
        if (!viewNames.isEmpty()){
            flowViewMapper.deleteViews(viewNames.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
    }

    @Override
    public void deleteTables(List<String> tablesNames) {
        if (!tablesNames.isEmpty()){
            flowViewMapper.deleteTables(tablesNames.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
    }

    @Override
    public void deleteViewNameByFlowId(int flowId) {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.le(FlowViewEntity::getCreateTime, new Date());
        wrapper.eq(FlowViewEntity::getType, 1);
        flowViewMapper.delete(wrapper);
    }

    @Override
    public void deleteViewName(List<String> viewNames)  {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FlowViewEntity::getViewName, viewNames);
        wrapper.eq(FlowViewEntity::getType, 1);
        flowViewMapper.delete(wrapper);
    }

    @Override
    public void deleteTableName(List<String> tableNames) {
        LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FlowViewEntity::getViewName, tableNames);
        wrapper.eq(FlowViewEntity::getType, 2);
        flowViewMapper.delete(wrapper);
    }

    @Override
    public List<Map<String, Object>> executeSql(String sql) {
        return flowViewMapper.executeSql(sql);
    }

    @Override
    public Integer executeSqlCount(String sql) {
        Integer count = flowViewMapper.executeSqlCount(sql);
        return count>100? 100: count;
    }

    @Override
    public List<String> getAllFields(String tableName) {
        return  flowViewMapper.getAllFields(tableName);
    }
}
