package com.siact.energy.cal.tool.entity.flow;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-31
 * @Description: 数据源表
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("data_source")
public class DataSourceEntity {
    @TableId(type = IdType.AUTO)
    private long id;
    @TableField("database_name")
    private String databaseName;
    @TableField("database_ip")
    private String databaseIp;
    @TableField("database_port")
    private String databasePort;
    @ApiModelProperty("数据库名称")
    private String db;
    @TableField("table_name")
    private String tableName;

    private Integer status;
    @TableField("user_name")
    private String userName;

    private String password;

    @TableField("jdbc_url")
    private String jdbcUrl;

    @TableField("project_id")
    private String projectId;
    private String description;


}
