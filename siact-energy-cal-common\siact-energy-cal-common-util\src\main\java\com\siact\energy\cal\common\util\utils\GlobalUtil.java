package com.siact.energy.cal.common.util.utils;/**
 * @Package com.siact.energycal.utils
 * @description: 全局变量类
 * <AUTHOR>
 * @create 2024/7/13 17:07
 */


import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyKey;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyValue;
import com.siact.energy.cal.common.pojo.vo.energycal.DataSourceVo;

import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName GlobalUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/13 17:07
 * @Version 1.0
 **/

public class GlobalUtil {
    //存放计算公式,(projectcode -> (devcode,devproeprty),(formula,caltype))
    public static Map<String, Map<DevPropertyKey, DevPropertyValue>> calculateMap = new ConcurrentHashMap<>();

    // 存放指标属性(projectcode ->(calType->devproperty))
    public static Map<String,Map<Integer, List<String>>> calculateFormulaMap = new ConcurrentHashMap<>();
    //存放属性和计算公式的对应(devproperty -> formula)
    public static Map<String,String> propertyFormulaMap = new ConcurrentHashMap<>();
    //存放不同项目的数据库配置信息
    public static Map<String, DataSourceVo> dbConfigMap = new ConcurrentHashMap<>();
    //存放不同项目的数据库连接信息
    public static Map<String, Connection> dbConnectionMap = new ConcurrentHashMap<>();
}
