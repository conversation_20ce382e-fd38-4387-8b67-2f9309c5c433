package com.siact.energy.cal.server.service;

import com.siact.auth.server.common.vo.UserDetailVO;
import com.siact.auth.vo.ProjectVO;
import java.util.List;
import java.util.function.Function;

public interface BaseService {
    /**
     * 获取当前登录用户
     */
    UserDetailVO getLoginUser();
    
    /**
     * 获取用户项目权限
     */
    List<ProjectVO> getUserProjects();
    
    /**
     * 根据项目dataCode过滤数据
     */
    <T> List<T> filterAuthorizedProjects(List<T> allProjects, Function<T, String> getDataCode);
    
    /**
     * 是否启用权限验证
     */
    boolean isEnabled();
}