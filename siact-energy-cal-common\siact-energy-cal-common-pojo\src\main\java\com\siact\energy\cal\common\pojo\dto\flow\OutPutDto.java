package com.siact.energy.cal.common.pojo.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-25
 * @Description: 输出组件定义
 * @Version: 1.0
 */
@Data
@ApiModel("输出组件DTO")
public class OutPutDto {
    @ApiModelProperty(value = "类型：mysql、MQTT", position = 1)
    private String type;
    @ApiModelProperty(value = "组件名称", position = 2)
    private String name;
    @ApiModelProperty(value = "主机名", position = 3)
    private String host;
    @ApiModelProperty(value = "端口", position = 4)
    private String port;
    @ApiModelProperty(value = "用户名", position = 5)
    private String userName;
    @ApiModelProperty(value = "密码", position = 6)
    private String password;
    @ApiModelProperty(value = "数据库名", position = 7)
    private String db;
    @ApiModelProperty(value = "组件描述", position = 8)
    private String des;
    @ApiModelProperty(value = "MQTT主题", position = 9)
    private String topic;
    @ApiModelProperty(value = "broker", position = 10)
    private String url;
}
