package com.siact.energy.cal.common.util.utils;

import com.siact.energy.cal.common.pojo.enums.DbTypeEnum;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;

/**
 * @Author: 李飞
 * @CreateTime: 2024-07-10
 * @Description:
 * @Version: 1.0
 */
public class Daos {
    public static void main(String[] args) throws Exception {
        //jdbc:TAOS-RS://***********:29001/test?useUnicode=true&characterEncoding=utf-8&serverTimezone=UTC&batchfetch=true
        Connection con = DBConnection.connection(DbTypeEnum.TaoS.getDbType(), "***********",
                "29001", "test", "root",
                "taosdata");

        String sb = "select min(itemvalue) as a  from test.xych where devproperty in ('PGY02001_SPD01001_STPDZ01004_UDYPDX003_EQPD01DYPDX001_MPEQR2001', 'PGY02001_SPD01001_STPDZ01004_UDYPDX003_EQPD01DYPDX001_MPEQR2001')  group by 'ts' limit 100";
        ResultSet rs = DBTools.executeQuerySql(con, sb.toString());
        ResultSetMetaData metaData = rs.getMetaData();
        while (true){
            if (!rs.next()){
                break;
            }
            System.out.println(rs.getObject(1));
        }
        con.close();
    }
}
