package com.siact.energy.cal.common.pojo.dto.ruleCol;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 指标集表(RuleCol) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-05-20 11:30:23
 */
@ApiModel("指标集表更新DTO")
@Data
public class RuleColUpdateDTO extends RuleColInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

