package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
public enum DbTypeEnum {

    Mysql("mysql", "com.mysql.cj.jdbc.Driver"),

    DM("dm", "dm.jdbc.driver.DmDriver"),

    oracle("oracle", "oracle.jdbc.driver.OracleDriver"),

    postgresql("pg", "org.postgresql.Driver"),

    TaoS("taoS", "com.taosdata.jdbc.TSDBDriver"),
    InfluxDB("InfluxDB","");


    private final String dbType;
    private final String driverName;

    DbTypeEnum(String dbType, String driverName) {
        this.dbType = dbType;
        this.driverName = driverName;
    }

}
