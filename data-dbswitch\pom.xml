<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.siact</groupId>
        <artifactId>siact-energy-cal</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.siact.energy.cal</groupId>
    <artifactId>data-dbswitch</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>data-dbswitch</name>
    <description>data-dbswitch</description>
    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <mysql-connector-java.version>8.0.28</mysql-connector-java.version>
        <postgresql.version>42.2.18</postgresql.version>
        <ojdbc8.version>********</ojdbc8.version>
        <sqljdbc6.0.version>6.0</sqljdbc6.0.version>
        <msbase.version>3.0</msbase.version>
        <msutil.version>3.0</msutil.version>
        <mssqlserver.version>3.0</mssqlserver.version>
        <greenplum-jdbc.version>5.1.4</greenplum-jdbc.version>
        <dm-jdbc.version>1.0.0</dm-jdbc.version>
        <kingbase-jdbc.version>8.6.0</kingbase-jdbc.version>
        <hive-jdbc.version>3.1.2</hive-jdbc.version>
        <sizeof.version>0.4.0</sizeof.version>
        <json-smart.version>2.3</json-smart.version>
        <calcite-core.version>1.21.0</calcite-core.version>
        <calcite-server.version>1.21.0</calcite-server.version>
        <jsqlparser.version>4.3</jsqlparser.version>
        <druid-starter-version>1.2.8</druid-starter-version>
        <hutool.version>5.8.4</hutool.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid-starter-version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.ojdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>${ojdbc8.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.ojdbc</groupId>
            <artifactId>orai18n</artifactId>
            <version>${ojdbc8.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!-- mvn install:install-file -DgroupId=com.microsoft.sqlserver -DartifactId=sqljdbc6.0 -Dversion=6.0 -Dpackaging=jar -Dfile=sqljdbc6.0-6.0.jar-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>sqljdbc4</artifactId>
            <version>4.0</version>
        </dependency>
        <!--mvn install:install-file -DgroupId=com.microsoft.sqlserver -DartifactId=msbase -Dversion=3.0 -Dpackaging=jar -Dfile=msbase.jar-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>msbase</artifactId>
            <version>${msbase.version}</version>
        </dependency>
        <!--mvn install:install-file -DgroupId=com.microsoft.sqlserver -DartifactId=msutil -Dversion=3.0 -Dpackaging=jar -Dfile=msutil.jar-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>msutil</artifactId>
            <version>${msutil.version}</version>
        </dependency>
        <!-- mvn install:install-file -DgroupId=com.microsoft.sqlserver -DartifactId=mssqlserver -Dversion=3.0 -Dpackaging=jar -Dfile=mssqlserver.jar-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssqlserver</artifactId>
            <version>${mssqlserver.version}</version>
        </dependency>
        <!--mvn install:install-file -DgroupId=com.pivotal -DartifactId=greenplum-jdbc -Dversion=5.1.4 -Dpackaging=jar -Dfile=greenplum-jdbc-5.1.4.jar-->
      <!--  <dependency>
            <groupId>com.pivotal</groupId>
            <artifactId>greenplum-jdbc</artifactId>
            <version>${greenplum-jdbc.version}</version>
        </dependency>-->
        <!--mvn install:install-file -DgroupId=com.dameng -DartifactId=dm-jdbc -Dversion=1.0.0 -Dpackaging=jar -Dfile=DmJdbcDriver18.jar-->
       <!-- <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>dm-jdbc</artifactId>
            <version>${dm-jdbc.version}</version>
        </dependency>-->
        <!--mvn install:install-file -DgroupId=com.kingbase -DartifactId=kingbase-jdbc -Dversion=8.6.0 -Dpackaging=jar -Dfile=kingbase8-8.6.0.jar-->
        <dependency>
            <groupId>com.kingbase</groupId>
            <artifactId>kingbase-jdbc</artifactId>
            <version>${kingbase-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1-jre</version>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc</artifactId>
            <version>db2jcc4</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.31.1</version>
        </dependency>

        <!-- 时序数据库的jar -->
        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>2.0.37</version>
        </dependency>

        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>${hive-jdbc.version}</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty.aggregate</groupId>
                    <artifactId>jetty-all</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hive</groupId>
                    <artifactId>hive-shims</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>tomcat</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty.orbit</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty.aggregate</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--mvn install:install-file -Dfile=jconn4.jar -DgroupId=com.sybase -DartifactId=jconn4 -Dversion=1.0 -Dpackaging=jar-->
     <!--   <dependency>
            <groupId>com.sybase</groupId>
            <artifactId>jconn4</artifactId>
            <version>1.0</version>
            <scope>runtime</scope>
        </dependency>-->

        <!--mvn install:install-file -Dfile=oscarJDBC8.jar -DgroupId=com.oscar -DartifactId=oscar-jdbc -Dversion=7.0.0 -Dpackaging=jar-->
      <!--  <dependency>
            <groupId>com.oscar</groupId>
            <artifactId>oscar-jdbc</artifactId>
            <version>7.0.0</version>
            <scope>runtime</scope>
        </dependency>-->

        <!--mvn install:install-file -Dfile=gbase-connector-java-8.3.81.53-build55.5.3-bin.jar -DgroupId=com.gbase.jdbc -DartifactId=gbase-connector-java -Dversion=8.3.81.53 -Dpackaging=jar-->
       <!-- <dependency>
            <groupId>com.gbase.jdbc</groupId>
            <artifactId>gbase-connector-java</artifactId>
            <version>8.3.81.53</version>
            <scope>runtime</scope>
        </dependency>-->

        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>sizeof</artifactId>
            <version>${sizeof.version}</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>${json-smart.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-core</artifactId>
            <version>${calcite-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.calcite</groupId>
            <artifactId>calcite-server</artifactId>
            <version>${calcite-server.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--sql解析工具-->
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>

        <!--flink 相关-->
      <!--  <dependency>
            <groupId>net.srt</groupId>
            <artifactId>flink-common</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>-->

      <!--  <dependency>
            <groupId>net.srt</groupId>
            <artifactId>flink-process</artifactId>
            <version>${project.version}</version>
            <scope>provided</scope>
        </dependency>-->
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 发布插件 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>siact-public</id>
            <name>siact-public</name>
            <url>http://192.100.30.160:8081/repository/siact-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>siact-snapshots</id>
            <name>siact-snapshots</name>
            <url>http://192.100.30.160:8081/repository/siact-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>siact-release</id>
            <name>siact-release</name>
            <url>http://192.100.30.160:8081/repository/siact-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>siact-release</id>
            <name>siact-release</name>
            <url>http://192.100.30.160:8081/repository/siact-release/</url>
        </repository>
        <snapshotRepository>
            <id>siact-snapshots</id>
            <name>siact-snapshots</name>
            <url>http://192.100.30.160:8081/repository/siact-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
