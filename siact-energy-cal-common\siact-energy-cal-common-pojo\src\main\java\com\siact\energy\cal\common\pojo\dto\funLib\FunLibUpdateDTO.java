package com.siact.energy.cal.common.pojo.dto.funLib;


import com.siact.energy.cal.common.pojo.validator.UpdateValidGroup;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;


/**
 * 常用函数库(FunLib) 更新DTO
 *
 * <AUTHOR>
 * @since 2024-06-11 11:04:46
 */
@ApiModel("常用函数库更新DTO")
@Data
public class FunLibUpdateDTO extends FunLibInsertDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    @NotNull(groups = UpdateValidGroup.class, message = "ID不能为空")
    private Long id;

}

