package com.siact.energy.cal.tool.convertor.flow;

import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ComponentFlowDto;
import com.siact.energy.cal.common.pojo.vo.flow.ComponentFlowVO;
import com.siact.energy.cal.tool.entity.flow.ComponentFlowEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: 李飞
 * @CreateTime: 2024-06-12
 * @Description:
 * @Version: 1.0
 */
@Mapper
public interface ComponentFlowConvertor {
     ComponentFlowConvertor INSTANCE = Mappers.getMapper(ComponentFlowConvertor.class);

     ComponentFlowEntity dtoToEntity(ComponentFlowDto componentFlowDto);

     ComponentFlowVO entityToVo(ComponentFlowEntity componentFlowEntity);

     List<ComponentFlowVO> entityToVoList(List<ComponentFlowEntity> componentFlowEntityList);

     PageBean<ComponentFlowEntity> voToEntity(PageBean<ComponentFlowVO> componentFlowVoPageBean);
}
