package com.siact.energy.cal.common.pojo.vo.ruleCol;



import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * 指标集表(RuleCol) VO
 *
 * <AUTHOR>
 * @since 2024-05-20 11:30:22
 */
@ApiModel("指标集表VO")
@Data
public class RuleColVO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", position = 1)
    private Long id;

    /**
     * 指标集名称
     */
    @ApiModelProperty(value = "指标集名称", position = 2)
    private String ruleColName;

    /**
     * 指标集描述
     */
    @ApiModelProperty(value = "指标集描述", position = 3)
    private String ruleColDes;

    /**
     * 指标集构建
     */
    @ApiModelProperty(value = "指标集构建-属性列表", required = true, position = 4)
    private List<AttrObject> attrList;

    /**
     * 是否激活（0-激活,1-未激活）
     */
    @ApiModelProperty(value = "是否激活（0-激活,1-未激活）", position = 4)
    private Integer activeState;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", position = 5)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 6)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}

