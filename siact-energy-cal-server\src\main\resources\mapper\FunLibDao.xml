<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.server.dao.funLib.FunLibDao">

    <resultMap type="com.siact.energy.cal.server.entity.funLib.FunLib" id="FunLibMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="label" column="label" jdbcType="VARCHAR"/>
        <result property="pieces" column="pieces" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="calType" column="cal_type" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into fun_lib(value, label, pieces, description, cal_type, creator, create_time, updater, update_time,
        deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.value}, #{entity.label}, #{entity.pieces}, #{entity.description}, #{entity.calType},
            #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into fun_lib(value, label, pieces, description, cal_type, creator, create_time, updater, update_time,
        deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.value}, #{entity.label}, #{entity.pieces}, #{entity.description}, #{entity.calType},
            #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
        on duplicate key update
        value = values(value) , label = values(label) , pieces = values(pieces) , description = values(description) ,
        cal_type = values(cal_type) , creator = values(creator) , create_time = values(create_time) , updater =
        values(updater) , update_time = values(update_time) , deleted = values(deleted)
    </insert>

</mapper>

