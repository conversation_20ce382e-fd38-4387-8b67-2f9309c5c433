package com.siact.energy.cal.server.service.ruleDetail.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.googlecode.aviator.AviatorEvaluator;
import com.siact.api.common.api.vo.common.*;
import com.siact.api.feign.api.ins.InsService;
import com.siact.api.feign.api.model.ModelService;
import com.siact.common.core.enums.PropertyGroupEnum;
import com.siact.common.core.vo.PageVo;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.*;
import com.siact.energy.cal.common.pojo.enums.ActiveStateEnum;
import com.siact.energy.cal.common.pojo.enums.DeletedEnum;
import com.siact.energy.cal.common.pojo.enums.RuleTypeEnum;
import com.siact.energy.cal.common.pojo.vo.common.DigitalTwinTreeVO;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleFormulaDetail;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.convertor.common.DigitalTwinConvertor;
import com.siact.energy.cal.server.convertor.ruleDetail.RuleDetailConvertor;
import com.siact.energy.cal.server.dao.ruleDetail.RuleDetailDao;
import com.siact.energy.cal.server.entity.ruleDetail.FormulaDetail;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetail;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance;
import com.siact.energy.cal.server.service.dataProject.DataProjectService;
import com.siact.energy.cal.server.service.dataProject.impl.DataProjectServiceImpl;
import com.siact.energy.cal.server.service.ruleCol.RuleColService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
import com.siact.ins.server.common.vo.common.InsTreeVo;
import com.siact.model.vo.common.ModelTreeVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * 指标详情表(RuleDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Slf4j
@Service("ruleDetailService")
public class RuleDetailServiceImpl extends ServiceImpl<RuleDetailDao, RuleDetail> implements RuleDetailService {

    @Autowired
    private RuleDetailDao ruleDetailDao;

    @Autowired
    private ModelService modelService;

    @Autowired
    private InsService insService;

    @Autowired
    private DataProjectService dataProjectService;

    @Autowired
    private DataProjectServiceImpl dataProjectServiceImpl;

    @Autowired
    private RuleColService ruleColService;

    @Autowired
    private RuleDetailInstanceService ruleDetailInstanceService;

    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;

    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    private final String prefix = "@[";
    private final String suffix = "]";


    // 在类的顶部，其他内部类定义的位置添加
    @Data
    @AllArgsConstructor
    private static class ModelInstanceMappingResult {
        private Map<String, Map<String, String>> modelInsMap;
        private Map<String, String> devPropertyNameMap;
    }
    /**
     * 模型树缓存名称
     */
    private static final String MODEL_TREE_CACHE_NAMES = "modelTree";

    /**
     * 实例树缓存名称
     */
    private static final String INS_TREE_CACHE_NAMES = "insTree";

    /**
     * 属性组后缀
     */
    public static final String PROP_GROUP_SUFFIX = "Group";

    /**
     * 空字符串
     */
    public static final String EMPTY_STR = "";

    /**
     * 中杠
     */
    public static final String CENTER_BAR = "-";

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 规则变更事件
     */
    @Getter
    public static class RuleChangeEvent extends ApplicationEvent {
        private final RuleDetail rule;
        private final RuleDetail oldRule;
        private final ChangeType changeType;

        public RuleChangeEvent(Object source, RuleDetail rule, RuleDetail oldRule, ChangeType changeType) {
            super(source);
            this.rule = rule;
            this.oldRule = oldRule;
            this.changeType = changeType;
        }

        public enum ChangeType {
            ADD,        // 新增
            DELETE,     // 删除
            MODIFY,     // 修改
            ENABLE,     // 启用
            DISABLE     // 禁用
        }
    }

    /**
     * 发布规则变更事件
     */
    private void publishRuleChangeEvent(RuleDetail rule, RuleDetail oldRule, RuleChangeEvent.ChangeType changeType) {
        try {
            eventPublisher.publishEvent(new RuleChangeEvent(this, rule, oldRule, changeType));
        } catch (Exception e) {
            log.error("发布规则变更事件失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public PageBean<RuleDetailVO> listPage(PageBean<RuleDetailVO> page, RuleDetailQueryDTO ruleDetailQueryDTO) {
        // 如果projectId为空，返回空的指标详情列表
        if (ruleDetailQueryDTO.getProjectId() == null) {
            PageBean<RuleDetailVO> emptyPage = new PageBean<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            emptyPage.setCurrent(page.getCurrent());
            emptyPage.setSize(page.getSize());
            return emptyPage;
        }

        // FIXME 高卫东 自定义的查询方法，不能将字符串转为数组问题修复
        PageBean<RuleDetailVO> pageBean = RuleDetailConvertor.INSTANCE.entityPage2VoPageBean(ruleDetailDao.listPage(page, ruleDetailQueryDTO));
        pageBean.getRecords().forEach(r -> {
            RuleDetailVO ruleDetailVO = getVoById(r.getId());
            r.setRuleFormula(ruleDetailVO.getRuleFormula());
            r.setRuleFormulaShow(ruleDetailVO.getRuleFormulaShow());
        });
        return pageBean;
    }

    @Override
    public RuleDetailVO getVoById(Serializable id) {
        RuleDetail ruleDetail = getById(id);
        return RuleDetailConvertor.INSTANCE.entity2Vo(ruleDetail);
    }

    @Override
    public List<RuleDetailVO> getDatacodeByRuleColId(Long ruleColId) {
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleDetail::getRuleColId, ruleColId);
        return RuleDetailConvertor.INSTANCE.entities2Vos(list(queryWrapper));
    }

    @Override
    @Transactional
    public Boolean save(RuleDetailInsertDTO ruleDetailInsertDTO) {
        RuleDetail ruleDetail = RuleDetailConvertor.INSTANCE.insertDTO2Entity(ruleDetailInsertDTO);
        ruleDetail.setActiveState(ActiveStateEnum.ENABLE.getValue());
        ruleDetail.setDeleted(DeletedEnum.NORMAL.getValue());
        boolean saved = save(ruleDetail);
        if (saved) {
            // 发布新增事件
            publishRuleChangeEvent(ruleDetail, null, RuleChangeEvent.ChangeType.ADD);

            // 更新指标集的attr_list列 - 注释掉与tool模块保持一致
//            Long ruleColId = ruleDetailInsertDTO.getRuleColId();
//            List<AttrObject> targetAttrList = getTargetAttrList(ruleDetailInsertDTO);
//            ruleColService.updateAttrList(ruleColId, targetAttrList);
        }
        return saved;
    }

    @Override
    @Transactional
    public Boolean updateVoById(RuleDetailUpdateDTO ruleDetailUpdateDTO) {
        RuleDetail oldRule = getById(ruleDetailUpdateDTO.getId());
        RuleDetail newRule = RuleDetailConvertor.INSTANCE.updateDTO2Entity(ruleDetailUpdateDTO);

        boolean updated = updateById(newRule);
        if (updated) {
            // 发布修改事件
            publishRuleChangeEvent(newRule, oldRule, RuleChangeEvent.ChangeType.MODIFY);
        }
        return updated;
    }

    @Override
    public void verifyInsertParam(RuleDetailInsertDTO dto) {
        if (isExistByDevProperty(dto.getDevProperty(), dto.getProjectId(), dto.getRuleColId(), null)) {
            throw new BizException("当前输出指标已经存在指标表达式");
        }
        //判断是否存在循环依赖,即A=A+B 公式是不允许的
        if (iscircularDependency(dto)) {
            throw new BizException("当前表达式存在循环依赖,请修改输出指标");
        }
        ;
    }

    private Boolean iscircularDependency(RuleDetailInsertDTO dto) {
        String devProperty = dto.getDevProperty();
        List<String> ruleFormula = dto.getRuleFormula();
        return ruleFormula.contains(prefix + devProperty + suffix);
    }

    /**
     * 是否存在属性编码的表达式
     *
     * @param devProperty 属性编码
     * @param id          数据id
     * @return 是否存在
     */
    private boolean isExistByDevProperty(String devProperty, Long projectId, Long ruleColId, Long id) {
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(RuleDetail::getDevProperty, devProperty)
                .eq(RuleDetail::getProjectId, projectId)
                .eq(RuleDetail::getRuleColId, ruleColId);

        List<RuleDetail> list = list(queryWrapper);
        boolean exist = !CollectionUtils.isEmpty(list) && !Objects.equals(id, list.get(0).getId());
        return exist;
    }

    /**
     * 根据RuleDetail获取规则详情
     */
    public RuleDetailVo getRuleDetailByDevproperty(RuleDetail rule) {
        if (rule == null) {
            return null;
        }
        return ruleDetailDao.getFormulaDetailByDevproperty(rule.getProjectId(), rule.getDevProperty());
    }

    /**
     * 根据RuleDetailInsertDTO获取规则详情
     */
    public RuleDetailVo getRuleDetailByDevproperty(RuleDetailInsertDTO ruleDetailInsertDTO) {
        if (ruleDetailInsertDTO == null) {
            return null;
        }
        return ruleDetailDao.getFormulaDetailByDevproperty(
                ruleDetailInsertDTO.getProjectId(),
                ruleDetailInsertDTO.getDevProperty()
        );
    }

    /**
     * 根据RuleDetailUpdateDTO获取规则详情
     */
    public RuleDetailVo getRuleDetailByDevproperty(RuleDetailUpdateDTO ruleDetailUpdateDTO) {
        if (ruleDetailUpdateDTO == null) {
            return null;
        }
        return ruleDetailDao.getFormulaDetailByDevproperty(
                ruleDetailUpdateDTO.getProjectId(),
                ruleDetailUpdateDTO.getDevProperty()
        );
    }

    @Override
    public void verifyUpdateParam(RuleDetailUpdateDTO dto) {
        if (isExistByDevProperty(dto.getDevProperty(), dto.getProjectId(), dto.getRuleColId(), dto.getId())) {
            throw new BizException("当前输出指标已经存在指标表达式");
        }
    }

    @Override
    @Cacheable(cacheNames = MODEL_TREE_CACHE_NAMES, key = "#dataCode")
    public List<DigitalTwinTreeVO> modelTree(String dataCode) {

        List<DigitalTwinTreeVO> digitalTwinTreeVOList;

        // 获取数字孪生模型树
        R<List<ModelTreeVo>> listR = modelService.list(dataCode, true, true, false, false, true, true);
        if (Objects.equals(R.OK().getCode(), listR.getCode())) {

            List<ModelTreeVo> modelTreeVoList = listR.getData();
            // 转换为目标对象
            DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
            digitalTwinTreeVOList = convertor.modelTreeVos2DigitalTwinTreeVOs(modelTreeVoList);
            // 递归将编码分组
            Table<String, String, DigitalTwinTreeVO> table = HashBasedTable.create();
            buildDataCodeNodeTypeObjTable(digitalTwinTreeVOList, table);

            // 查询并拼装动态、静态属性
            // 按照节点类型分组
            Map<String, Map<String, DigitalTwinTreeVO>> nodeTypeVoMap = table.columnMap();
            // 分类遍历
            // TODO 高卫东 并发调用
            nodeTypeVoMap.forEach((k, v) -> {
                // 分批调用
                List<String> dataCodeList = Lists.newArrayList(v.keySet());
                Lists.partition(dataCodeList, 200).forEach(l -> {

                    ModelQueryPropertyDTO modelQueryPropertyDTO = new ModelQueryPropertyDTO();
                    modelQueryPropertyDTO.setDataCodes(l);
                    modelQueryPropertyDTO.setPageNumber(1);
                    modelQueryPropertyDTO.setPageSize(Integer.MAX_VALUE);

                    // 动态属性
                    R<PageVo<ModelPropertyVO>> dynamicPageVoR = modelService.pageDynamicProp(modelQueryPropertyDTO);
                    PropertyGroupEnum dynamicProp = PropertyGroupEnum.Dynamic;
                    if (Objects.equals(R.OK().getCode(), dynamicPageVoR.getCode())) {
                        fillModelPropData(convertor, table, dynamicPageVoR, dynamicProp);
                    } else {
                        log.error("获取动态属性失败：{}", dynamicPageVoR);
                        throw new BizException("获取动态属性失败");
                    }

                    // 静态属性
                    R<PageVo<ModelPropertyVO>> staticPageVoR = modelService.pageStaticProp(modelQueryPropertyDTO);
                    PropertyGroupEnum staticProp = PropertyGroupEnum.Static;
                    if (Objects.equals(R.OK().getCode(), staticPageVoR.getCode())) {
                        fillModelPropData(convertor, table, staticPageVoR, staticProp);
                    } else {
                        log.error("获取静态属性失败：{}", staticPageVoR);
                        throw new BizException("获取静态属性失败");
                    }

                });
            });

        } else {
            log.error("获取模型树失败：{}", listR);
            throw new BizException("获取模型树失败");
        }

        return digitalTwinTreeVOList;
    }

    /**
     * 填充属性数据
     *
     * @param convertor         转换器
     * @param table             节点数据
     * @param pageVoR           数字孪生分页数据
     * @param propertyGroupEnum 属性组
     */
    private static void fillModelPropData(DigitalTwinConvertor convertor, Table<String, String, DigitalTwinTreeVO> table, R<PageVo<ModelPropertyVO>> pageVoR, PropertyGroupEnum propertyGroupEnum) {

        // 获取到分页数据
        List<ModelPropertyVO> modelPropertyVOList = pageVoR.getData().getRecords();
        // 按照节点编码分组
        Map<String, List<ModelPropertyVO>> dataCodePropVOMap = modelPropertyVOList.stream().collect(Collectors.groupingBy(ModelPropertyVO::getModelDataCode));
        // 遍历
        dataCodePropVOMap.forEach((k, v) -> {
            // 转换为简化对象
            List<DigitalTwinTreeVO> propVOList = convertor.propVOs2DigitalTwinTreeVOs(v, propertyGroupEnum.getTag());
            // 构建属性根简化对象
            DigitalTwinTreeVO propVO = DigitalTwinTreeVO.builder()
                    .name(propertyGroupEnum.getDesc())
                    .nodeType(joinPropGroupField(propertyGroupEnum.getTag(), EMPTY_STR))
                    .dataCode(joinPropGroupField(k, CENTER_BAR)).children(propVOList).build();
            table.row(k).values().forEach(d -> {
                // 如果当前节点不存在存在子节点
                if (CollectionUtils.isEmpty(d.getChildren())) {
                    d.setChildren(Lists.newArrayList(propVO));
                }
                // 如果当前节点存在子节点
                else {
                    d.getChildren().add(0, propVO);
                }
            });
        });
    }

    /**
     * 拼接属性组信息
     *
     * @param str   待拼接字符串
     * @param split 分割符
     * @return 拼接结果
     */
    public static String joinPropGroupField(String str, String split) {
        return String.join(EMPTY_STR, str, split, PROP_GROUP_SUFFIX);
    }

    /**
     * 递归组装数字化编码、类型、对象集合
     *
     * @param digitalTwinTreeVOList 原始数据
     * @param table                 结果收集集合
     */
    private void buildDataCodeNodeTypeObjTable(List<DigitalTwinTreeVO> digitalTwinTreeVOList,
                                               Table<String, String, DigitalTwinTreeVO> table) {
        if (!CollectionUtils.isEmpty(digitalTwinTreeVOList)) {
            digitalTwinTreeVOList.forEach(d -> {
                table.put(d.getDataCode(), d.getNodeType(), d);
                buildDataCodeNodeTypeObjTable(d.getChildren(), table);
            });
        }
    }

    @Override
    @Cacheable(cacheNames = INS_TREE_CACHE_NAMES, key = "#dataCode")
    public List<DigitalTwinTreeVO> insTree(String dataCode) {
        List<DigitalTwinTreeVO> digitalTwinTreeVOList;
        StopWatch stopWatch = new StopWatch();

        // 特殊处理 ENERGY_DATAWAREHOUSE
        if ("ENERGY_DATA_WAREHOUSE".equals(dataCode)) {
            stopWatch.start("查询能源数据仓所有项目实例树");
            try {
                // 1. 获取所有项目
                List<DataProjectVO> projectByDigitalTwin = dataProjectServiceImpl.getProjectByDigitalTwin();
                if (ObjectUtil.isEmpty(projectByDigitalTwin)) {
                    log.error("获取项目列表失败：{}", projectByDigitalTwin);
                    throw new BizException("获取项目列表失败");
                }

                List<InsTreeVo> allInsTreeVos = new ArrayList<>();
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 2. 并发获取每个项目的实例树
                for (DataProjectVO project : projectByDigitalTwin) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            R<List<InsTreeVo>> insListR = insService.list(
                                    project.getProjectCode(), true, true, false, false, true, true);
                            if (Objects.equals(R.OK().getCode(), insListR.getCode())) {
                                synchronized (allInsTreeVos) {
                                    allInsTreeVos.addAll(insListR.getData());
                                }
                            } else {
                                log.error("获取项目{}的实例树失败：{}", project.getProjectCode(), insListR);
                            }
                        } catch (Exception e) {
                            log.error("获取项目{}的实例树异常：", project.getProjectCode(), e);
                        }
                    }, executor);  // 使用线程池执行异步任务

                    futures.add(future);
                }

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                // 转换为目标对象
                DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
                digitalTwinTreeVOList = convertor.insTreeVos2DigitalTwinTreeVOs(allInsTreeVos);

            } catch (Exception e) {
                log.error("获取能源数据仓所有实例树失败：", e);
                throw new BizException("获取能源数据仓所有实例树失败");
            }
            stopWatch.stop();
        } else {
            // 原有逻辑
            stopWatch.start("查询数字孪生实例树");
            R<List<InsTreeVo>> listR = insService.list(dataCode, true, true, false, false, true, true);
            stopWatch.stop();
            if (Objects.equals(R.OK().getCode(), listR.getCode())) {
                List<InsTreeVo> insTreeVoList = listR.getData();
                DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
                digitalTwinTreeVOList = convertor.insTreeVos2DigitalTwinTreeVOs(insTreeVoList);
            } else {
                log.error("获取实例树失败：{}", listR);
                throw new BizException("获取实例树失败");
            }
        }

        log.info(stopWatch.prettyPrint());
        log.info("总用时：" + stopWatch.getTotalTimeMillis() + "ms");

        return digitalTwinTreeVOList;
    }






    /**
     * 根据 dataCode 获取实例名称
     *
     * @param dataCode 实例编码
     * @return 实例名称
     */
    public String getInsNameByDataCode(String dataCode) {
        String url = dataTwinsUrl + "/common/ins/def"; // 接口地址
        Map<String, String> params = new HashMap<>();
        params.put("dataCode", dataCode);

        // 发送 GET 请求并获取响应
        String response = HttpClientUtil.doGet(url, params);

        // 解析响应
        JSONObject jsonResponse = JSONObject.parseObject(response);
        if (jsonResponse.getInteger("code") == 200) {
            JSONObject data = jsonResponse.getJSONObject("data");
            return data.getString("insName"); // 返回 insName
        } else {
            log.error("获取实例定义信息失败: {}", jsonResponse.getString("msg"));
            return null; // 或者抛出异常
        }
    }

    /**
     * 根据 dataCode 获取属性名称
     *
     * @param dataCode 数据编码
     * @return 属性名称(propName)
     */
    public String getPropNameByDataCode(String dataCode) {
        String url = dataTwinsUrl + "/prop/all/rt"; // 接口地址

        // 构建请求体 - JSON数组格式
        JSONArray requestBody = new JSONArray();
        requestBody.add(dataCode);

        // 发送 POST 请求并获取响应
        String response = null;
        try {
            response = HttpClientUtil.postJson(url, requestBody);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 解析响应
        JSONObject jsonResponse = JSONObject.parseObject(response);
        if (jsonResponse.getInteger("code") == 200) {
            JSONObject data = jsonResponse.getJSONArray("data").getJSONObject(0);
            return data.getString("propName");
        } else {
            log.error("获取属性名称失败: {}", jsonResponse.getString("msg"));
            return null;
        }
    }

    private RuleDetailViewDTO buildRuleDetailViewDTO(RuleDetail ruleDetail, String formula) {
        RuleDetailViewDTO dto = new RuleDetailViewDTO();

        // 设置规则类型（实例级/模型级）
        dto.setRuleType(ruleDetail.getRuleType());

        // 设置计算类型
        dto.setCalType(ruleDetail.getCalType());

        // 设置节点（模型）信息
        dto.setDevName(ruleDetail.getDevName());
        dto.setDevNameCode(ruleDetail.getDevCode());

        // 设置属性信息
        dto.setPropName(ruleDetail.getPropName());
        dto.setPropNameCode(ruleDetail.getDevProperty());

        // 设置指标集信息
        dto.setRuleColId(String.valueOf(ruleDetail.getRuleColId()));

        // 设置项目ID
        dto.setProjectId(ruleDetail.getProjectId());

        // 设置公式
        dto.setFormulaShow(formula);

        return dto;
    }


    public static List<DigitalTwinTreeVO> mergeLists(List<DigitalTwinTreeVO> list1, List<DigitalTwinTreeVO> list2) {
        // 创建一个Map来存储第一个列表的数据，方便通过dataCode快速查找
        Map<String, DigitalTwinTreeVO> map = new HashMap<>();
        for (DigitalTwinTreeVO item : list1) {
            map.put(item.getDataCode(), item);
        }

        // 创建一个新的列表来保存最终的结果
        List<DigitalTwinTreeVO> mergedList = new ArrayList<>(list1);

        // 遍历第二个列表
        for (DigitalTwinTreeVO item : list2) {
            // 检查是否已经存在于map中
            if (map.containsKey(item.getDataCode())) {
                // 如果存在，则合并children
                DigitalTwinTreeVO existingItem = map.get(item.getDataCode());
                existingItem.getChildren().addAll(item.getChildren());
            } else {
                // 如果不存在，则添加到新的列表中
                mergedList.add(item);
            }
        }

        return mergedList;
    }

    @Override
    @CacheEvict(cacheNames = MODEL_TREE_CACHE_NAMES, key = "#dataCode")
    public void clearModelTreeCache(String dataCode) {

    }

    @Override
    @CacheEvict(cacheNames = INS_TREE_CACHE_NAMES, key = "#dataCode")
    public void clearInsTreeCache(String dataCode) {

    }

    @Override
    public boolean verifyExpression(RuleDetailVerifyExpressionDTO dto) {

        // TODO 高卫东 修改为jep校验

        try {
            List<String> afterReplaceRuleFormula = Lists.newArrayList();
            dto.getRuleFormula().forEach(s -> {
                afterReplaceRuleFormula.add(s
                        // 替换函数
                        .replaceFirst("^#\\[(.*)\\]$", "$1")
                        // 替换变量
                        .replaceFirst("^@\\[(.*)\\]$", "$1"));
            });
            String expression = afterReplaceRuleFormula.stream().collect(Collectors.joining());
            AviatorEvaluator.validate(expression);
            return true;
        } catch (Exception e) {
            log.info("指标表达式校验不通过，", e);
            throw new BizException("指标表达式校验不通过");
        }
    }

    @Override
    @Transactional
    public Boolean toggle(Integer activeState, List<Long> ids) {
        LambdaUpdateWrapper<RuleDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(RuleDetail::getId, ids)
                .set(RuleDetail::getActiveState, activeState);

        // 获取更新前的规则
        List<RuleDetail> oldRules = listByIds(ids);
        boolean updated = update(updateWrapper);
        // 发布启用/禁用事件
        RuleChangeEvent.ChangeType changeType = ActiveStateEnum.ENABLE.getValue() == activeState ?
                RuleChangeEvent.ChangeType.ENABLE : RuleChangeEvent.ChangeType.DISABLE;

        oldRules.forEach(oldRule -> {
            RuleDetail newRule = new RuleDetail();
            BeanUtils.copyProperties(oldRule, newRule);
            newRule.setActiveState(activeState);
            newRule.setUpdateTime(new Date());
            publishRuleChangeEvent(newRule, oldRule, changeType);
        });

        return updated;
    }

    @Override
    public List<RuleDetailVO> ruleList(String type) {
        LambdaQueryWrapper<RuleDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuleDetail::getCalType, type);
        List<RuleDetail> ruleDetails = ruleDetailDao.selectList(wrapper);
        return RuleDetailConvertor.INSTANCE.entities2Vos(ruleDetails);
    }


    /**
     * 查询并填充实例属性数据
     *
     * @param convertor 转换器
     * @param table     数据填充对象
     * @param dataCodes 节点实例数字化编码
     */
    private void splitQueryAndFillInsPropData(DigitalTwinConvertor convertor,
                                              Table<String, String, DigitalTwinTreeVO> table,
                                              List<String> dataCodes, String taskName) {

        StopWatch stopWatch = new StopWatch();

        InfoListQueryVo dynamicInfoListQueryVo = InfoListQueryVo.builder()
                .dataCodes(Sets.newHashSet(dataCodes))
                .propGroups(Lists.newArrayList(PropertyGroupEnum.Dynamic.getTag()))
                .pageNumber(1)
                .pageSize(Integer.MAX_VALUE)
                .build();
        stopWatch.start(String.join("-", taskName, "动态属性请求"));
        R<List<InsVO>> dynamicPropR = insService.batchAll(dynamicInfoListQueryVo);
        stopWatch.stop();
        stopWatch.start(String.join("-", taskName, "动态属性结果组装"));
        if (Objects.equals(R.OK().getCode(), dynamicPropR.getCode())) {
            List<InsVO> insVOList = dynamicPropR.getData();
            Table<String, String, List<DigitalTwinTreeVO>> propTable = HashBasedTable.create();
            insVOList.forEach(i -> {
                // 如果有动态属性
                List<DypropInsVO> dynamicProperties = i.getDynamicProperties();
                if (!CollectionUtils.isEmpty(dynamicProperties)) {
                    propTable.put(dynamicProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Dynamic.getTag(),
                            convertor.dypropInsVOs2DigitalTwinTreeVOs(dynamicProperties, PropertyGroupEnum.Dynamic.getTag()));
                }
            });

            // 遍历将动态、静态属性绑定给树对象
            Map<String, Map<String, List<DigitalTwinTreeVO>>> dataCodeMap = propTable.rowMap();
            dataCodeMap.forEach((k, v) -> {
                // 构建动态属性根简化对象
                DigitalTwinTreeVO dynamicPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Dynamic.getDesc())
                        .nodeType(joinPropGroupField(PropertyGroupEnum.Dynamic.getTag(), EMPTY_STR))
                        .dataCode(joinPropGroupField(k, CENTER_BAR)).children(v.get(PropertyGroupEnum.Dynamic.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(dynamicPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, dynamicPropVO);
                    }
                });
            });

        } else {
            log.error("获取动态属性数据失败：{}", dynamicPropR);
            throw new BizException("获取动态属性数据失败");
        }
        stopWatch.stop();

        InfoListQueryVo staticInfoListQueryVo = InfoListQueryVo.builder()
                .dataCodes(Sets.newHashSet(dataCodes))
                .propGroups(Lists.newArrayList(PropertyGroupEnum.Static.getTag()))
                .pageNumber(1)
                .pageSize(Integer.MAX_VALUE)
                .build();
        stopWatch.start(String.join("-", taskName, "静态属性请求"));
        R<List<InsVO>> staticPropR = insService.batchAll(staticInfoListQueryVo);
        stopWatch.stop();
        stopWatch.start(String.join("-", taskName, "静态属性结果组装"));
        if (Objects.equals(R.OK().getCode(), staticPropR.getCode())) {
            List<InsVO> insVOList = staticPropR.getData();
            Table<String, String, List<DigitalTwinTreeVO>> propTable = HashBasedTable.create();
            insVOList.forEach(i -> {
                // 如果有静态属性
                List<StpropInsVO> staticProperties = i.getStaticProperties();
                if (!CollectionUtils.isEmpty(staticProperties)) {
                    propTable.put(staticProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Static.getTag(),
                            convertor.stpropInsVOs2DigitalTwinTreeVOs(staticProperties, PropertyGroupEnum.Static.getTag()));
                }
            });

            // 遍历将动态、静态属性绑定给树对象
            Map<String, Map<String, List<DigitalTwinTreeVO>>> dataCodeMap = propTable.rowMap();
            dataCodeMap.forEach((k, v) -> {
                // 构建静态属性根简化对象
                DigitalTwinTreeVO staticPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Static.getDesc())
                        .nodeType(joinPropGroupField(PropertyGroupEnum.Static.getTag(), EMPTY_STR))
                        .dataCode(joinPropGroupField(k, CENTER_BAR)).children(v.get(PropertyGroupEnum.Static.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(staticPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, staticPropVO);
                    }
                });
            });

        } else {
            log.error("获取静态属性数据失败：{}", staticPropR);
            throw new BizException("获取静态属性数据失败");
        }

        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        log.error("查询动态静态属性数据*******************************");
        log.debug("查询的数字化编码：{}", dataCodes);
    }

    /**
     * 查询并填充实例属性数据
     *
     * @param convertor 转换器
     * @param table     数据填充对象
     * @param dataCodes 节点实例数字化编码
     */
    private void queryAndFillInsPropData(DigitalTwinConvertor convertor,
                                         Table<String, String, DigitalTwinTreeVO> table,
                                         List<String> dataCodes, String taskName) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start(taskName);

        InfoListQueryVo infoListQueryVo = InfoListQueryVo.builder()
                .dataCodes(Sets.newHashSet(dataCodes))
                .propGroups(Lists.newArrayList(PropertyGroupEnum.Dynamic.getTag(), PropertyGroupEnum.Static.getTag()))
                .pageNumber(1)
                .pageSize(Integer.MAX_VALUE)
                .build();

        R<List<InsVO>> propR = insService.batchAll(infoListQueryVo);
        if (Objects.equals(R.OK().getCode(), propR.getCode())) {
            List<InsVO> insVOList = propR.getData();
            Table<String, String, List<DigitalTwinTreeVO>> propTable = HashBasedTable.create();
            insVOList.forEach(i -> {
                // 如果有动态属性
                List<DypropInsVO> dynamicProperties = i.getDynamicProperties();
                if (!CollectionUtils.isEmpty(dynamicProperties)) {
                    propTable.put(dynamicProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Dynamic.getTag(),
                            convertor.dypropInsVOs2DigitalTwinTreeVOs(dynamicProperties, PropertyGroupEnum.Dynamic.getTag()));
                }
                // 如果有静态属性
                List<StpropInsVO> staticProperties = i.getStaticProperties();
                if (!CollectionUtils.isEmpty(staticProperties)) {
                    propTable.put(staticProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Static.getTag(),
                            convertor.stpropInsVOs2DigitalTwinTreeVOs(staticProperties, PropertyGroupEnum.Static.getTag()));
                }
            });

            // 遍历将动态、静态属性绑定给树对象
            Map<String, Map<String, List<DigitalTwinTreeVO>>> dataCodeMap = propTable.rowMap();
            dataCodeMap.forEach((k, v) -> {
                // 构建动态属性根简化对象
                DigitalTwinTreeVO dynamicPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Dynamic.getDesc())
                        .nodeType(PropertyGroupEnum.Dynamic.getTag())
                        .dataCode(k).children(v.get(PropertyGroupEnum.Dynamic.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(dynamicPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, dynamicPropVO);
                    }
                });
                // 构建静态属性根简化对象
                DigitalTwinTreeVO staticPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Static.getDesc())
                        .nodeType(PropertyGroupEnum.Static.getTag())
                        .dataCode(k).children(v.get(PropertyGroupEnum.Static.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(staticPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, staticPropVO);
                    }
                });
            });

        } else {
            log.error("获取属性数据失败：{}", propR);
            throw new BizException("获取属性数据失败");
        }

        stopWatch.stop();
        log.info(stopWatch.prettyPrint());

    }

    /**
     * 分页查询
     *
     * @param page               分页对象
     * @param ruleDetailQueryDTO 查询实体
     * @return 分页数据
     */
    private PageBean<RuleDetailVO> page(PageBean<RuleDetailVO> page, RuleDetailQueryDTO ruleDetailQueryDTO) {

        // 转换器
        RuleDetailConvertor convertor = RuleDetailConvertor.INSTANCE;
        // VO转实体
        RuleDetail ruleDetail = convertor.queryDTO2Entity(ruleDetailQueryDTO);

        // 创建查询对象
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>(ruleDetail);

        List<String> voFieldNameList = ClassUtil.getClassAllFields(RuleDetailVO.class);
        queryWrapper.select(c -> voFieldNameList.contains(c.getProperty()));

        // 查询实体数据
        Page<RuleDetail> entityPage = page(convertor.voPageBean2EntityPage(page), queryWrapper);

        // 实体分页转VO分页
        PageBean<RuleDetailVO> voPageBean = convertor.entityPage2VoPageBean(entityPage);

        return voPageBean;
    }


    @Override
    public List<SelectOptionVO> getIndicatorList() {
        List<RuleDetailVo> allFormulaDetail = ruleDetailDao.getAllFormulaDetail();
        ArrayList<SelectOptionVO> selectOptionVOS = new ArrayList<>();
        for (RuleDetailVo ruleDetailVo : allFormulaDetail) {
            selectOptionVOS.add(SelectOptionVO.builder().value(ruleDetailVo.getDevProperty()).label(ruleDetailVo.getDevName() + "/" + ruleDetailVo.getPropName()).build());
        }
        return selectOptionVOS;
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        List<RuleDetail> rules = listByIds(ids);
        // 发布删除事件
        rules.forEach(rule ->
                publishRuleChangeEvent(rule, null, RuleChangeEvent.ChangeType.DELETE));
        boolean removed = removeByIds(ids);
        return removed;
    }



    /**
     * 获取实例节点映射
     * @param modelCode 模型编码
     * @param instanceMap 实例映射
     * @return 实例节点映射
     */
    private Map<String, Map<String, List<String>>> getInstanceNodeMappings(
            String modelCode,
            Map<String, String> instanceMap) {

        Map<String, Map<String, List<String>>> insNodeMap = new HashMap<>();

        if (CollectionUtils.isEmpty(instanceMap)) {
            return insNodeMap;
        }

        // 并行处理每个实例的子节点
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String insDataCode : instanceMap.keySet()) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 构建请求参数
                    Map<String, String> params = new HashMap<>();
                    params.put("dataCode", insDataCode);
                    params.put("hasSub", "true");
                    params.put("hasEq", "true");
                    params.put("hasPipe", "false");
                    params.put("withSelf", "true");

                    // 调用接口获取子节点
                    String insResult = HttpClientUtil.doGet(dataTwinsUrl + "/common/ins/tree", params);
                    JSONArray insNodeArray = CommonUtils.returnResultHandler(insResult);

                    if (insNodeArray != null) {
                        for (int i = 0; i < insNodeArray.size(); i++) {
                            JSONObject insNode = insNodeArray.getJSONObject(i);
                            List<String> insList = extractDataCodes(insNode);

                            // 线程安全地更新映射
                            synchronized (insNodeMap) {
                                CommonUtils.addDataToMap(insNodeMap, modelCode, insDataCode, insList);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("获取实例子节点失败: insDataCode={}, error={}",
                            insDataCode, e.getMessage(), e);
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return insNodeMap;
    }

    /**
     * 将RuleDetailVo转换为RuleFormulaDetail
     */
    private RuleFormulaDetail convertToRuleFormulaDetail(RuleDetailVo vo) {
        RuleFormulaDetail detail = new RuleFormulaDetail();
        detail.setRuleName(vo.getRuleName());
        detail.setCalType(vo.getCalType());
        detail.setDevCode(vo.getDevCode());
        detail.setDevName(vo.getDevName());
        detail.setDevProperty(vo.getDevProperty());
        detail.setPropName(vo.getPropName());
        detail.setRuleFormula(vo.getRuleFormula());
        return detail;
    }
    /**
     * 批量获取模型实例映射
     */
    private ModelInstanceMappingResult batchGetModelInstanceMappings(String projectCode, List<String> modelCodes) {
        Map<String, Map<String, String>> modelInsMap = new HashMap<>();
        Map<String, String> devPropertyNameMap = new HashMap<>();

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("insDataCode", projectCode);
        requestBody.put("propModelDataCodes", modelCodes);

        // 批量获取实例映射
        String result = HttpClientUtil.postJson(dataTwinsUrl + "/common/prop/ins", requestBody);
        JSONArray array = CommonUtils.returnResultHandler(result);

        if (array != null) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsObject = array.getJSONObject(i);
                String modelCode = jsObject.getString("propModelDataCode");
                String insDataCode = jsObject.getString("insDataCode");
                String propInsDataCode = jsObject.getString("propInsDataCode");
                String insName = jsObject.getString("insName");
                String propInsName = jsObject.getString("propInsName");

                // 存储模型-实例映射
                modelInsMap.computeIfAbsent(modelCode, k -> new HashMap<>())
                        .put(insDataCode, propInsDataCode);

                // 存储名称映射
                devPropertyNameMap.put(insDataCode, insName);
                devPropertyNameMap.put(propInsDataCode, propInsName);
            }
        }

        return new ModelInstanceMappingResult(modelInsMap, devPropertyNameMap);
    }
    private String getInsByModelCode(String url, List<String> modelList, String projectCode) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("insDataCode", projectCode);
        jsonObject.put("propModelDataCodes", modelList);
        return HttpClientUtil.postJson(url, jsonObject);
    }

    /*
     * <AUTHOR>
     * @Description //递归获取该DataCode的子节点信息
     * @Date 16:18 2024/7/13
     * @Param
     * @return
     **/

    private List<String> extractDataCodes(JSONObject jsonObj) {
        List<String> dataList = new ArrayList<>();

        Deque<JSONObject> stack = new ArrayDeque<>();
        stack.push(jsonObj);

        while (!stack.isEmpty()) {
            JSONObject node = stack.pop();
            dataList.add(node.getString("dataCode"));

            if (node.containsKey("children")) {
                JSONArray children = node.getJSONArray("children");
                if (!ObjectUtils.isEmpty(children))
                    for (int i = 0; i < children.size(); i++) {
                        stack.push(children.getJSONObject(i));
                    }
            }
        }

        return dataList;
    }

    private List<FormulaDetail> getInsFormulaList(String modelDevCode, String modelDevProp, Map<String, Map<String, String>> modelInsMap, Map<String, Map<String, List<String>>> insNodeMap, String ruleFormula) {
        List<FormulaDetail> list = new ArrayList<>();
        //获取到计算公式中目标模型对应实例设备和实例属性编码
        Map<String, String> modelDevPropMap = modelInsMap.getOrDefault(modelDevProp, new HashMap<>());
        //遍历
        for (Map.Entry<String, String> entry : modelDevPropMap.entrySet()) {
            String insDevCode = entry.getKey();
            String insDevProp = entry.getValue();
            String subFormula = ruleFormula;
            List<String> insNodeList = insNodeMap.getOrDefault(modelDevProp, new HashMap<>()).getOrDefault(insDevCode, new ArrayList<>());

            List<String> modelList = FormulaUtils.getVarList(subFormula);
            Map<String, List<String>> model2InsList = new HashMap<>();

            for (String model : modelList) {
                Map<String, String> innerMap = modelInsMap.getOrDefault(model, new HashMap<>());

                for (Map.Entry<String, String> innerEntry : innerMap.entrySet()) {
                    if (insNodeList.contains(innerEntry.getKey())) {
                        model2InsList.computeIfAbsent(model, k -> new ArrayList<>()).add(innerEntry.getValue());
                    }
                }
            }

            for (Map.Entry<String, List<String>> entrySet : model2InsList.entrySet()) {
                String modelCode = prefix + entrySet.getKey() + suffix;
                String insListStr = String.join(",", entrySet.getValue().stream().map(code -> prefix + code + suffix).toArray(String[]::new));
                subFormula = subFormula.replace(modelCode, insListStr);
            }

            list.add(new FormulaDetail(insDevCode, insDevProp, subFormula));
        }

        return list;
    }

    private List<AttrObject> getTargetAttrList(RuleDetailInsertDTO ruleDetailInsertDTO) {
        ArrayList<AttrObject> attrObjects = new ArrayList<>();
        String devProperty = ruleDetailInsertDTO.getDevProperty();
        if (ruleDetailInsertDTO.getRuleType() == 1) {
            ArrayList<String> list = new ArrayList<>();
            list.add(devProperty);
            Long projectId = ruleDetailInsertDTO.getProjectId();
            DataProjectVO vo = dataProjectService.getVoById(projectId);
            String projectCode = vo.getProjectCode();
            //说明是模型级指标，需要获取该模型所有的属性列表，将其放入指标集的attr_list中
            String url = dataTwinsUrl + "/common/prop/ins";
            String result = getInsByModelCode(url, list, projectCode);
            JSONArray array = CommonUtils.returnResultHandler(result);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                String propInsDataCode = jsonObject.getString("propInsDataCode");
                String propInsName = jsonObject.getString("propInsName");
                String insName = jsonObject.getString("insName");
                AttrObject attrObject = new AttrObject();
                attrObject.setAttrCode(propInsDataCode);
                attrObject.setAttrName(insName + "/" + propInsName);
                ;
                attrObjects.add(attrObject);

            }

        } else {
            AttrObject attrObject = new AttrObject();
            attrObject.setAttrCode(devProperty);
            attrObject.setAttrName(ruleDetailInsertDTO.getDevName() + "/" + ruleDetailInsertDTO.getPropName());
            ;
            attrObjects.add(attrObject);
        }
        return attrObjects;
    }

    @Override
    public List<Long> getIdsByProjectId(Long projectId) {
        if (projectId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleDetail::getProjectId, projectId)
                   .select(RuleDetail::getId);

        List<RuleDetail> ruleDetails = list(queryWrapper);
        return ruleDetails.stream()
                         .map(RuleDetail::getId)
                         .collect(Collectors.toList());
    }

    @Override
    public RuleDetail getByDevProperty(String devProperty, Long projectId, Long ruleColId) {
        if (!StringUtils.hasText(devProperty) || projectId == null || ruleColId == null) {
            return null;
        }

        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleDetail::getDevProperty, devProperty)
                   .eq(RuleDetail::getProjectId, projectId)
                   .eq(RuleDetail::getRuleColId, ruleColId)
                   .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    /**
     * 获取实例规则详情并处理
     *
     * @param ruleDetailVo 规则详情VO
     */
    public void getInsRuleDetail(RuleDetailVo ruleDetailVo) {
        if (ObjectUtil.isNotEmpty(ruleDetailVo.getRuleFormula())) {
            // 简化实现，暂时注释掉复杂逻辑
            // ruleDetailVo.setRuleFormula(FormulaUtils.convert(ruleDetailVo.getRuleFormula()));
            if (RuleTypeEnum.INS.getValue() == ruleDetailVo.getRuleType()) {
                //将数据存储到mysql的rule_detail_instance表中
                ruleDetailInstanceService.saveToRuleDetailInstance(ruleDetailVo);
            } else if (RuleTypeEnum.MODEL.getValue() == ruleDetailVo.getRuleType()) {
                // TODO: 实现模型级规则的处理逻辑
                log.info("处理模型级规则: {}", ruleDetailVo.getDevProperty());
            }
        }
    }

    /**
     * 批量修改 rule_detail_instance 表中对应的实例规则（逻辑删除）
     * 替代原有的 removeBatchFromRedis 方法
     *
     * @param ruleDetails 规则详情列表
     */
    public void removeBatchFromRuleDetailInstance(List<RuleDetailVo> ruleDetails) {
        if (CollectionUtils.isEmpty(ruleDetails)) {
            log.warn("没有提供任何规则详情，不执行删除操作");
            return;
        }
        try {
            // 1. 收集所有需要删除的规则的 ID
            List<Long> ruleDetailIds = ruleDetails.stream()
                    .map(RuleDetailVo::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (ruleDetailIds.isEmpty()) {
                log.warn("规则详情列表中没有有效的ID，不执行删除操作");
                return;
            }

            // 2. 构建逻辑删除条件
            boolean result = ruleDetailInstanceService.remove(
                    new QueryWrapper<RuleDetailInstance>()
                            .in("rule_detail_id", ruleDetailIds)
            );

            // 3. 记录删除结果
            if (result) {
                log.info("成功逻辑删除 rule_detail_instance 表中关联的规则实例，关联规则ID: {}", ruleDetailIds);
            } else {
                log.warn("尝试逻辑删除 rule_detail_instance 表中的规则实例未完全成功，关联规则ID: {}", ruleDetailIds);
            }
        } catch (Exception e) {
            log.error("逻辑删除 rule_detail_instance 表数据失败，错误: {}", e.getMessage(), e);
            throw new BizException("批量逻辑删除实例规则失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除 rule_detail_instance 表中对应的实例规则（逻辑删除）
     * 为了与tool模块保持一致，提供此方法作为removeBatchFromRuleDetailInstance的别名
     *
     * @param ruleDetails 规则详情列表
     */
    public void deleteBatchFromRuleDetailInstance(List<RuleDetailVo> ruleDetails) {
        removeBatchFromRuleDetailInstance(ruleDetails);
    }

}

