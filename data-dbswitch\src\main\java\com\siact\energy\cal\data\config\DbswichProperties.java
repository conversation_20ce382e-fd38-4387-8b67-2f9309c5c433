// Copyright tang.  All rights reserved.
// https://gitee.com/inrgihc/dbswitch
//
// Use of this source code is governed by a BSD-style license
//
// Author: tang (<EMAIL>)
// Date : 2020/1/2
// Location: beijing , china
/////////////////////////////////////////////////////////////
package com.siact.energy.cal.data.config;


import lombok.Data;
import com.siact.energy.cal.data.entity.SourceDataSourceProperties;
import com.siact.energy.cal.data.entity.TargetDataSourceProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 属性映射配置
 *
 * <AUTHOR>
 */
@Data
public class DbswichProperties {

  private List<SourceDataSourceProperties> source = new ArrayList<>();

  private TargetDataSourceProperties target = new TargetDataSourceProperties();
}
