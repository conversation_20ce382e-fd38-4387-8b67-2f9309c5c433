package com.siact.energy.cal.common.pojo.enums;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.common.pojo.enums
 * @description: 时间单位枚举类
 * @create 2024/10/14 11:05
 */
public enum TimeUnit {
    YEAR("y","Y", null),
    MON<PERSON>("n", "M",YEAR),
    DAY("d", "D",MONTH),
    HOUR("h","H", DAY),
    MIN("m","MIN", HOUR);

    public static final String TIPS = "时间单位类型，Y:年;M:月;D:日;H:小时;MIN:分";
    private final String name;
    private final String oriName;
    private final TimeUnit parent;

    TimeUnit(String name, String oriName,TimeUnit parent) {
        this.name = name;
        this.oriName = oriName;
        this.parent = parent;
    }

    public TimeUnit getParent() {
        return parent;
    }

    public String getOriName() {
        return oriName;
    }
    public String getName() {
        return name;
    }

    public static TimeUnit fromString(String text) {
        if (text != null) {
            for (TimeUnit b : TimeUnit.values()) {
                if (text.equalsIgnoreCase(b.name)) {
                    return b;
                }
            }
        }
        return null;
    }
}
