package com.siact.energy.cal.tool.service.flow.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.tool.dao.flow.SiLiteflowChainMapper;
import com.siact.energy.cal.tool.entity.flow.SiLiteflowChainEntity;
import com.siact.energy.cal.tool.service.flow.ISiLiteflowChainService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Service
public class SiLiteflowChainServiceImpl extends ServiceImpl<SiLiteflowChainMapper, SiLiteflowChainEntity> implements ISiLiteflowChainService {

}
