package com.siact.energy.cal.tool.service.ruleDetail;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleFormulaDetail;

import com.siact.energy.cal.tool.dao.ruleDetail.RuleDetailInstanceDao;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetailInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleDetailInstanceService extends ServiceImpl<RuleDetailInstanceDao, RuleDetailInstance> {

    @Autowired
    private RuleDetailInstanceDao ruleDetailInstanceDao;

    public void saveToRuleDetailInstance(RuleDetailVo ruleDetailVo) {
        // 构建RuleDetailInstance对象
        RuleDetailInstance instance = new RuleDetailInstance();
        instance.setRuleDetailId(ruleDetailVo.getId());
        instance.setRuleName(ruleDetailVo.getRuleName());
        instance.setRuleDes(ruleDetailVo.getRuleDes());
        instance.setRuleType(ruleDetailVo.getRuleType());
        instance.setCalType(ruleDetailVo.getCalType());
        instance.setDevCode(ruleDetailVo.getDevCode());
        instance.setDevName(ruleDetailVo.getDevName());
        instance.setDevProperty(ruleDetailVo.getDevProperty());
        instance.setPropName(ruleDetailVo.getPropName());
        instance.setRuleFormula(ruleDetailVo.getRuleFormula()); // 将List<String>转换为String
        instance.setRuleFormulaShow(StrUtil.join(ruleDetailVo.getRuleFormulaShow(), ""));
        instance.setProjectId(ruleDetailVo.getProjectId());
        instance.setRuleColId(ruleDetailVo.getRuleColId());
        instance.setActiveState(ruleDetailVo.getActiveState());
        instance.setDeleted(ruleDetailVo.getDeleted());

        // 构建查询参数
        Set<String> devProperties = new HashSet<>();
        devProperties.add(ruleDetailVo.getDevProperty());

       // 查询
        List<RuleDetailInstance> existingInstances = ruleDetailInstanceDao.findInstancesByKeyFields(
                ruleDetailVo.getId(),  // ruleDetailId
                ruleDetailVo.getRuleColId(),  // ruleColId
                ruleDetailVo.getRuleType(),  // ruleType
                devProperties  // devProperties
        );

       // 判断是否存在
        RuleDetailInstance existingInstance = existingInstances.isEmpty() ? null : existingInstances.get(0);
        if (existingInstance != null) {
            instance.setId(existingInstance.getId());
            updateById(instance);
        } else {
            save(instance);
        }
    }

    public List<RuleDetailInstance> findInstancesByKeyFields(Long modelRuleDetailId, Long modelRuleColId, Integer sourceRuleType, Set<String> instanceDevProperties) {
        return ruleDetailInstanceDao.findInstancesByKeyFields(modelRuleDetailId, modelRuleColId, sourceRuleType, instanceDevProperties);
    }

    /**
     * 获取计算类型配置映射
     * 根据项目编码和规则集ID获取不同计算类型对应的属性列表
     *
     * @param projectCode 项目编码
     * @param ruleColId 规则集ID
     * @return 计算类型配置映射 Map<计算类型, List<属性编码>>
     */
    public Map<Integer, List<String>> getCalTypeConfigMap(String projectCode, Long ruleColId) {
        if (StrUtil.isBlank(projectCode)) {
            log.warn("项目编码为空，无法获取计算类型配置");
            return new HashMap<>();
        }

        try {
            // 构建查询条件
            QueryWrapper<RuleDetailInstance> queryWrapper = new QueryWrapper<>();

            // 根据项目编码查询（通过dev_code前缀匹配项目）
            queryWrapper.like("dev_code", projectCode);

            // 如果指定了规则集ID，则添加条件
            if (ruleColId != null) {
                queryWrapper.eq("rule_col_id", ruleColId);
            }

            // 只查询激活且未删除的记录
            queryWrapper.eq("active_state", 0); // 已激活
            queryWrapper.eq("deleted", 0); // 未删除

            // 查询所有符合条件的实例
            List<RuleDetailInstance> instances = list(queryWrapper);

            // 按计算类型分组
            Map<Integer, List<String>> calTypeConfigMap = instances.stream()
                    .filter(instance -> instance.getCalType() != null && StrUtil.isNotBlank(instance.getDevProperty()))
                    .collect(Collectors.groupingBy(
                            RuleDetailInstance::getCalType,
                            Collectors.mapping(RuleDetailInstance::getDevProperty, Collectors.toList())
                    ));

            log.debug("项目 {} 规则集 {} 的计算类型配置: {}", projectCode, ruleColId, calTypeConfigMap);
            return calTypeConfigMap;

        } catch (Exception e) {
            log.error("获取计算类型配置失败，项目编码: {}, 规则集ID: {}, 错误: {}", projectCode, ruleColId, e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 从 rule_detail_instance 表中获取公式详情对象
     * 查询优先级：实例级公式 > 模型级公式
     *
     * @param propCode 属性编码
     * @param ruleColId 规则集ID
     * @return 公式详情对象
     */
    public RuleFormulaDetail getFormula(String propCode, Long ruleColId) {
        if (StrUtil.isBlank(propCode)) {
            return null;
        }

        try {
            // 1. 首先查询实例级公式(rule_type=0)
            QueryWrapper<RuleDetailInstance> instanceQuery = new QueryWrapper<RuleDetailInstance>()
                    .eq("dev_property", propCode)
                    .eq("rule_type", 0) // 实例级公式
                    .eq("active_state", 0) // 已激活
                    .eq("deleted", 0); // 未删除

            if (ruleColId != null) {
                instanceQuery.eq("rule_col_id", ruleColId);
            }

            instanceQuery.last("LIMIT 1");

            RuleDetailInstance instance = getOne(instanceQuery);

            // 2. 如果没找到实例级公式，查询模型级公式
            if (instance == null) {
                QueryWrapper<RuleDetailInstance> modelQuery = new QueryWrapper<RuleDetailInstance>()
                        .eq("dev_property", propCode)
                        .eq("rule_type", 1) // 模型级公式
                        .eq("active_state", 0) // 已激活
                        .eq("deleted", 0); // 未删除

                if (ruleColId != null) {
                    modelQuery.eq("rule_col_id", ruleColId);
                }

                modelQuery.last("LIMIT 1");

                instance = getOne(modelQuery);
            }

            // 3. 转换为RuleFormulaDetail对象返回
            if (instance != null) {
                RuleFormulaDetail formulaDetail = new RuleFormulaDetail();
                formulaDetail.setRuleName(instance.getRuleName());
                formulaDetail.setCalType(instance.getCalType());
                formulaDetail.setDevCode(instance.getDevCode());
                formulaDetail.setDevName(instance.getDevName());
                formulaDetail.setDevProperty(instance.getDevProperty());
                formulaDetail.setPropName(instance.getPropName());
                formulaDetail.setRuleFormula(instance.getRuleFormula());

                log.debug("成功从 rule_detail_instance 表获取到属性 {} 的公式详情", propCode);
                return formulaDetail;
            } else {
                log.debug("在 rule_detail_instance 表中未找到属性 {} 的公式详情", propCode);
                return null;
            }
        } catch (Exception e) {
            log.error("从 rule_detail_instance 表查询公式详情失败，属性编码: {}, 规则集ID: {}, 错误: {}", propCode, ruleColId, e.getMessage(), e);
            return null;
        }
    }
}