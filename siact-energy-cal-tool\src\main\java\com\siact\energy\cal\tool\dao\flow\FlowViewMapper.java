package com.siact.energy.cal.tool.dao.flow;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.energy.cal.tool.entity.flow.FlowViewEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface FlowViewMapper extends BaseMapper<FlowViewEntity> {
    void deleteViews(String join);
    List<Map<String, Object>> executeSql(String sql);

    Integer executeSqlCount(String sql);

    void deleteTables(String sql);

    List<String> getAllFields(String tableName);
}
