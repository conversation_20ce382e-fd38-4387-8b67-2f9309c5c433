package com.siact.energy.cal.server.service.flow.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.ApiConfigDto;
import com.siact.energy.cal.common.pojo.vo.flow.ApiConfigVO;
import com.siact.energy.cal.server.convertor.flow.ApiConfigConvertor;
import com.siact.energy.cal.server.dao.flow.ApiConfigMapper;
import com.siact.energy.cal.server.entity.flow.ApiConfigEntity;
import com.siact.energy.cal.server.service.flow.IApiConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Service
public class ApiConfigServiceImpl extends ServiceImpl<ApiConfigMapper, ApiConfigEntity> implements IApiConfigService {

    @Resource
    ApiConfigMapper apiConfigMapper;
    @Override
    public String addModifyApiConfig(ApiConfigDto apiConfigDto) {
        ApiConfigEntity apiConfigEntity = apiConfigMapper.selectById(apiConfigDto.getId());
        if (apiConfigEntity == null){
            apiConfigEntity = new ApiConfigEntity();
            BeanUtil.copyProperties(apiConfigDto, apiConfigEntity);
            apiConfigMapper.insert(apiConfigEntity);
        }else {
            BeanUtil.copyProperties(apiConfigDto, apiConfigEntity);
            apiConfigMapper.updateById(apiConfigEntity);
        }
        return String.valueOf(apiConfigEntity.getId());
    }

    @Override
    public String deleteApiConfig(String id) {
        ApiConfigEntity apiConfigEntity = apiConfigMapper.selectById(id);
        apiConfigEntity.setDeleted(1);
        return apiConfigMapper.updateById(apiConfigEntity) == 1 ? "success" : "fail";
    }

    @Override
    public ApiConfigVO getApiConfig(String id) {
        ApiConfigConvertor instance = ApiConfigConvertor.INSTANCE;
        ApiConfigEntity apiConfigEntity = apiConfigMapper.selectById(id);
        return instance.convertEntityToVo(apiConfigEntity);
    }

    @Override
    public List<ApiConfigVO> getApiConfigList(PageBean<ApiConfigVO> page, ApiConfigDto apiConfigDto) {
        ApiConfigConvertor instance = ApiConfigConvertor.INSTANCE;
        ApiConfigEntity apiConfigEntity = new ApiConfigEntity();
        BeanUtil.copyProperties(apiConfigEntity, apiConfigDto);

        LambdaQueryWrapper<ApiConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ApiConfigEntity::getCreateTime);


        PageBean<ApiConfigEntity> page1 = page(instance.apiConfigEntityToPageBean(page), null);
        PageBean<ApiConfigVO> apiConfigVoPage = instance.pageEntityToPageVo(page1);
        return apiConfigVoPage.getRecords();
    }
}
