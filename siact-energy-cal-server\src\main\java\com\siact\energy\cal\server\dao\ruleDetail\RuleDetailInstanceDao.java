package com.siact.energy.cal.server.dao.ruleDetail;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siact.energy.cal.server.entity.ruleDetail.RuleDetailInstance;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Package com.siact.energy.cal.tool.dao.ruleDetail
 * @description:
 * @create 2025/4/16 14:58
 */
@Repository
public interface RuleDetailInstanceDao extends BaseMapper<RuleDetailInstance> {

    RuleDetailInstance getInstanceByPropCodeAndRuleColId(@Param("devproperty")String devproperty,@Param("ruleColId")Long ruleColId);
    /**
     * 根据四个关键字段查询实例记录
     *
     * @param ruleDetailId 规则详情ID
     * @param ruleColId 规则列ID
     * @param ruleType 规则类型
     * @param devProperties 设备属性列表
     * @return 匹配的实例记录列表
     */
    List<RuleDetailInstance> findInstancesByKeyFields(
            @Param("ruleDetailId") Long ruleDetailId,
            @Param("ruleColId") Long ruleColId,
            @Param("ruleType") Integer ruleType,
            @Param("devProperties") Set<String> devProperties
    );

    /**
     * 根据项目编码和规则列ID查询规则详情实例列表
     */
    List<RuleDetailInstance> selectListByProjectAndCol(
            @Param("projectCode") String projectCode,
            @Param("ruleColId") Long ruleColId,
            @Param("isActive") Integer isActive,
            @Param("isDeleted") Integer isDeleted);

}
