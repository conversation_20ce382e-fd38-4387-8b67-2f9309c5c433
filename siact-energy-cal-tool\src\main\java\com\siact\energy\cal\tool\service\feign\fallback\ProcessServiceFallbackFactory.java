package com.siact.energy.cal.tool.service.feign.fallback;


import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.pojo.dto.flow.ParamDto;
import com.siact.energy.cal.common.pojo.vo.flow.FlowRunResultVO;
import com.siact.energy.cal.tool.service.feign.ProcessFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ProcessServiceFallbackFactory implements FallbackFactory<ProcessFeignClient> {
    
    @Override
    public ProcessFeignClient create(Throwable cause) {
        log.error("调用服务后端公式接口失败", cause);
        
        return new ProcessFeignClient() {
            @Override
            public R<FlowRunResultVO> runFlow(String id, List<ParamDto> paramList) {
                String errorMsg = "无法连接到服务后端运行工作流";
                if (cause != null) {
                    // 根据异常类型提供更具体的错误信息
                    if (cause instanceof feign.RetryableException) {
                        errorMsg += "，服务连接超时或不可达，请检查网络连接";
                    } else {
                        errorMsg += "，错误: " + cause.getMessage();
                    }
                }
                return R.ERROR(errorMsg);

            }
        };
    }
}