<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.siact</groupId>
    <artifactId>siact-energy-cal</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1-SNAPSHOT</version>
    <modules>
        <module>siact-energy-cal-common</module>
        <module>data-dbswitch</module>
        <module>siact-energy-cal-tool</module>
        <module>siact-energy-cal-server</module>
        <module>siact-energy-cal-feign</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud.version>2020.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <spring-boot.version>2.4.2</spring-boot.version>
<!--        <mysql.connector.java.version>5.1.49</mysql.connector.java.version>-->
        <mysql.connector.java.version>8.0.30</mysql.connector.java.version>
        <mybatis.plus.version>3.5.3.1</mybatis.plus.version>
        <knife4j.openapi2.spring.boot.starter.version>4.0.0</knife4j.openapi2.spring.boot.starter.version>
        <knife4j.aggregation.spring.boot.starter>2.0.8</knife4j.aggregation.spring.boot.starter>
        <google.guava.version>21.0</google.guava.version>
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <lombok.mapstruct.binding>0.2.0</lombok.mapstruct.binding>
        <taos-jdbcdriver>2.0.40</taos-jdbcdriver>
        <commons.lang3.veriosn>3.12.0</commons.lang3.veriosn>
        <fastjson2.version>2.0.32</fastjson2.version>
        <mockito.inline.version>3.6.28</mockito.inline.version>
        <siact-energy-cal-common-core.version>0.0.1-SNAPSHOT</siact-energy-cal-common-core.version>
        <siact-energy-cal-common-datasource.version>0.0.1-SNAPSHOT</siact-energy-cal-common-datasource.version>
        <siact-energy-cal-common-doc.version>0.0.1-SNAPSHOT</siact-energy-cal-common-doc.version>
        <siact-energy-cal-common-feign.version>0.0.1-SNAPSHOT</siact-energy-cal-common-feign.version>
        <siact-energy-cal-common-pojo.version>0.0.1-SNAPSHOT</siact-energy-cal-common-pojo.version>
        <siact-energy-cal-common-util.version>0.0.1-SNAPSHOT</siact-energy-cal-common-util.version>
        <siact-sec-api-feign.version>0.0.13-SNAPSHOT</siact-sec-api-feign.version>
        <aviator.version>5.4.1</aviator.version>
        <rocket-api-boot-starter.version>2.4.5.RELEASE</rocket-api-boot-starter.version>
        <taos-jdbcdriver.version>2.0.40</taos-jdbcdriver.version>
        <hutool-all.version>5.8.26</hutool-all.version>
        <fastjson.version>1.2.83</fastjson.version>
        <spring-integration-core.version>5.4.2</spring-integration-core.version>
        <spring-integration-mqtt.version>5.5.5</spring-integration-mqtt.version>
        <mqttv3.version>1.2.0</mqttv3.version>
        <feign-okhttp.version>10.10.1</feign-okhttp.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <!--依赖项配置-->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--添加mysql驱动-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.java.version}</version>
            </dependency>
            <!--添加南大通用驱动-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.7.4</version>
            </dependency>
            <!--   人大金仓数据库驱动         -->
            <dependency>
                <groupId>cn.com.kingbase</groupId>
                <artifactId>kingbase8</artifactId>
                <version>8.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.taosdata.jdbc</groupId>
                <artifactId>taos-jdbcdriver</artifactId>
                <version>${taos-jdbcdriver}</version>
            </dependency>
            <!--  mybatis-plus DAO层工具 https://mp.baomidou.com/  -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <scope>compile</scope>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <scope>compile</scope>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <!-- mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok.mapstruct.binding}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>
            <!-- commons-lang3 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.veriosn}</version>
            </dependency>
            <!-- fastjson2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
                <version>${knife4j.openapi2.spring.boot.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-aggregation-spring-boot-starter</artifactId>
                <version>${knife4j.aggregation.spring.boot.starter}</version>
            </dependency>
            <!-- Mockito 单元测试使用 -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <scope>test</scope>
                <version>${mockito.inline.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.googlecode.aviator/aviator -->
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.alenfive</groupId>
                <artifactId>rocket-api-boot-starter</artifactId>
                <version>${rocket-api-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taosdata.jdbc</groupId>
                <artifactId>taos-jdbcdriver</artifactId>
                <version>${taos-jdbcdriver.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-core</artifactId>
                <version>${siact-energy-cal-common-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-datasource</artifactId>
                <version>${siact-energy-cal-common-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-doc</artifactId>
                <version>${siact-energy-cal-common-doc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-feign</artifactId>
                <version>${siact-energy-cal-common-feign.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-pojo</artifactId>
                <version>${siact-energy-cal-common-pojo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-energy-cal-common-util</artifactId>
                <version>${siact-energy-cal-common-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.siact</groupId>
                <artifactId>siact-sec-api-feign</artifactId>
                <version>${siact-sec-api-feign.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-core</artifactId>
                <version>${spring-integration-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-mqtt</artifactId>
                <version>${spring-integration-mqtt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqttv3.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign-okhttp.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding}</version>
                        </path>
                        <!-- Mapstruct should follow the lombok path(s) -->
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
