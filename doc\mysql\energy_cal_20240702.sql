/*
 Navicat Premium Data Transfer

 Source Server         : **************
 Source Server Type    : MySQL
 Source Server Version : 50624 (5.6.24-log)
 Source Host           : **************:3306
 Source Schema         : energy_cal

 Target Server Type    : MySQL
 Target Server Version : 50624 (5.6.24-log)
 File Encoding         : 65001

 Date: 24/05/2024 18:09:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for data_project
-- ----------------------------
DROP TABLE IF EXISTS `data_project`;
CREATE TABLE `data_project`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目名称',
  `project_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目编码',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_project_code`(`project_code`) USING BTREE COMMENT '项目编码唯一约束'
) ENGINE = InnoDB AUTO_INCREMENT = 1790310767699787786 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for data_source
-- ----------------------------
DROP TABLE IF EXISTS `data_source`;
CREATE TABLE `data_source`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `database_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源名称',
  `database_ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源ip',
  `database_port` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源端口',
  `db` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '超级表名',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态（0-正常/1-中断），描述数据库的连接状态',
  `user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `jdbc_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库连接串',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id,如果是数仓数据库，id为空',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源描述',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793937602480541699 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for data_trans
-- ----------------------------
DROP TABLE IF EXISTS `data_trans`;
CREATE TABLE `data_trans`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id',
  `host` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主机名',
  `port` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '端口',
  `user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `password` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `topic` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1791646070922645507 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据传输表（mqtt）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rule_col
-- ----------------------------
DROP TABLE IF EXISTS `rule_col`;
CREATE TABLE `rule_col`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `rule_col_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标集名称',
  `rule_col_des` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标集描述',
  `active_state` int(255) NULL DEFAULT NULL COMMENT '是否激活（0-激活,1-未激活）',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(255) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793940576980275202 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '指标集表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `rule_detail`;
CREATE TABLE `rule_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `rule_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标名称',
  `rule_des` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '指标描述',
  `rule_type` int(11) NULL DEFAULT NULL COMMENT '指标类型（0-实例级规则，1-模型级规则）',
  `cal_type` int(11) NULL DEFAULT NULL COMMENT '计算类型（0-数值计算，1-逻辑计算，2-正则表达式）',
  `dev_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点（模型）编码',
  `dev_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '节点（模型）名称',
  `dev_property` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性编码，模型编码',
  `prop_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性（模型）名称',
  `rule_formula` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公式表达式',
  `rule_formula_show` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公式表达式',
  `project_id` bigint(20) NULL DEFAULT NULL COMMENT '项目id',
  `rule_col_id` bigint(20) NULL DEFAULT NULL COMMENT '所属规则集id',
  `active_state` int(11) NULL DEFAULT NULL COMMENT '是否激活（0-激活,1-未激活）',
  `creator` bigint(20) NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updater` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(11) NULL DEFAULT NULL COMMENT '删除标志(0-正常，1-已删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1793529158305128451 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '指标详情表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;


-- energy_cal.si_api_config_t definition
DROP TABLE IF EXISTS `si_api_config_t`;
CREATE TABLE `si_api_config_t` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `api_name` varchar(255) DEFAULT NULL COMMENT 'api名称',
    `api_url` varchar(255) DEFAULT NULL COMMENT 'api地址',
    `request_type` varchar(255) DEFAULT NULL COMMENT '请求类型',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
    `deleted` int(1) unsigned zerofill DEFAULT '0' COMMENT '逻辑删除',
    `remark` varchar(255) DEFAULT NULL COMMENT '接口描述',
    `flow_id` bigint(20) DEFAULT NULL COMMENT '绑定组件流id\r\n',
    `table_id` varchar(255) DEFAULT NULL COMMENT '输出表id\r\n',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4;


-- energy_cal.si_component definition
DROP TABLE IF EXISTS `si_component`;

CREATE TABLE `si_component` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '组件id',
    `name` varchar(255) DEFAULT NULL COMMENT '组件名称',
    `type` varchar(255) DEFAULT NULL COMMENT '组件类型(1:输入;2:计算;3:输出;4:数据准备)',
    `des` varchar(255) DEFAULT NULL COMMENT '组件描述',
    KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;


-- energy_cal.si_component_flow definition
DROP TABLE IF EXISTS `si_component_flow`;
CREATE TABLE `si_component_flow` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_name` varchar(255) DEFAULT NULL COMMENT '组件流名称',
    `context` text COMMENT '组件流内容',
    `deleted` int(1) unsigned zerofill DEFAULT '0' COMMENT '是否删除:0未删除,1已删除',
    `create_id` int(11) DEFAULT NULL COMMENT '创建人id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_name` varchar(255) DEFAULT NULL COMMENT '创建人',
    `status` int(1) DEFAULT '0' COMMENT '运行状态:0:未运行,1:运行中,2:成功,3:失败',
    `flow_dec` varchar(2550) DEFAULT NULL COMMENT '组件流描述',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=131 DEFAULT CHARSET=utf8mb4;


-- energy_cal.si_component_relation definition
drop table if exists `si_component_relation`;
CREATE TABLE `si_component_relation` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_id` int(11) DEFAULT NULL COMMENT '组件流id',
    `node_id` varchar(255) DEFAULT NULL COMMENT '节点id',
    `type` varchar(255) DEFAULT NULL COMMENT '组件类型',
    `text` text COMMENT '参数',
    `deleted` int(1) DEFAULT '0' COMMENT '是否删除',
    `formula` varchar(255) DEFAULT NULL COMMENT '公式集合',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=509 DEFAULT CHARSET=utf8mb4;

-- energy_cal.si_flow_tab_view_t definition
drop table if exists `si_flow_tab_view_t`;
CREATE TABLE `si_flow_tab_view_t` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `flow_id` int(11) DEFAULT NULL COMMENT '算子流id',
    `view_name` varchar(255) DEFAULT NULL COMMENT '视图',
    `type` int(1) unsigned zerofill DEFAULT NULL COMMENT '名称类型0:其他,1:视图,2:表',
    `table_name` varchar(255) DEFAULT NULL COMMENT '表名称',
    `com_id` varchar(64) DEFAULT NULL COMMENT '组件id',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `create_id` int(11) DEFAULT NULL COMMENT '创建人id',
    `create_name` varchar(255) DEFAULT NULL COMMENT '创建人名称',
    `task_id` varchar(64) DEFAULT NULL COMMENT '任务id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=693 DEFAULT CHARSET=utf8mb4;


-- energy_cal.xxl_job_group definition
drop table if exists `xxl_job_group`;
CREATE TABLE `xxl_job_group` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `app_name` varchar(64) NOT NULL COMMENT '执行器AppName',
     `title` varchar(12) NOT NULL COMMENT '执行器名称',
     `address_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '执行器地址类型：0=自动注册、1=手动录入',
     `address_list` text COMMENT '执行器地址列表，多地址逗号分隔',
     `update_time` datetime DEFAULT NULL,
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4;


-- energy_cal.xxl_job_info definition
drop table if exists `xxl_job_info`;
CREATE TABLE `xxl_job_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
    `job_desc` varchar(255) NOT NULL,
    `add_time` datetime DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    `author` varchar(64) DEFAULT NULL COMMENT '作者',
    `alarm_email` varchar(255) DEFAULT NULL COMMENT '报警邮件',
    `schedule_type` varchar(50) NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
    `schedule_conf` varchar(128) DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
    `misfire_strategy` varchar(50) NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
    `executor_route_strategy` varchar(50) DEFAULT NULL COMMENT '执行器路由策略',
    `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
    `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
    `executor_block_strategy` varchar(50) DEFAULT NULL COMMENT '阻塞处理策略',
    `executor_timeout` int(11) NOT NULL DEFAULT '0' COMMENT '任务执行超时时间，单位秒',
    `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
    `glue_type` varchar(50) NOT NULL COMMENT 'GLUE类型',
    `glue_source` mediumtext COMMENT 'GLUE源代码',
    `glue_remark` varchar(128) DEFAULT NULL COMMENT 'GLUE备注',
    `glue_updatetime` datetime DEFAULT NULL COMMENT 'GLUE更新时间',
    `child_jobid` varchar(255) DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
    `trigger_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '调度状态：0-停止，1-运行',
    `trigger_last_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '上次调度时间',
    `trigger_next_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '下次调度时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4;

-- energy_cal.xxl_job_lock definition
drop table if exists `xxl_job_lock`;
CREATE TABLE `xxl_job_lock` (
    `lock_name` varchar(50) NOT NULL COMMENT '锁名称',
    PRIMARY KEY (`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO energy_cal.xxl_job_lock
(lock_name)
VALUES('schedule_lock');

-- energy_cal.xxl_job_log definition
drop table if exists `xxl_job_log`;
CREATE TABLE `xxl_job_log` (
   `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
   `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
   `executor_address` varchar(255) DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
   `executor_handler` varchar(255) DEFAULT NULL COMMENT '执行器任务handler',
   `executor_param` varchar(512) DEFAULT NULL COMMENT '执行器任务参数',
   `executor_sharding_param` varchar(20) DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
   `executor_fail_retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '失败重试次数',
   `trigger_time` datetime DEFAULT NULL COMMENT '调度-时间',
   `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
   `trigger_msg` text COMMENT '调度-日志',
   `handle_time` datetime DEFAULT NULL COMMENT '执行-时间',
   `handle_code` int(11) NOT NULL COMMENT '执行-状态',
   `handle_msg` text COMMENT '执行-日志',
   `alarm_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
   PRIMARY KEY (`id`),
   KEY `I_trigger_time` (`trigger_time`),
   KEY `I_handle_code` (`handle_code`)
) ENGINE=InnoDB AUTO_INCREMENT=13452 DEFAULT CHARSET=utf8mb4;

-- energy_cal.xxl_job_log_report definition
drop table if exists `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trigger_day` datetime DEFAULT NULL COMMENT '调度-时间',
  `running_count` int(11) NOT NULL DEFAULT '0' COMMENT '运行中-日志数量',
  `suc_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行成功-日志数量',
  `fail_count` int(11) NOT NULL DEFAULT '0' COMMENT '执行失败-日志数量',
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `i_trigger_day` (`trigger_day`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;

-- energy_cal.xxl_job_logglue definition
drop table if exists `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
   `glue_type` varchar(50) DEFAULT NULL COMMENT 'GLUE类型',
   `glue_source` mediumtext COMMENT 'GLUE源代码',
   `glue_remark` varchar(128) NOT NULL COMMENT 'GLUE备注',
   `add_time` datetime DEFAULT NULL,
   `update_time` datetime DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- energy_cal.xxl_job_registry definition
drop table if exists `xxl_job_registry`;
CREATE TABLE `xxl_job_registry` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `registry_group` varchar(50) NOT NULL,
    `registry_key` varchar(255) NOT NULL,
    `registry_value` varchar(255) NOT NULL,
    `update_time` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=130 DEFAULT CHARSET=utf8mb4;

-- energy_cal.xxl_job_user definition
drop table if exists `xxl_job_user`;
CREATE TABLE `xxl_job_user` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '账号',
    `password` varchar(50) NOT NULL COMMENT '密码',
    `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
    `permission` varchar(255) DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;


INSERT INTO energy_cal.xxl_job_group
(id, app_name, title, address_type, address_list, update_time)
VALUES(7, 'siact', 'siact', 0, 'http://127.0.0.1:9999', '2024-07-01 14:50:52');

INSERT INTO energy_cal.xxl_job_registry
(id, registry_group, registry_key, registry_value, update_time)
VALUES(129, 'EXECUTOR', 'siact', 'http://127.0.0.1:9999', '2024-07-01 16:39:59');


INSERT INTO energy_cal.xxl_job_user
(id, username, password, `role`, permission)
VALUES(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', 1, NULL);

