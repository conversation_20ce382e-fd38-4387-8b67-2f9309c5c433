<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.tool.dao.dataProject.DataProjectDao">

    <resultMap type="com.siact.energy.cal.tool.entity.dataProject.DataProject" id="DataProjectMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="projectCode" column="project_code" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into data_project(project_name, project_code, creator, create_time, updater, update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectName}, #{entity.projectCode}, #{entity.creator}, #{entity.createTime}, #{entity.updater},
            #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按项目编码更新 - 支持多数据库，默认MySQL -->
    <insert id="insertOrUpdateBatch">
        <choose>
            <!-- PostgreSQL数据库（包括南大通用） -->
            <when test="_databaseId == 'postgresql'">
                insert into data_project(project_name, project_code, creator, create_time, updater, update_time, deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.projectName}, #{entity.projectCode}, #{entity.creator}, #{entity.createTime}, #{entity.updater},
                    #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on conflict (project_code) do update set
                project_name = excluded.project_name,
                updater = excluded.updater,
                update_time = excluded.update_time,
                deleted = excluded.deleted
            </when>
            <!-- 默认使用MySQL语法 -->
            <otherwise>
                insert into data_project(project_name, project_code, creator, create_time, updater, update_time, deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.projectName}, #{entity.projectCode}, #{entity.creator}, #{entity.createTime}, #{entity.updater},
                    #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on duplicate key update
                project_name = values(project_name),
                updater = values(updater),
                update_time = values(update_time),
                deleted = values(deleted)
            </otherwise>
        </choose>
    </insert>

    <select id="selectByProjectCode" resultType="com.siact.energy.cal.tool.entity.dataProject.DataProject">
        SELECT id,project_name, project_code,creator,create_time,updater,update_time,deleted FROM data_project
        WHERE project_code = #{projectCode}
    </select>
    <select id="selectListWithDeleted" resultType="com.siact.energy.cal.tool.entity.dataProject.DataProject">
        SELECT
        id,
        project_code,
        project_name,
        create_time,
        update_time,
        deleted
        FROM
        data_project
        <!-- 不添加任何删除状态过滤条件，查询所有记录 -->
    </select>

</mapper>

