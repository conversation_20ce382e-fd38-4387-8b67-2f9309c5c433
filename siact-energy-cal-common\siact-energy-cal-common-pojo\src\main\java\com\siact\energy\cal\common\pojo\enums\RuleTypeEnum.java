package com.siact.energy.cal.common.pojo.enums;

import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;

import java.util.ArrayList;
import java.util.List;

/**
 * RuleTypeEnum
 *
 * <AUTHOR>
 * @since 2024-05-21 10:09:00
 */
public enum RuleTypeEnum {

    /**
     * 实例级规则
     */
    INS(0, "实例级规则"),

    /**
     * 模型级规则
     */
    MODEL(1, "模型级规则");

    public static final String TIPS = "指标类型，0:实例级规则,1:模型级规则";

    private final Integer value;

    private final String description;

    RuleTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取下拉列表数据
     *
     * @return 列表数据
     */
    public static List<SelectOptionVO> list() {

        List<SelectOptionVO> list = new ArrayList();

        for(RuleTypeEnum enumObj : values()) {
            list.add(SelectOptionVO.builder().label(enumObj.getDescription()).value(enumObj.getValue()).build());
        }

        return list;
    }

}
