package com.siact.energy.cal.server.controller.edu;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.siact.edu.common.pojo.dto.DriverProjectDTO;
import com.siact.edu.common.pojo.dto.calc.CalcDriverDetailDTO;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.server.service.edu.EDURuleDetailService;
import com.siact.energy.cal.server.service.funLib.FunLibService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * EDU指标详情表(RuleDetail)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Api(tags = {"EDU指标详情表"})
@ApiSort(50)
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/edu/rule/detail")
public class EDURuleDetailController {

    private final EDURuleDetailService eDURuleDetailService;

    /**
     * 新增数据
     *
     * @param driverProjectDTO 实体对象
     * @return 新增结果
     */
    @ApiOperation(value = "EDU加载数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "driverProjectDTO", value = "实体对象", dataType = "EDU指标详情DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 30, ignoreParameters = {"id"})
    @PostMapping("/load")
    public R<Boolean> load(@RequestBody @Validated DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        Boolean save = eDURuleDetailService.eduLoad(driverProjectDTO);

        return R.OK(save);
    }

    /**
     * EDU更新数据
     *
     * @param driverProjectDTO 实体对象
     * @return 更新结果
     */
    @ApiOperation(value = "EDU更新数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "driverProjectDTO", value = "实体对象", dataType = "EDU指标详情DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 40)
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody @Validated DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        Boolean update = eDURuleDetailService.eduUpdate(driverProjectDTO);
        return R.OK(update);
    }

    /**
     * EDU删除数据
     *
     * @param driverProjectDTO 实体对象
     * @return 删除结果
     */
    @ApiOperation(value = "EDU删除数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "driverProjectDTO", value = "实体对象", dataType = "EDU指标详情DTO", required = true)
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功")
    })
    @ApiOperationSupport(order = 50)
    @DeleteMapping("/delete")
    public R<Boolean> delete(@RequestBody @Validated DriverProjectDTO<CalcDriverDetailDTO> driverProjectDTO) {
        Boolean deleted = eDURuleDetailService.eduDelete(driverProjectDTO);
        return R.OK(deleted);
    }


}

